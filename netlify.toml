# Netlify Configuration for RapidDoc AI
# Critical: Prevents MIME type errors by ensuring assets are served correctly

[build]
  command = "npm run build"
  publish = "dist"

[dev]
  command = "npm run dev"
  port = 4028

# CRITICAL: Asset handling - this prevents double asset path issues
# Order matters: more specific rules first

# Handle legacy double asset paths (redirects to single path)
[[redirects]]
  from = "/assets/assets/*"
  to = "/assets/:splat"
  status = 301
  force = true

# CRITICAL: Ensure assets are served directly, not caught by SPA fallback
[[redirects]]
  from = "/assets/*"
  to = "/assets/:splat"
  status = 200
  force = true

# Favicon and manifest
[[redirects]]
  from = "/favicon.ico"
  to = "/favicon.ico"
  status = 200

[[redirects]]
  from = "/manifest.json"
  to = "/manifest.json"
  status = 200

# Static files
[[redirects]]
  from = "/robots.txt"
  to = "/robots.txt"
  status = 200

[[redirects]]
  from = "/sw.js"
  to = "/sw.js"
  status = 200



# SPA fallback - MUST be last (catch-all for routes)
# CRITICAL: Exclude assets and API routes from SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["visitor"]}
  force = false

# CRITICAL MIME Type Headers - Prevents "text/html" errors
[[headers]]
  for = "/assets/*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/assets/*.mjs"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/assets/*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"
    Cache-Control = "public, max-age=31536000, immutable"
    X-Content-Type-Options = "nosniff"

# All other assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Service Worker (should not be cached aggressively)
[[headers]]
  for = "/sw.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "no-cache"

# Main HTML and static files
[[headers]]
  for = "/*"
  [headers.values]
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    Referrer-Policy = "no-referrer-when-downgrade"
    X-XSS-Protection = "0"
    # Enhanced CSP with comprehensive image sources for templates
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https://images.unsplash.com https://source.unsplash.com https://*.unsplash.com https://unsplash.com https://picsum.photos https://via.placeholder.com https://placehold.co https://raw.githubusercontent.com https://*.githubusercontent.com https://*.supabase.co https://supabase.com; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com data:; connect-src 'self' https://*.supabase.co https://api.openai.com https://generativelanguage.googleapis.com https://api.replicate.com https://api.unsplash.com https://images.unsplash.com https://source.unsplash.com https://www.google-analytics.com https://fonts.googleapis.com https://fonts.gstatic.com; worker-src 'self' blob:; frame-ancestors 'none'; frame-src 'self'; object-src 'none'; base-uri 'self'"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), payment=()"

# Force HTTPS redirects
[[redirects]]
  from = "http://rapiddoc.netlify.app/*"
  to = "https://rapiddoc.netlify.app/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.rapiddoc.netlify.app/*"
  to = "https://rapiddoc.netlify.app/:splat"
  status = 301
  force = true
