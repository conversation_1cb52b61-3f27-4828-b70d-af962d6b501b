{"name": "RapidDoc_ai", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@google/generative-ai": "^0.24.1", "@stripe/stripe-js": "^7.8.0", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/forms": "^0.5.7", "@testing-library/jest-dom": "^5.15.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@tiptap/core": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "docx": "^9.5.1", "dompurify": "^3.2.6", "dotenv": "^16.6.1", "image-size": "^2.0.2", "lucide-react": "^0.484.0", "mammoth": "^1.9.1", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "6.0.2", "rehype-parse": "^9.0.1", "rehype-stringify": "^10.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "stripe": "^18.4.0", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5", "unsplash-js": "^7.0.19"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build --sourcemap", "build:fresh": "rm -rf dist && npm run build", "serve": "vite preview", "test": "jest", "test:watch": "jest --watch", "db:setup": "node scripts/setup-database.js", "db:test": "node scripts/test-connection.js", "admin:deploy": "node scripts/deploy-admin-system.js", "admin:verify": "node scripts/deploy-admin-system.js --verify", "admin:create-super": "node scripts/create-super-admin.js", "dev:edge-functions": "node dev-server/edge-function-server.js", "deploy:force-cache-bust": "./scripts/force-cache-bust.sh", "netlify:cache-bust": "./scripts/netlify-cache-bust.sh"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "10.4.2", "eslint": "^9.31.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "postcss": "8.4.8", "rollup": "^4.46.3", "tailwindcss": "3.4.6", "vite": "^6.3.5", "vite-tsconfig-paths": "3.6.0"}}