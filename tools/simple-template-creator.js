#!/usr/bin/env node

/**
 * Simplified Template Creator for debugging
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { createClient } from "@supabase/supabase-js";
import sizeOf from "image-size";
import dotenv from "dotenv";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, "../.env") });

console.log("🚀 Simple Template Creator Starting...");

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

// Business Contact Template - Extended Overlay Types Configuration
const BUSINESS_CONTACT_TEXT_OVERLAYS = {
  overlays: [
    {
      id: "title",
      type: "text",
      placeholder: "{{title}}",
      position: { x: 220, y: 242, width: 760, height: 314 },
      styling: {
        fontSize: 70,
        fontFamily: "Open Sans",
        fontWeight: "bold",
        color: "#7d4f30",
        textAlign: "center",
        lineHeight: 1.3,
        maxLines: 2,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "company_name",
      type: "company",
      placeholder: "{{company_name}}",
      position: { x: 629, y: 154, width: 367, height: 45 },
      styling: {
        fontSize: 28,
        fontFamily: "Montserrat",
        fontWeight: "normal",
        color: "#02393b",
        textAlign: "right",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "author",
      type: "text",
      placeholder: "{{user_name}}",
      position: { x: 168, y: 1319, width: 268, height: 32 },
      styling: {
        fontSize: 21,
        fontFamily: "Montserrat",
        fontWeight: "normal",
        color: "#02393b",
        textAlign: "left",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "date",
      type: "text",
      placeholder: "{{month}} {{year}}",
      position: { x: 418, y: 592, width: 385, height: 50 },
      styling: {
        fontSize: 32,
        fontFamily: "Open Sans",
        fontWeight: "bold",
        color: "#02393b",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "company_logo",
      type: "logo",
      placeholder: "{{logo}}",
      position: { x: 1002, y: 133, width: 65, height: 77 },
      styling: {
        opacity: 1.0,
        borderRadius: 0,
      },
    },
    {
      id: "desc",
      type: "text",
      placeholder: "{{desc}}",
      position: { x: 143, y: 784, width: 913, height: 100 },
      styling: {
        fontSize: 24,
        fontFamily: "Montserrat",
        fontWeight: "normal",
        color: "#ffffff",
        textAlign: "center",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
    {
      id: "for",
      type: "text",
      placeholder: "{{for}}",
      position: { x: 761, y: 1319, width: 293, height: 32 },
      styling: {
        fontSize: 21,
        fontFamily: "Montserrat",
        fontWeight: "normal",
        color: "#02393b",
        textAlign: "right",
        lineHeight: 1.2,
        maxLines: 1,
        overflow: "ellipsis",
        verticalAlign: "center",
      },
    },
  ],
};

// Run the functions
async function main() {
  console.log("🚀 Creating multiple templates...\n");

  try {
    // Create Business Contact Template
    await createBusinessContactTemplate();

    console.log("\n✅ All templates created successfully!");
  } catch (error) {
    console.error("\n❌ Template creation failed:", error.message);
    process.exit(1);
  }
}

async function createBusinessContactTemplate() {
  try {
    console.log(
      "📋 Creating business-contact template with extended overlay types..."
    );

    const imagePath = "./cover/academic-1.png";
    const name = "Academic Template";
    const category = "academic";
    const description =
      "Academic template";
    const tags = ["academic"];

    // Check if image exists
    console.log("📁 Checking image file...");
    if (!fs.existsSync(imagePath)) {
      throw new Error(`Image file not found: ${imagePath}`);
    }
    console.log("✅ Image file found");

    // Get image dimensions
    console.log("📏 Getting image dimensions...");
    const buffer = fs.readFileSync(imagePath);
    const dimensions = sizeOf(buffer);
    console.log(
      `✅ Image dimensions: ${dimensions.width}x${dimensions.height}`
    );

    // Generate template ID
    const templateId = `academic-${Date.now().toString(36)}`;
    console.log(`🆔 Generated template ID: ${templateId}`);

    // Upload image to storage
    console.log("📤 Uploading image to Supabase storage...");
    const fileName = `${templateId}.png`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from("template-backgrounds")
      .upload(fileName, buffer, {
        contentType: "image/png",
        upsert: true,
      });

    if (uploadError) {
      console.error("❌ Upload error:", uploadError);
      throw uploadError;
    }

    console.log("✅ Image uploaded successfully");

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from("template-backgrounds").getPublicUrl(fileName);

    // Create template data
    const templateData = {
      id: templateId,
      name,
      description,
      category,
      tags,
      background_image_url: publicUrl,
      background_image_width: dimensions.width,
      background_image_height: dimensions.height,
      text_overlays: BUSINESS_CONTACT_TEXT_OVERLAYS,
      supported_formats: ["pdf", "png", "jpg"],
      status: "active",
      is_premium: false,
    };

    console.log("💾 Inserting template into database...");

    // Insert into database
    const { data: createdTemplate, error: dbError } = await supabase
      .from("cover_templates")
      .insert([templateData])
      .select()
      .single();

    if (dbError) {
      console.error("❌ Database error:", dbError);
      throw dbError;
    }

    console.log("\n🎉 Business Contact Template created successfully!");
    console.log(`   ID: ${createdTemplate.id}`);
    console.log(`   Name: ${createdTemplate.name}`);
    console.log(`   Category: ${createdTemplate.category}`);
    console.log(`   Background: ${createdTemplate.background_image_url}`);
    console.log(`   Extended Overlay Types: company, website, email, phone`);

    return createdTemplate;
  } catch (error) {
    console.error(
      "\n❌ Failed to create business contact template:",
      error.message
    );
    console.error("Full error:", error);
    throw error;
  }
}

main();
