# Simple Extended Overlay Types Implementation

## Summary
Add support for 4 new overlay types: `company`, `website`, `email`, `phone`. 

**Key insight:** The database and data mapping already exist! No database changes needed.

## Database Fields (Already Exist)
```sql
-- user_profiles table already has:
- organization TEXT    -> {{company_name}}
- website TEXT        -> {{website}}  
- phone TEXT          -> {{phone_number}}
- email TEXT          -> {{email_address}}
- full_name TEXT      -> {{user_name}}
```

## ProfileDataService (Already Works)
```javascript
const fieldMap = {
  'company_name': 'organization', 
  'website': 'website',
  'phone_number': 'phone',
  'email_address': 'email'
};
```

## New Overlay Types
Just add these types to templates:

```json
{
  "id": "company_name",
  "type": "company", 
  "placeholder": "{{company_name}}",
  "position": {"x": 50, "y": 100, "width": 300, "height": 40},
  "styling": {"fontSize": 20, "color": "#2c3e50"}
}
```

## Frontend Changes Needed

### 1. Admin Interface (`TextOverlayEditor.jsx`)
Add new overlay type buttons:
```jsx
<Button onClick={() => addNewOverlay('company')}>Company</Button>
<Button onClick={() => addNewOverlay('website')}>Website</Button>
<Button onClick={() => addNewOverlay('email')}>Email</Button>
<Button onClick={() => addNewOverlay('phone')}>Phone</Button>
```

### 2. Type Selector
Update the type dropdown:
```jsx
<option value="company">Company Name</option>
<option value="website">Website</option>
<option value="email">Email</option>
<option value="phone">Phone</option>
```

### 3. User Input Form
The existing form already handles these fields since they're in user_profiles.

## Implementation Steps
1. ✅ Update template creator with new overlay types
2. Update admin interface overlay type selector
3. Update frontend rendering to handle new types
4. Test with existing user profile data

That's it! No database migrations, no complex validation, no fancy styling panels.
