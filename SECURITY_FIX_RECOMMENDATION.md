// Recommended Security Fix for SecuritySection.jsx
// Add this before the supabase.auth.updateUser call:

try {
  // First verify the current password by attempting to sign in
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: user.email, // Get from auth context
    password: passwordForm.currentPassword
  });

  if (signInError) {
    setPasswordError('Current password is incorrect');
    return;
  }

  // If sign-in successful, proceed with password update
  const { error } = await supabase.auth.updateUser({
    password: passwordForm.newPassword
  });

  // ... rest of the existing code
} catch (err) {
  setPasswordError('Failed to verify current password');
}
