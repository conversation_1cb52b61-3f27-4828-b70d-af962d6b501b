# Template Customization Testing Guide

## 📋 Manual Testing Checklist

### Phase 1: Basic Functionality Test

1. **Start the app**:
   ```bash
   npm start
   ```

2. **Navigate to template selection**:
   - Create or open a document
   - Go to template selection phase
   - Select any template with text overlays

3. **Test customizations**:
   - [ ] Change text color on any overlay
   - [ ] Modify font size
   - [ ] Adjust text position
   - [ ] Change text content

4. **Test localStorage persistence**:
   - [ ] Make customizations
   - [ ] Reload the page (F5 or Cmd+R)
   - [ ] Navigate back to the same template
   - [ ] ✅ Customizations should still be there (localStorage working)
   - [ ] Close and reopen the browser tab
   - [ ] ✅ Customizations should still be there (localStorage persistence)
   - [ ] Close and reopen the entire browser
   - [ ] ✅ Customizations should still be there (cross-session persistence)

## 🔍 Debug Commands

### Browser Console Debug Commands:

```javascript
// Check localStorage data
Object.keys(localStorage).filter(key => key.includes('overlay-customizations')).forEach(key => {
  console.log(key, JSON.parse(localStorage.getItem(key)));
});

// Clear all template customizations
Object.keys(localStorage).filter(key => key.includes('overlay-customizations')).forEach(key => {
  localStorage.removeItem(key);
});
```

## 🐛 Common Issues & Solutions

### Issue: Customizations not persisting
**Solution**:
1. Check browser console for errors
2. Verify localStorage is enabled
3. Make sure `enablePersistence: true` is set in hook options

### Issue: Customizations persist too long
**Solution**:
This is normal localStorage behavior - customizations persist until:
- User manually clears browser data
- Application clears them programmatically
- Browser storage quota is exceeded

## ✅ Success Criteria

- [ ] Customizations survive page reloads within the same session
- [ ] Customizations are cleared when browser tab is closed (expected behavior)
- [ ] No console errors during normal operation
- [ ] Multiple templates can have independent customizations

## 📊 Performance Benchmarks

- **localStorage save**: < 1ms
- **localStorage load**: < 1ms
- **Cross-session persistence**: ✅ Enabled
