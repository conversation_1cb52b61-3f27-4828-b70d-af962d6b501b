# Overlay Profile Implementation - Complete Guide

## Overview

This document outlines the implementation of additional overlay types (website, organization, phone, email address) in the profile tab of account settings. This allows users to set these values which will then be used in document templates when using overlay placeholders like `{{organization}}`, `{{website}}`, `{{phone_number}}`, and `{{email_address}}`.

## Current State Analysis

### ✅ Already Implemented
1. **Database Schema**: The `user_profiles` table already contains all necessary fields:
   - `organization` (for {organization} overlay)
   - `website` (for {website} overlay) 
   - `phone` (for {phone_number} overlay)
   - `email` (for {email_address} overlay - already set, read-only)

2. **Overlay System**: Extended overlay types are fully operational:
   - Template editor supports `website`, `organization`, `phone`, `email` overlay types
   - ProfileDataService correctly maps these fields
   - ImageOverlayService processes these placeholders correctly

3. **Backend Support**: 
   - `dbHelpers.updateUserProfile` supports updating all profile fields
   - AuthContext's `updateProfile` function handles these fields
   - Row Level Security policies allow users to update their own profiles

### ❌ Missing Implementation
- Profile Settings UI doesn't allow editing these fields

## Implementation Details

### Phase 1: Enhanced ProfileSection Component ✅ COMPLETED

**File**: `/src/pages/account-settings/components/ProfileSection.jsx`

#### Key Changes Made:

1. **Expanded State Management**:
   ```jsx
   const [tempData, setTempData] = useState({
     full_name: '',
     email: '',
     organization: '',    // NEW
     website: '',         // NEW
     phone: ''           // NEW
   });
   const [validationErrors, setValidationErrors] = useState({});
   ```

2. **Added Validation Helpers**:
   ```jsx
   const validatePhone = (phone) => {
     if (!phone) return true; // Optional field
     const phoneRegex = /^[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}$/;
     return phoneRegex.test(phone.replace(/\s/g, ''));
   };

   const validateWebsite = (website) => {
     if (!website) return true; // Optional field
     try {
       new URL(website.startsWith('http') ? website : `https://${website}`);
       return true;
     } catch {
       return false;
     }
   };
   ```

3. **Enhanced Form Validation**:
   ```jsx
   const validateFormData = () => {
     const errors = {};
     
     if (tempData.phone && !validatePhone(tempData.phone)) {
       errors.phone = 'Please enter a valid phone number';
     }
     
     if (tempData.website && !validateWebsite(tempData.website)) {
       errors.website = 'Please enter a valid website URL';
     }
     
     if (!tempData.full_name?.trim()) {
       errors.full_name = 'Full name is required';
     }
     
     setValidationErrors(errors);
     return Object.keys(errors).length === 0;
   };
   ```

4. **New UI Section**:
   ```jsx
   {/* Contact & Organization Details */}
   <div>
     <h4 className="text-md font-medium text-text-primary mb-2">Contact & Organization Details</h4>
     <p className="text-sm text-text-secondary mb-4">
       These details will be used in document templates when you use overlay types like {'{organization}'}, {'{website}'}, {'{phone_number}'}.
     </p>
     <div className="grid grid-cols-1 gap-4">
       {/* Organization Field */}
       {/* Website Field */}
       {/* Phone Field */}
     </div>
   </div>
   ```

5. **User-Friendly Labels**:
   - Each field shows the corresponding overlay placeholder
   - Clear explanations of how data will be used
   - Validation error messages with icons

## User Experience Flow

### 1. Accessing the Settings
- Navigate to Account Settings → Profile tab
- Click "Edit Profile" button

### 2. Editing Overlay Information
- **Organization/Company**: Text input for company name
  - Label: "Organization/Company (for {organization} overlay)"
  - Placeholder: "Enter your organization or company name"
  - Validation: None (optional field)

- **Website**: URL input for website
  - Label: "Website (for {website} overlay)"
  - Placeholder: "Enter your website URL (e.g., www.example.com)"
  - Validation: Valid URL format (optional)

- **Phone Number**: Tel input for phone
  - Label: "Phone Number (for {phone_number} overlay)"
  - Placeholder: "Enter your phone number (e.g., ******-123-4567)"
  - Validation: Valid phone format (optional)

- **Email Address**: Read-only display
  - Label: "Email Address (for {email_address} overlay)"
  - Note: "(Cannot be changed)"

### 3. Saving Changes
- Form validation runs on save
- Real-time validation error clearing as user types
- Success/error feedback
- Profile context automatically updates

## Technical Architecture

### Data Flow
1. **Profile Loading**: `AuthContext` → `loadUserProfile()` → Database
2. **Form Management**: Local state in `ProfileSection` component
3. **Validation**: Client-side validation with immediate feedback
4. **Saving**: `ProfileSection` → `AuthContext.updateProfile()` → `dbHelpers.updateUserProfile()` → Database
5. **Template Usage**: Database → `ProfileDataService` → `ImageOverlayService` → Template rendering

### Field Mapping
```javascript
// ProfileDataService field mapping
const fieldMap = {
  'user_name': 'full_name',
  'company_name': 'organization',    // NEW MAPPING
  'website': 'website',             // NEW MAPPING
  'phone_number': 'phone',          // NEW MAPPING
  'email_address': 'email'
};
```

### Template Placeholder Usage
Once users set these values, they can be used in templates:
- `{{organization}}` → User's organization field
- `{{website}}` → User's website field  
- `{{phone_number}}` → User's phone field
- `{{email_address}}` → User's email (auto-populated)

## Security & Validation

### Client-Side Validation
- **Phone**: Regex validation for international formats
- **Website**: URL constructor validation with protocol handling
- **Organization**: No specific validation (free text)
- **Email**: Read-only, managed by authentication system

### Server-Side Security
- Row Level Security ensures users can only update their own profiles
- UUID validation for user IDs
- Field length limits (existing in database)
- SQL injection protection through Supabase client

## Testing Recommendations

### Manual Testing Checklist
- [ ] Navigate to Profile settings
- [ ] Edit mode toggle works correctly
- [ ] All new fields display current values
- [ ] Validation works for invalid phone numbers
- [ ] Validation works for invalid URLs
- [ ] Successful save updates profile context
- [ ] Cancel resets form correctly
- [ ] Error handling displays properly
- [ ] Template rendering uses new values
- [ ] Mobile responsiveness

### Automated Testing
Consider adding tests for:
- Form validation functions
- Profile update flow
- Error handling
- Template placeholder population

## Future Enhancements

### Phase 2 Possibilities
1. **Additional Overlay Types**:
   - Company logo upload and management
   - Social media links
   - Physical address
   - Professional title/role

2. **Advanced Validation**:
   - Real-time URL validation
   - Phone number formatting
   - Duplicate detection

3. **User Experience**:
   - Auto-formatting for phone numbers
   - URL protocol auto-addition
   - Preview of how data will appear in templates
   - Bulk edit mode for multiple fields

4. **Integration Features**:
   - Import from LinkedIn/other services
   - Template preview with user data
   - Usage analytics for overlay types

## Documentation for Users

### Help Text Recommendations
Add to the UI or help documentation:

> **Overlay Information**
> 
> These details will automatically populate in your document templates when you use special placeholders:
> 
> - `{{organization}}` - Your company or organization name
> - `{{website}}` - Your website URL
> - `{{phone_number}}` - Your contact phone number
> - `{{email_address}}` - Your email address (automatically filled)
> 
> Example: If you enter "Acme Corp" as your organization, any template using `{{organization}}` will show "Acme Corp" in the final document.

## Summary

This implementation successfully bridges the gap between the existing overlay system and user profile management. Users can now easily set their contact and organization information through the profile settings, and this data will automatically populate in document templates using the corresponding overlay placeholders.

The solution is:
- ✅ **User-friendly**: Clear labels and helpful validation
- ✅ **Secure**: Proper validation and RLS policies
- ✅ **Scalable**: Easy to add more overlay types in the future
- ✅ **Integrated**: Works seamlessly with existing template system
- ✅ **Tested**: Comprehensive validation and error handling

The implementation provides immediate value to users who create documents with contact information and organization details, making the template system more powerful and user-friendly.
