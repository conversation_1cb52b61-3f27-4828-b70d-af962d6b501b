#!/bin/bash

# RapidDoc AI - Rebuild and Deploy Script
# Fixes MIME type issues and ensures clean deployment

echo "🔄 Starting clean rebuild for Netlify deployment..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist build .vite

# Clear npm cache
echo "📦 Clearing npm cache..."
npm cache clean --force

# Reinstall dependencies to ensure consistency
echo "📥 Reinstalling dependencies..."
rm -rf node_modules package-lock.json
npm install

# Build for production
echo "🏗️  Building for production..."
npm run build

# Verify build output
if [ -d "dist" ]; then
  echo "✅ Build successful! Contents of dist directory:"
  ls -la dist/
  echo ""
  echo "📁 Assets directory:"
  ls -la dist/assets/ | head -10
  echo ""
  echo "🚀 Ready for Netlify deployment!"
  echo ""
  echo "Next steps:"
  echo "1. Push these changes to your git repository"
  echo "2. Netlify will automatically rebuild and deploy"
  echo "3. Check that assets are served with correct MIME types"
else
  echo "❌ Build failed! No dist directory found."
  exit 1
fi

# Check for common issues
echo "🔍 Checking for potential issues..."

# Check for double asset paths
if find dist -name "assets" -type d | grep -q "assets/assets"; then
  echo "⚠️  Warning: Double asset paths detected - this should be handled by redirects"
else
  echo "✅ No double asset paths found"
fi

# Check for exportService chunk
if find dist/assets -name "*exportService*" -type f; then
  echo "✅ exportService chunk found in assets"
else
  echo "⚠️  exportService chunk not found - this might cause the original error"
fi

echo "🎉 Deployment preparation complete!"