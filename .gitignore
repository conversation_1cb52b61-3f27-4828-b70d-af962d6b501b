.DS_Store
node_modules
.idea
package-lock.json
/build
dist/
.vscode/
.idea/
vite.config.*s.*

# Environment variables - NEVER commit these
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Local Netlify folder
.netlify

# Production analysis reports
production-analysis-*.json

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# OS generated files
Thumbs.db
