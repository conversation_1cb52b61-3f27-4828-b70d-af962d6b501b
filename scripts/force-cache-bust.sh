#!/bin/bash

# Force Cache Bust and Redeploy Script for Netlify
# This script helps clear cached assets and forces a fresh deployment

echo "🚀 Force Cache Bust and Redeploy"
echo "=================================="

# 1. Clean old builds
echo "🧹 Cleaning old build artifacts..."
rm -rf dist/
rm -rf node_modules/.vite/

# 2. Clear npm cache
echo "🧹 Clearing npm cache..."
npm cache clean --force

# 3. Reinstall dependencies (ensures fresh packages)
echo "📦 Reinstalling dependencies..."
npm ci

# 4. Build with cache busting
echo "🏗️ Building with fresh assets..."
VITE_BUILD_ID=$(date +%s) npm run build

# 5. Generate deployment info
echo "📝 Generating deployment info..."
echo "{
  \"build_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
  \"build_id\": \"$(date +%s)\",
  \"git_commit\": \"$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')\",
  \"cache_bust\": true
}" > dist/build-info.json

echo "✅ Build completed successfully!"
echo ""
echo "🔧 Deployment checklist:"
echo "  1. Commit and push changes to trigger Netlify deployment"
echo "  2. Clear browser cache (Ctrl+F5 / Cmd+Shift+R)"
echo "  3. Check Netlify deploy logs for any issues"
echo "  4. Test assets loading at: https://your-site.netlify.app/_debug"
echo ""
echo "📊 Build info saved to dist/build-info.json"