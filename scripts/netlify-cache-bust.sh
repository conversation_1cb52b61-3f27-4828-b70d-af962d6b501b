#!/bin/bash

# Netlify CLI Cache Bust Script
# Forces complete cache clearing and fresh deployment

echo "🚀 Netlify CLI Cache Bust & Deploy"
echo "=================================="

# Check if netlify CLI is installed
if ! command -v netlify &> /dev/null; then
    echo "❌ Netlify CLI not found. Install with: npm install -g netlify-cli"
    exit 1
fi

# Check if we're linked to a Netlify site
if [ ! -f ".netlify/state.json" ]; then
    echo "❌ Not linked to Netlify site. Run: netlify link"
    exit 1
fi

# 1. Clear all local caches
echo "🧹 Clearing local caches..."
rm -rf dist/
rm -rf node_modules/.vite/
rm -rf .netlify/cache/

# 2. Clear npm cache
echo "📦 Clearing npm cache..."
npm cache clean --force

# 3. Fresh install
echo "📥 Fresh npm install..."
npm ci

# 4. Build with timestamp for cache busting
echo "🏗️ Building with cache bust timestamp..."
BUILD_ID=$(date +%s)
VITE_BUILD_ID=$BUILD_ID npm run build

# 5. Add build metadata
echo "📝 Adding build metadata..."
echo "{
  \"build_time\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
  \"build_id\": \"$BUILD_ID\",
  \"git_commit\": \"$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')\",
  \"cache_bust\": true,
  \"netlify_cli\": true
}" > dist/build-info.json

# 6. Purge Netlify CDN cache
echo "🔥 Purging Netlify CDN cache..."
netlify api purgeCache --data='{}' 2>/dev/null || echo "⚠️ CDN cache purge may have failed (not critical)"

# 7. Deploy with cache-busting message
echo "🚀 Deploying to production..."
DEPLOY_MESSAGE="Force cache bust: $BUILD_ID at $(date)"
netlify deploy --prod --dir=dist --message="$DEPLOY_MESSAGE"

# 8. Get deployment URL
SITE_URL=$(netlify status --json | grep -o '"url":"[^"]*' | cut -d'"' -f4)

echo ""
echo "✅ Cache bust deployment complete!"
echo ""
echo "🔗 Site URL: $SITE_URL"
echo "🔍 Test debug page: $SITE_URL/_debug"
echo "📊 Build ID: $BUILD_ID"
echo ""
echo "🧪 Next steps:"
echo "  1. Wait 1-2 minutes for global CDN propagation"
echo "  2. Clear your browser cache (Ctrl+F5 / Cmd+Shift+R)"
echo "  3. Test the site functionality"
echo "  4. Check debug page for asset loading status"