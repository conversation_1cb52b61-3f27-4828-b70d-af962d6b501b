# AI Prompt Logging Guide

## Overview
Comprehensive console logging has been added to track all AI prompts sent to Google Gemini for document generation. This helps with debugging, monitoring AI requests, and understanding what prompts are being generated.

## Logging Categories

### 🎯 Document Generation Flow
All major AI operations now log both the prompt being sent and the response received:

1. **🚀 [DOCUMENT GENERATION]** - Overall document generation start
2. **🗃️ [OUTLINE PROMPT]** - Document outline generation prompt
3. **✅ [OUTLINE RESPONSE]** - Document outline generation response
4. **📈 [CHAPTER PROMPT]** - Individual chapter content prompt
5. **✅ [CHAPTER RESPONSE]** - Individual chapter content response
6. **📝 [INTRODUCTION PROMPT]** - Introduction section prompt
7. **✅ [INTRODUCTION RESPONSE]** - Introduction section response
8. **🎯 [CONCLUSION PROMPT]** - Conclusion section prompt
9. **✅ [CONCLUSION RESPONSE]** - Conclusion section response

### 🎨 Prompt Factory
10. **🎨 [PROMPT FACTORY]** - Document type-specific prompt creation

### 🔧 Supporting Operations
11. **🎯 [SUB-NICHE PROMPT]** - Sub-niche generation prompt
12. **✅ [SUB-NICHE RESPONSE]** - Sub-niche generation response
13. **📝 [TITLE PROMPT]** - Title generation prompt
14. **✅ [TITLE RESPONSE]** - Title generation response

### ✏️ Text Editing Operations
15. **✏️ [TEXT EDIT PROMPT]** - Text editing operations (rewrite, expand, reduce, fix-grammar)
16. **🤖 [AI PROMPT]** - Final prompt sent to Gemini (from callDocGenerateAI)
17. **✅ [AI RESPONSE]** - Final response received from Gemini

## What's Logged

### For Each Prompt:
- **Operation Type** - What type of content is being generated
- **Document Context** - Document type, topic, audience, tone, etc.
- **Prompt Length** - Character count of the prompt
- **Full Prompt** - Complete prompt text sent to AI
- **Timestamp** - When the request was made

### For Each Response:
- **Response Length** - Character count of the response
- **Response Preview** - First 200 characters of the response
- **Full Response** - Complete AI response text
- **Timestamp** - When the response was received

## How to Use

### Development/Debugging
1. Open browser developer tools (F12)
2. Navigate to Console tab
3. Generate any document content
4. View detailed AI prompt and response logs

### Example Log Output
```javascript
🚀 [DOCUMENT GENERATION] Starting AI-powered content generation: {
  documentId: "doc-123",
  documentType: "academic",
  topic: "Climate Change Research",
  audience: "researchers",
  // ... more context
}

🎨 [PROMPT FACTORY] Generated outline prompt for academic (research-paper): {
  documentType: "academic",
  promptType: "outline",
  promptLength: 1247,
  prompt: "You are an expert academic researcher...",
  // ... full prompt details
}

🤖 [AI PROMPT] Sending prompt to Gemini: {
  promptLength: 1247,
  prompt: "You are an expert academic researcher...",
  options: { temperature: 0.7, maxTokens: 1000 }
}

✅ [AI RESPONSE] Received response from Gemini: {
  responseLength: 892,
  responsePreview: "{\n  \"title\": \"Climate Change Research Methods\"...",
  fullResponse: "{ full JSON response... }"
}
```

## Benefits

1. **Debugging** - See exactly what prompts are sent to AI
2. **Optimization** - Identify prompt patterns that work best
3. **Monitoring** - Track AI usage and response quality
4. **Development** - Understand document generation flow
5. **Troubleshooting** - Debug AI generation issues

## Files Modified

- `src/services/aiService.js` - Added logging to all AI functions
- `src/services/documentTypePrompts.js` - Added logging to prompt factory

## Log Filtering

To filter logs in browser console:
- `[DOCUMENT GENERATION]` - Overall process
- `[PROMPT]` - All prompts being sent
- `[RESPONSE]` - All AI responses
- `[CHAPTER]` - Chapter-specific operations
- `[TITLE]` - Title generation
- `[SUB-NICHE]` - Sub-niche generation
- `[OUTLINE]` - Document outline
- `[INTRODUCTION]` - Introduction section
- `[CONCLUSION]` - Conclusion section