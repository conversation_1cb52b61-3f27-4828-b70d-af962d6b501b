import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

// https://vitejs.dev/config/
export default defineConfig({
  // This changes the out put dir from dist to build
  // comment this out if that isn't relevant for your project
  base: "/",
  build: {
    outDir: "dist",
    sourcemap: "hidden",
    chunkSizeWarningLimit: 2000,
    assetsDir: "assets",
    // Force fresh builds
    emptyOutDir: true,
    rollupOptions: {
      external: [],
      output: {
        // Ensure consistent asset naming without double paths
        assetFileNames: (assetInfo) => {
          const name = assetInfo.name || 'asset';
          const hash = '[hash]';
          const ext = '[extname]';
          return `assets/${name}-${hash}${ext}`;
        },
        chunkFileNames: (chunkInfo) => {
          const name = chunkInfo.name || 'chunk';
          const hash = '[hash]';
          return `assets/${name}-${hash}.js`;
        },
        entryFileNames: `assets/[name]-[hash].js`,
        manualChunks: {
          vendor: [
            "react",
            "react-dom",
            "react-router-dom",
            "@supabase/supabase-js",
            "docx",
            "mammoth",
            "dompurify",
          ],
          editor: [
            "@tiptap/core",
            "@tiptap/react",
            "@tiptap/starter-kit",
            "@tiptap/extension-image",
          ],
          "content-processing": [
            "unified",
            "remark-parse",
            "remark-rehype",
            "rehype-stringify",
          ],
        },
      },
    },
  },
  plugins: [tsconfigPaths(), react()],
  server: {
    port: "4028",
    host: "0.0.0.0",
    strictPort: true,
    allowedHosts: [".amazonaws.com"],
  },
  resolve: {
    alias: {
      // Help resolve unsplash-js if needed
    },
  },
  optimizeDeps: {
    include: [
      "docx",
      "mammoth",
      "dompurify",
      "@tiptap/core",
      "@tiptap/react",
      "@tiptap/starter-kit",
    ],
  },
  define: {
    global: "globalThis",
  },
  // Ensure modules are properly processed
  esbuild: {
    logOverride: { "this-is-undefined-in-esm": "silent" },
    drop: ["console", "debugger"],
  },
  // Improve module loading
  experimental: {
    renderBuiltUrl(filename) {
      return filename;
    },
  },
});
