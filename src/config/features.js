/**
 * Feature Flags Configuration
 * Centralized configuration for enabling/disabling application features
 * 
 * This allows for safe feature toggling without code removal
 */

export const FEATURE_FLAGS = {
  // Document Import Features
  PDF_IMPORT: false,        // Temporarily disabled - PDF import functionality
  DOCX_IMPORT: true,        // DOCX import remains enabled
  
  // Other features can be added here as needed
  // TEMPLATE_LIBRARY: true,
  // AI_SUGGESTIONS: true,
  // COLLABORATION: false,
};

/**
 * Check if a feature is enabled
 * @param {string} featureName - Name of the feature flag
 * @returns {boolean} Whether the feature is enabled
 */
export const isFeatureEnabled = (featureName) => {
  return FEATURE_FLAGS[featureName] === true;
};

/**
 * Get all enabled features
 * @returns {string[]} Array of enabled feature names
 */
export const getEnabledFeatures = () => {
  return Object.keys(FEATURE_FLAGS).filter(feature => FEATURE_FLAGS[feature]);
};

/**
 * Feature flag constants for easy import
 */
export const FEATURES = {
  PDF_IMPORT: 'PDF_IMPORT',
  DOCX_IMPORT: 'DOCX_IMPORT',
};