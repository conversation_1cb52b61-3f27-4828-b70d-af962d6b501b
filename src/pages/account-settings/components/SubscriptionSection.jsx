import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useSubscriptionGuard } from '../../../hooks/useSubscriptionGuard.jsx';
import subscriptionService from '../../../services/subscriptionService';
import UsageDashboard from '../../../components/subscription/UsageDashboard';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';
import { prodLogger } from '../../../utils/prodLogger.js';

const SubscriptionSection = () => {
  const { user, profile } = useAuth();
  const {
    subscription,
    usage,
    loading: subscriptionLoading,
    openCustomerPortal,
    cancelSubscription,
    refresh
  } = useSubscriptionGuard();
  const [billingHistory, setBillingHistory] = useState([]);
  const [subscriptionHistory, setSubscriptionHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelling, setCancelling] = useState(false);

  // Load billing and subscription history
  useEffect(() => {
    const loadHistory = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        const [billing, subscriptions] = await Promise.all([
          subscriptionService.getBillingHistory(user.id),
          subscriptionService.getSubscriptionHistory(user.id)
        ]);
        setBillingHistory(billing || []);
        setSubscriptionHistory(subscriptions || []);
      } catch (error) {
        prodLogger.error('Failed to load history:', error);
        setBillingHistory([]);
        setSubscriptionHistory([]);
      } finally {
        setLoading(false);
      }
    };

    loadHistory();
  }, [user?.id]);
  const handleCancelSubscription = async () => {
    try {
      setCancelling(true);
      await cancelSubscription(false); // Cancel at period end
      setShowCancelModal(false);
      refresh();
    } catch (error) {
      prodLogger.error('Cancellation failed:', error);
    } finally {
      setCancelling(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatAmount = (amountInCents) => {
    return `$${(amountInCents / 100).toFixed(2)}`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'cancelled':
      case 'expired':
        return 'text-red-600 bg-red-100';
      case 'trial':
        return 'text-blue-600 bg-blue-100';
      case 'past_due':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (subscriptionLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-surface rounded-lg border border-border p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-border rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-border rounded"></div>
              <div className="h-4 bg-border rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Usage Dashboard */}
      <UsageDashboard />

      {/* Subscription Management */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-text-primary">Subscription Management</h3>
          <div className="flex space-x-2 sm:space-x-3">
            <Button 
              variant="outline" 
              onClick={openCustomerPortal}
              size="sm"
              className="whitespace-nowrap text-sm"
            >
              <Icon name="CreditCard" size={16} className="mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Billing Portal</span>
              <span className="sm:hidden">Portal</span>
            </Button>
            {subscription?.is_active && subscription?.subscription_tier !== 'free' && (
              <Button
                variant="ghost"
                onClick={() => setShowCancelModal(true)}
                size="sm"
                className="text-red-600 hover:text-red-700 whitespace-nowrap text-sm"
              >
                <span className="hidden sm:inline">Cancel Subscription</span>
                <span className="sm:hidden">Cancel</span>
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-text-primary mb-3">Current Subscription</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-text-secondary">Plan:</span>
                <span className="font-medium text-text-primary capitalize">
                  {subscription?.subscription_tier || 'Free'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription?.subscription_status)}`}>
                  {subscription?.subscription_status || 'Free'}
                </span>
              </div>
              {subscription?.current_period_end && (
                <div className="flex justify-between">
                  <span className="text-text-secondary">Next billing:</span>
                  <span className="font-medium text-text-primary">
                    {formatDate(subscription.current_period_end)}
                  </span>
                </div>
              )}
              {subscription?.cancel_at_period_end && (
                <div className="flex justify-between">
                  <span className="text-text-secondary">Cancels on:</span>
                  <span className="font-medium text-red-600">
                    {formatDate(subscription.current_period_end)}
                  </span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-medium text-text-primary mb-3">Quick Actions</h4>
            <div className="space-y-2">
              <Button 
                variant="outline" 
                size="sm"
                className="w-full justify-start text-sm" 
                onClick={() => window.open('/pricing', '_blank')}
              >
                <Icon name="ArrowUp" size={16} className="mr-2" />
                Upgrade Plan
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                className="w-full justify-start text-sm" 
                onClick={openCustomerPortal}
              >
                <Icon name="Settings" size={16} className="mr-2" />
                Update Payment Method
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                className="w-full justify-start text-sm" 
                onClick={openCustomerPortal}
              >
                <Icon name="Download" size={16} className="mr-2" />
                Download Invoices
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Billing History */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-6">Billing History</h3>

        {loading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse flex items-center space-x-4 p-4 border border-border rounded-lg">
                <div className="w-10 h-10 bg-border rounded-lg"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-border rounded w-1/4"></div>
                  <div className="h-3 bg-border rounded w-1/2"></div>
                </div>
                <div className="h-4 bg-border rounded w-16"></div>
              </div>
            ))}
          </div>
        ) : billingHistory.length > 0 ? (
          <div className="space-y-3">
            {billingHistory.map((invoice) => (
              <div key={invoice.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 border border-border rounded-lg gap-3">
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon name="Receipt" size={16} className="text-primary" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 mb-1">
                      <span className="text-sm font-medium text-text-primary truncate">
                        {invoice.stripe_invoice_id}
                      </span>
                      <span className={`text-xs px-2 py-0.5 rounded-full self-start sm:self-auto ${getStatusColor(invoice.status)}`}>
                        {invoice.status}
                      </span>
                    </div>
                    <p className="text-xs text-text-secondary">
                      {formatDate(invoice.created_at)} • {invoice.billing_reason}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between sm:justify-end sm:text-right sm:flex-col sm:items-end gap-2">
                  <div className="text-sm font-medium text-text-primary">
                    {formatAmount(invoice.amount_paid)}
                  </div>
                  {invoice.invoice_pdf_url && (
                    <a
                      href={invoice.invoice_pdf_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-primary hover:underline whitespace-nowrap"
                    >
                      Download PDF
                    </a>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon name="Receipt" size={24} className="text-text-secondary mx-auto mb-2" />
            <p className="text-text-secondary">No billing history available</p>
          </div>
        )}
      </div>

      {/* Cancel Subscription Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-surface rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-text-primary mb-4">
              Cancel Subscription
            </h3>
            <p className="text-text-secondary mb-6">
              Are you sure you want to cancel your subscription? You'll continue to have access
              until the end of your current billing period.
            </p>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <Button
                variant="primary"
                onClick={handleCancelSubscription}
                loading={cancelling}
                size="sm"
                className="flex-1 bg-red-600 hover:bg-red-700 text-sm"
              >
                Cancel Subscription
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(false)}
                size="sm"
                className="flex-1 text-sm"
              >
                Keep Subscription
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionSection;