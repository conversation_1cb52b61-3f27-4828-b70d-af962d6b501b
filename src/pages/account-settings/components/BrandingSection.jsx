import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';
import LogoUpload from '../../../components/ui/LogoUpload';
import { useAuth } from '../../../contexts/AuthContext';
import { getUserLogos, setDefaultLogo, deleteLogo, uploadLogo, fixUserLogoConstraints } from '../../../services/logoService';
import { prodLogger } from '../../../utils/prodLogger.js';

/**
 * Branding Section - Central Logo Management
 * Provides centralized logo management in user account settings
 */
const BrandingSection = () => {
  const { user, profile, updateProfile } = useAuth();
  const [logos, setLogos] = useState([]);
  const [defaultLogo, setDefaultLogoState] = useState(null);
  const [loading, setLoading] = useState(false);
  const [uploadMode, setUploadMode] = useState(false);
  const [selectedLogos, setSelectedLogos] = useState(new Set());
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Load user's logos on component mount
  useEffect(() => {
    if (user?.id) {
      loadUserLogos();
    }
  }, [user?.id]);

  const loadUserLogos = async () => {
    try {
      setLoading(true);
      setError('');
      
      const result = await getUserLogos(user.id, { 
        activeOnly: true, 
        includeDefault: true,
        unifiedGrid: true  // Use new unified grid format
      });
      
      if (result.success) {
        setLogos(result.logos);
        setDefaultLogoState(result.defaultLogo);
        
        // Debug: Log the first few logos to see their structure
        if (result.logos.length > 0) {
          prodLogger.debug('🔍 First logo structure:', {
            firstLogo: result.logos[0],
            hasIsDefault: 'isDefault' in result.logos[0],
            isDefaultValue: result.logos[0].isDefault
          });
        }
      } else {
        setError(result.error || 'Failed to load logos');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to load user logos:', err);
      setError('Failed to load logos');
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = async (file, validationResult) => {
    if (!validationResult.success) {
      setError(validationResult.error);
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const uploadResult = await uploadLogo(file, user.id, {
        name: file.name.replace(/\.[^/.]+$/, ''),
        description: 'Logo uploaded from account settings'
      });
      
      if (uploadResult.success) {
        let message = 'Logo uploaded successfully!';
        if (uploadResult.fixedConstraints > 0) {
          message += ` (Fixed ${uploadResult.fixedConstraints} database issues)`;
        }
        if (uploadResult.setAsFirstDefault) {
          message += ' Set as default logo.';
        }
        
        setSuccessMessage(message);
        setUploadMode(false);
        await loadUserLogos(); // Refresh the list
        
        // Clear success message after 4 seconds (longer for detailed messages)
        setTimeout(() => setSuccessMessage(''), 4000);
      } else {
        setError(uploadResult.error || 'Failed to upload logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to upload logo:', err);
      
      // Check if it's a constraint error and provide helpful message
      if (err.message && err.message.includes('Database constraint error')) {
        setError('Database constraint error detected. This usually indicates corrupted data. Please try again, and if the issue persists, contact support.');
      } else {
        setError('Failed to upload logo. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSetDefault = async (logoId) => {
    try {
      setLoading(true);
      setError('');
      
      const result = await setDefaultLogo(logoId, user.id);
      
      if (result.success) {
        setSuccessMessage('Default logo updated!');
        await loadUserLogos(); // Refresh to show new default
        
        // Update user profile context
        await updateProfile({ default_logo_id: logoId });
        
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        setError(result.error || 'Failed to set default logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to set default logo:', err);
      setError('Failed to set default logo');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLogo = async (logoId) => {
    if (!confirm('Are you sure you want to delete this logo? This action cannot be undone.')) {
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const result = await deleteLogo(logoId, user.id);
      
      if (result.success) {
        setSuccessMessage('Logo deleted successfully!');
        await loadUserLogos(); // Refresh the list
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        setError(result.error || 'Failed to delete logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to delete logo:', err);
      setError('Failed to delete logo');
    } finally {
      setLoading(false);
    }
  };

  const handleFixConstraints = async () => {
    try {
      setLoading(true);
      setError('');
      
      const result = await fixUserLogoConstraints(user.id);
      
      if (result.success) {
        if (result.fixed > 0) {
          setSuccessMessage(`Fixed ${result.fixed} database constraint issues!`);
          await loadUserLogos(); // Refresh the list
        } else {
          setSuccessMessage('No constraint issues found - your data is healthy!');
        }
        setTimeout(() => setSuccessMessage(''), 4000);
      } else {
        setError(result.message || 'Failed to fix constraints');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to fix constraints:', err);
      setError('Failed to fix database constraints');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedLogos.size === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedLogos.size} logo(s)? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const deletePromises = Array.from(selectedLogos).map(logoId => 
        deleteLogo(logoId, user.id)
      );
      
      await Promise.all(deletePromises);
      
      setSuccessMessage(`${selectedLogos.size} logo(s) deleted successfully!`);
      setSelectedLogos(new Set());
      await loadUserLogos();
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      prodLogger.error('❌ Failed to delete logos:', err);
      setError('Failed to delete selected logos');
    } finally {
      setLoading(false);
    }
  };

  const toggleLogoSelection = (logoId) => {
    const newSelection = new Set(selectedLogos);
    if (newSelection.has(logoId)) {
      newSelection.delete(logoId);
    } else {
      newSelection.add(logoId);
    }
    setSelectedLogos(newSelection);
  };


  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-text-primary mb-2">Brand Management</h2>
        <p className="text-sm text-text-secondary">
          Manage your logos and branding assets. Your default logo (marked with a ⭐) is automatically applied to new documents and appears first.
        </p>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-600">
          {error}
        </div>
      )}
      
      {successMessage && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg text-sm text-green-600">
          {successMessage}
        </div>
      )}

      {/* Action Bar */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Button
            variant="primary"
            onClick={() => setUploadMode(true)}
            iconName="Plus"
            iconPosition="left"
            disabled={loading}
          >
            Upload Logo
          </Button>
          
          {selectedLogos.size > 0 && (
            <Button
              variant="ghost"
              onClick={handleBulkDelete}
              iconName="Trash2"
              iconPosition="left"
              disabled={loading}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Delete Selected ({selectedLogos.size})
            </Button>
          )}
        </div>
        
      </div>

      {/* Upload Interface */}
      {uploadMode && (
        <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">Upload New Logo</h3>
            <Button
              variant="ghost"
              onClick={() => setUploadMode(false)}
              iconName="X"
              size="sm"
            >
              Cancel
            </Button>
          </div>
          
          <LogoUpload
            onFileSelect={handleLogoUpload}
            disabled={loading}
            showPreview={true}
          />
        </div>
      )}

      {/* Logo Grid */}
      {loading && logos.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <Icon name="Loader2" size={32} className="animate-spin text-gray-400" />
        </div>
      ) : logos.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {logos.map((logo, index) => {
            return (
            <div
              key={logo.id}
              className={`relative border-2 rounded-xl p-4 transition-all hover:shadow-md ${
                selectedLogos.has(logo.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${logo.isDefault ? 'ring-2 ring-blue-200' : ''}`}
            >
              {/* Selection Checkbox */}
              <div className="absolute top-3 left-3 z-10">
                <input
                  type="checkbox"
                  checked={selectedLogos.has(logo.id)}
                  onChange={() => toggleLogoSelection(logo.id)}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
              </div>

              {/* Default Badge with Star Icon */}
              {logo.isDefault && (
                <div className="absolute top-3 right-3 flex items-center gap-1 bg-blue-500 text-white text-xs px-2 py-1 rounded-full z-10 shadow-md">
                  <span className="font-medium">⭐ Default</span>
                </div>
              )}

              {/* Square Logo Preview Container */}
              <div className="aspect-square mb-3 mt-2 bg-white rounded-lg border border-gray-100 p-3 flex items-center justify-center">
                <img
                  src={logo.previewUrl}
                  alt={logo.name}
                  className="max-w-full max-h-full object-contain"
                />
              </div>

              {/* Logo Info */}
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 truncate" title={logo.name}>
                  {logo.name}
                </h4>
              </div>

              {/* Actions */}
              <div className="mt-3 flex gap-2">
                {!logo.isDefault && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSetDefault(logo.id)}
                    disabled={loading}
                    className="flex-1 text-xs"
                    iconName="Star"
                    iconPosition="left"
                  >
                    Set Default
                  </Button>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteLogo(logo.id)}
                  disabled={loading}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  iconName="Trash2"
                />
              </div>
            </div>
            )
          })}
        </div>
      ) : (null)}
    </div>
  );
};

export default BrandingSection;
