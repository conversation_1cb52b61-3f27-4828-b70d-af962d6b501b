import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import BrandingSection from './BrandingSection';

// Validation helpers
const validatePhone = (phone) => {
  if (!phone) return true; // Optional field
  const phoneRegex = /^[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

const validateWebsite = (website) => {
  if (!website) return true; // Optional field
  try {
    new URL(website.startsWith('http') ? website : `https://${website}`);
    return true;
  } catch {
    return false;
  }
};

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const ProfileSection = () => {
  const { user, profile, updateProfile, loading, error } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [tempData, setTempData] = useState({
    full_name: '',
    email: '',
    organization: '',
    website: '',
    phone: ''
  });
  const [updateError, setUpdateError] = useState('');
  const [validationErrors, setValidationErrors] = useState({});

  // Validate form data
  const validateFormData = () => {
    const errors = {};
    
    if (tempData.phone && !validatePhone(tempData.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }
    
    if (tempData.website && !validateWebsite(tempData.website)) {
      errors.website = 'Please enter a valid website URL';
    }
    
    if (!tempData.full_name?.trim()) {
      errors.full_name = 'Full name is required';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Initialize profile data from auth context - Enhanced with overlay fields
  useEffect(() => {
    if (profile) {
      const profileData = {
        full_name: profile.full_name || '',
        email: user?.email || '',
        organization: profile.organization || '',
        website: profile.website || '',
        phone: profile.phone || ''
      };
      setTempData(profileData);
    }
  }, [profile, user]);

  const handleEdit = () => {
    setIsEditing(true);
    setUpdateError('');
  };

  const handleSave = async () => {
    if (!validateFormData()) {
      return;
    }

    try {
      setUpdateError('');
      const result = await updateProfile(tempData);

      if (result.success) {
        setIsEditing(false);
        setValidationErrors({});
      } else {
        setUpdateError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      setUpdateError('An error occurred while updating profile');
    }
  };

  const handleCancel = () => {
    // Reset to current profile data - Enhanced with overlay fields
    if (profile) {
      const profileData = {
        full_name: profile.full_name || '',
        email: user?.email || '',
        organization: profile.organization || '',
        website: profile.website || '',
        phone: profile.phone || ''
      };
      setTempData(profileData);
    }
    setIsEditing(false);
    setUpdateError('');
    setValidationErrors({});
  };

  const handleInputChange = (field, value) => {
    setTempData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear validation errors when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };



  return (
    <div className="space-y-6">
      {/* Profile Information Section */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <div className="flex items-start justify-between gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-text-primary">Profile Information</h2>
            </div>
            <div className="flex-shrink-0">
              {!isEditing ? (
                <Button
                  variant="outline"
                  onClick={handleEdit}
                  iconName="Edit2"
                  iconPosition="left"
                  size="sm"
                  className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
                >
                  <span className="hidden sm:inline">Edit Profile</span>
                  <span className="sm:hidden">Edit</span>
                </Button>
              ) : (
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    onClick={handleCancel}
                    disabled={loading}
                    size="sm"
                    className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleSave}
                    iconName="Save"
                    iconPosition="left"
                    loading={loading}
                    disabled={loading}
                    size="sm"
                    className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
                  >
                    <span className="hidden sm:inline">
                      {loading ? 'Saving...' : 'Save Changes'}
                    </span>
                    <span className="sm:hidden">
                      {loading ? 'Saving...' : 'Save'}
                    </span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
        <p className="text-sm text-text-secondary">
          Manage your personal information and preferences. Click "Edit Profile" to make changes.
        </p>

        {/* Error Display */}
        {(error || updateError) && (
          <div className="mb-6 p-4 bg-error/10 border border-error/20 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="AlertCircle" size={16} color="var(--color-error)" />
              <p className="text-sm text-error">{error || updateError}</p>
            </div>
          </div>
        )}

        {/* MVP: Simplified profile layout without photo upload */}
        <div className="space-y-6 mt-6">
        {/* Basic Information */}
        <div>
          <h4 className="text-md font-medium text-text-primary mb-4">Basic Information</h4>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Full Name</label>
              {isEditing ? (
                <div>
                  <Input
                    type="text"
                    value={tempData.full_name || ''}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    placeholder="Enter your full name"
                    disabled={loading}
                    className={validationErrors.full_name ? 'border-error' : ''}
                  />
                  {validationErrors.full_name && (
                    <p className="mt-1 text-sm text-error flex items-center">
                      <Icon name="AlertCircle" size={12} className="mr-1" />
                      {validationErrors.full_name}
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-sm text-text-secondary bg-gray-50 border border-gray-200 p-3 rounded-lg">
                  {profile?.full_name || 'Not provided'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Overlay Information for Templates */}
        <div>
          <h4 className="text-md font-medium text-text-primary mb-2">Contact & Organization Details</h4>
          <p className="text-sm text-text-secondary mb-4">
            These details will be used in document templates.
          </p>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Organization/Company
              </label>
              {isEditing ? (
                <Input
                  type="text"
                  value={tempData.organization || ''}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
                  placeholder="Enter your organization or company name"
                  disabled={loading}
                />
              ) : (
                <div className="text-sm text-text-secondary bg-gray-50 border border-gray-200 p-3 rounded-lg">
                  {profile?.organization || 'Not provided'}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Website
              </label>
              {isEditing ? (
                <div>
                  <Input
                    type="url"
                    value={tempData.website || ''}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="Enter your website URL (e.g., www.example.com)"
                    disabled={loading}
                    className={validationErrors.website ? 'border-error' : ''}
                  />
                  {validationErrors.website && (
                    <p className="mt-1 text-sm text-error flex items-center">
                      <Icon name="AlertCircle" size={12} className="mr-1" />
                      {validationErrors.website}
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-sm text-text-secondary bg-gray-50 border border-gray-200 p-3 rounded-lg">
                  {profile?.website || 'Not provided'}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Phone Number
              </label>
              {isEditing ? (
                <div>
                  <Input
                    type="tel"
                    value={tempData.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter your phone number (e.g., ******-123-4567)"
                    disabled={loading}
                    className={validationErrors.phone ? 'border-error' : ''}
                  />
                  {validationErrors.phone && (
                    <p className="mt-1 text-sm text-error flex items-center">
                      <Icon name="AlertCircle" size={12} className="mr-1" />
                      {validationErrors.phone}
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-sm text-text-secondary bg-gray-50 border border-gray-200 p-3 rounded-lg">
                  {profile?.phone || 'Not provided'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Email Information - Read-only */}
        <div>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Email Address
              </label>
              <div className="text-sm text-text-secondary bg-gray-50 border border-gray-200 p-3 rounded-lg flex items-center justify-between min-w-0">
                <span className="flex-1 min-w-0 truncate">
                  <span className="block truncate">{user?.email}</span>
                  <span className="text-xs text-text-muted">(Cannot be changed)</span>
                </span>
                <Icon name="Lock" size={14} className="text-gray-400 flex-shrink-0 ml-2" />
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Branding Section */}
      <BrandingSection />
    </div>
  );
};

export default ProfileSection;