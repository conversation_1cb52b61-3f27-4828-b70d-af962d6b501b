import React, { useState, useCallback, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import DocumentWorkflowHeader from '../document-editor/components/DocumentWorkflowHeader';
import CreationWizard from './components/CreationWizard';
import { prodLogger } from '../../utils/prodLogger.js';

const DocumentCreator = () => {
  const { contentMargin } = useSidebar();
  const location = useLocation();

  const [documentData, setDocumentData] = useState({
    documentType: 'ebook',
    language: 'english',
    tone: 'academic',
    title: 'Untitled Document',
    format: 'pdf',
    baseline: 'docgenerate'
  });
  const [currentStep, setCurrentStep] = useState(1);
  
  // Force reset when user navigates to creator with reset flag
  const shouldResetData = location.state?.resetAIContent;
  const [creatorKey, setCreatorKey] = useState(Date.now());
  
  useEffect(() => {
    if (shouldResetData) {
      // Reset all data and force component remount
      prodLogger.debug('🔄 DocumentCreator: Resetting data due to navigation flag');
      setDocumentData({
        documentType: 'ebook',
        language: 'english', 
        tone: 'academic',
        title: 'Untitled Document',
        format: 'pdf',
        baseline: 'docgenerate'
      });
      setCurrentStep(1);
      setCreatorKey(Date.now());
    }
  }, [shouldResetData]);

  // Additional protection: Reset when user navigates to creator organically
  useEffect(() => {
    // Check if this is a fresh navigation (no location state or different session)
    const sessionId = sessionStorage.getItem('docforge_wizard_session');
    const currentSession = Date.now().toString();
    
    if (!location.state?.resetAIContent && (!sessionId || Date.now() - parseInt(sessionId) > 300000)) { // 5 minutes
      prodLogger.debug('🔄 DocumentCreator: Detected fresh navigation session - resetting data');
      
      // Set new session
      sessionStorage.setItem('docforge_wizard_session', currentSession);
      
      // Reset document data to ensure clean state
      setDocumentData({
        documentType: 'ebook',
        language: 'english', 
        tone: 'academic',
        title: 'Untitled Document',
        format: 'pdf',
        baseline: 'docgenerate'
      });
      setCurrentStep(1);
      setCreatorKey(Date.now());
    }
  }, [location.pathname]); // Trigger on path changes



  const handleDocumentDataChange = useCallback((newData) => {
    setDocumentData(prev => ({ ...prev, ...newData }));
  }, []);

  const handleStepChange = useCallback((step) => {
    setCurrentStep(step);
  }, []);



  // Always show 'Generate' phase as active during document creation
  const currentPhase = 'Generate';

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />

      {/* Reused DocumentWorkflowHeader with creator mode */}
      <DocumentWorkflowHeader
        currentPhase={currentPhase}
        mode="creator"
        headerTitle="Creation Wizard"
        showStepCounter={true}
        currentStep={currentStep}
        totalSteps={8}
        onPhaseClick={null} // Disable phase navigation in creator mode
      />

      {/* Main Content - Clean Layout with top padding for fixed header */}
      <main className={`${contentMargin} pt-16 transition-all duration-300 ease-in-out`}>
        <div className="h-screen">
          <CreationWizard
            key={creatorKey}
            currentStep={currentStep}
            onStepChange={handleStepChange}
            documentData={documentData}
            onDocumentDataChange={handleDocumentDataChange}
          />
        </div>
      </main>
    </div>
  );
};

export default DocumentCreator;