import React, { useState, useEffect } from 'react';
import Button from '../../../../components/ui/Button';
import MultiSelectCard from '../questionnaire/MultiSelectCard';
import { generateSubNiches } from '../../../../services/aiService';
import { useAuth } from '../../../../contexts/AuthContext';
import usageTrackingService from '../../../../services/usageTrackingService';
import { getDocumentTerminology } from '../../constants/documentOptions';

import { prodLogger } from '../../../../utils/prodLogger.js';
/**
 * SubNicheSelectionStep - Dedicated step for AI-generated sub-niche selection
 * Generates relevant sub-niches based on the user's entered topic
 */
const SubNicheSelectionStep = ({
  formData,
  onInputChange,
  onValidationChange,
  className = ''
}) => {
  const { user } = useAuth();
  // Get dynamic terminology based on document type and subtype
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const subType = formData.documentPurpose?.subType;
  const terminology = getDocumentTerminology(documentType, subType);

  const [availableSubNiches, setAvailableSubNiches] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [componentKey, setComponentKey] = useState(0); // Force reset when needed

  // AI-powered sub-niche generation using real Gemini AI
  const generateSubNichesForTopic = async (topic) => {
    setIsGenerating(true);

    try {
      // Use real AI service with document type-specific prompts
      const language = formData.topicAndNiche?.language || 'english';
      const aiSubNiches = await generateSubNiches(topic, language, formData);

      // Track AI request usage (supplementary feature)
      if (user?.id) {
        try {
          await usageTrackingService.trackUsage(user.id, 'ai_generation', 1, {
            operation: 'sub_niche_generation',
            topic: topic,
            language: language,
            timestamp: new Date().toISOString()
          });
          prodLogger.debug('✅ Sub-niche generation usage tracked successfully');
        } catch (trackingError) {
          prodLogger.error('Failed to track sub-niche generation usage:', trackingError);
          // Don't fail the operation if tracking fails
        }
      }

      setAvailableSubNiches(aiSubNiches);
      onInputChange('topicAndNiche.availableSubNiches', aiSubNiches);
      setHasGenerated(true);

    } catch (error) {
      prodLogger.error('Error generating sub-niches:', error);
      // Fallback to predefined options if AI fails
      const fallbackSubNiches = getFallbackSubNiches(topic);
      setAvailableSubNiches(fallbackSubNiches);
      onInputChange('topicAndNiche.availableSubNiches', fallbackSubNiches);
    } finally {
      setIsGenerating(false);
    }
  };

  // Fallback sub-niches if AI generation fails
  const getFallbackSubNiches = (topic) => {
    return [
      { id: 'fundamentals', name: `${topic} Fundamentals`, description: 'Core concepts and basics' },
      { id: 'practical-guide', name: `Practical ${topic} Guide`, description: 'Hands-on approach and implementation' },
      { id: 'advanced-techniques', name: `Advanced ${topic} Techniques`, description: 'Expert-level strategies' },
      { id: 'common-mistakes', name: `Common ${topic} Mistakes`, description: 'What to avoid and how to fix issues' },
    ];
  };

  // Generate sub-niches when component mounts or topic changes
  useEffect(() => {
    const topic = formData.topicAndNiche?.mainTopic;
    if (topic && !hasGenerated) {
      generateSubNichesForTopic(topic);
    }
  }, [formData.topicAndNiche?.mainTopic, hasGenerated]);

  // Reset hasGenerated flag when availableSubNiches are cleared (e.g., returning from editor)
  useEffect(() => {
    const topic = formData.topicAndNiche?.mainTopic;
    const availableSubNiches = formData.topicAndNiche?.availableSubNiches;

    // Reset if topic exists but no available sub-niches (indicates reset)
    if (topic && (!availableSubNiches || availableSubNiches.length === 0)) {
      prodLogger.debug('Detected AI content reset - resetting sub-niche generation flag');
      setHasGenerated(false);
      setAvailableSubNiches([]);
      setComponentKey(prev => prev + 1); // Force component reset
    }
  }, [formData.topicAndNiche?.availableSubNiches]);

  // Reset component state when topic changes (new document creation)
  useEffect(() => {
    const currentTopic = formData.topicAndNiche?.mainTopic;
    
    // Check if this is a fresh start with a topic but no sub-niches generation in progress
    if (!formData.topicAndNiche?.availableSubNiches?.length && 
        !isGenerating && 
        hasGenerated &&
        currentTopic) {
      // Reset local state for fresh generation
      prodLogger.debug('🔄 Resetting SubNicheSelectionStep for new document creation');
      setAvailableSubNiches([]);
      setHasGenerated(false);
      setIsGenerating(false);
      setComponentKey(prev => prev + 1);
    }
  }, [formData.topicAndNiche?.mainTopic, formData.topicAndNiche?.availableSubNiches]);

  const handleSubNicheChange = (selectedSubNiches) => {
    onInputChange('topicAndNiche.subNiches', selectedSubNiches);
  };

  const handleRegenerateSubNiches = () => {
    const topic = formData.topicAndNiche?.mainTopic;
    if (topic) {
      setHasGenerated(false);
      generateSubNichesForTopic(topic);
    }
  };

  // Validation
  useEffect(() => {
    const isValid = formData.topicAndNiche?.subNiches?.length > 0 ||
                   formData.topicAndNiche?.customSubNiche?.trim().length > 0;

    onValidationChange?.(isValid);
  }, [formData.topicAndNiche?.subNiches, formData.topicAndNiche?.customSubNiche]);

  return (
    <div key={componentKey} className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Choose your {terminology.subTopicLabel}
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          {terminology.subTopicDescription.replace('your topic', '')} <strong>{formData.topicAndNiche?.mainTopic}</strong>
        </p>
      </div>

      {/* Loading State */}
      {isGenerating && (
        <div className="text-center py-12">
          <div className="inline-flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="text-text-secondary">Generating relevant {terminology.subTopicLabel.toLowerCase()} using AI...</span>
          </div>
        </div>
      )}

      {/* Sub-niches Selection */}
      {!isGenerating && availableSubNiches.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg md:text-xl font-bold text-text-primary mb-2">
                AI-Generated {terminology.subTopicLabel}
              </h3>
              <p className="text-text-secondary text-sm md:text-base">
                Select one or more areas that match your content goals
              </p>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleRegenerateSubNiches}
              iconName="RefreshCw"
              iconPosition="left"
              className="text-sm"
            >
              Regenerate
            </Button>
          </div>

          <MultiSelectCard
            options={availableSubNiches}
            value={formData.topicAndNiche?.subNiches || []}
            onChange={handleSubNicheChange}
            multiSelect={true}
            columns={2}
            size="medium"
            showIcons={false}
            showDescriptions={true}
            allowCustom={true}
            customPlaceholder={`Enter your custom ${terminology.subTopicLabel.toLowerCase().slice(0, -1)}...`}
            onOptionsUpdate={(updatedOptions) => {
              setAvailableSubNiches(updatedOptions);
              onInputChange('topicAndNiche.availableSubNiches', updatedOptions);
            }}
            onCustomAdd={(customValue) => {
              // Keep for backward compatibility, but main logic is now handled in MultiSelectCard
              onInputChange('topicAndNiche.customSubNiche', customValue);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default SubNicheSelectionStep;
