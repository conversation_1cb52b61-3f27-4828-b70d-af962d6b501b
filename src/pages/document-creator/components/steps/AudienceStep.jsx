import React, { useEffect } from 'react';
import Input from '../../../../components/ui/Input';
import CustomDropdown from '../CustomDropdown';
import { getDocumentTerminology, ACADEMIC_LEVELS, getAcademicLevelById } from '../../constants/documentOptions';

/**
 * AudienceStep - Enhanced audience targeting with academic level selection
 * Conditional rendering: dropdown for academic documents, text input for others
 */
const AudienceStep = ({
  formData,
  onInputChange,
  onValidationChange,
  className = ''
}) => {
  // Get dynamic terminology based on document type and subtype
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const subType = formData.documentPurpose?.subType;
  const terminology = getDocumentTerminology(documentType, subType);
  
  // Check if this is an academic document
  const isAcademicDocument = documentType === 'academic';
  
  // Academic level options for dropdown
  const academicLevelOptions = ACADEMIC_LEVELS.map(level => ({
    id: level.id,
    name: level.name,
    description: level.description
  }));

  const handleAudienceChange = (value) => {
    onInputChange('audienceAnalysis.primaryAudience', value);
    // Also update the description field for consistency
    onInputChange('audienceAnalysis.audienceDescription', value);
  };
  
  const handleAcademicLevelChange = (levelId) => {
    const selectedLevel = getAcademicLevelById(levelId);
    onInputChange('audienceAnalysis.primaryAudience', levelId);
    onInputChange('audienceAnalysis.audienceDescription', selectedLevel.description);
    onInputChange('audienceAnalysis.academicLevel', levelId);
  };

  // Enhanced validation for academic vs non-academic documents
  useEffect(() => {
    let isValid = false;
    
    if (isAcademicDocument) {
      // For academic documents, validate dropdown selection
      isValid = ACADEMIC_LEVELS.some(level => 
        level.id === formData.audienceAnalysis?.primaryAudience
      );
    } else {
      // For non-academic documents, validate text input
      isValid = formData.audienceAnalysis?.primaryAudience?.trim().length > 0;
    }
    
    onValidationChange?.(isValid);
  }, [formData.audienceAnalysis?.primaryAudience, isAcademicDocument]);

  return (
    <div className={`space-y-8 max-w-2xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-left md:text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          {terminology.audiencePrompt}
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          {isAcademicDocument 
            ? "Select the academic level for your target readership"
            : terminology.audienceDescription
          }
        </p>
      </div>

      {/* Audience Input - Conditional Rendering */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          {terminology.audienceLabel}
        </label>
        
        {isAcademicDocument ? (
          // Academic Level Dropdown
          <CustomDropdown
            value={formData.audienceAnalysis?.primaryAudience || ''}
            onChange={handleAcademicLevelChange}
            options={academicLevelOptions}
            placeholder="Select academic level"
            className="w-full"
          />
        ) : (
          // Regular Text Input for Non-Academic Documents
          <Input
            type="text"
            placeholder="parents"
            value={formData.audienceAnalysis?.primaryAudience || ''}
            onChange={(e) => handleAudienceChange(e.target.value)}
            className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
          />
        )}
        
        {/* Academic Level Description */}
        {isAcademicDocument && formData.audienceAnalysis?.primaryAudience && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>{getAcademicLevelById(formData.audienceAnalysis.primaryAudience)?.name}:</strong>{' '}
              {getAcademicLevelById(formData.audienceAnalysis.primaryAudience)?.description}
            </p>
          </div>
        )}
      </div>

      {/* Additional Context (Optional) */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-secondary">
          Additional Context (Optional)
        </label>
        <textarea
          placeholder={isAcademicDocument 
            ? "e.g., Students focusing on applied research, thesis writers needing practical frameworks..."
            : "e.g., Busy working parents with children aged 5-12 who are looking for practical wellness strategies..."
          }
          value={formData.audienceAnalysis?.context || ''}
          onChange={(e) => onInputChange('audienceAnalysis.context', e.target.value)}
          rows={3}
          className="w-full text-base md:text-lg rounded-lg border-2 border-border focus:border-primary focus:ring-0 px-4 py-3 resize-vertical"
        />
        <p className="text-xs text-text-secondary">
          Provide more details about your target audience to help generate more targeted content
        </p>
      </div>
    </div>
  );
};

export default AudienceStep;
