import React, { useEffect } from 'react';
import Input from '../../../../components/ui/Input';
import CustomDropdown from '../CustomDropdown';
import { getDocumentTerminology } from '../../constants/documentOptions';

/**
 * TopicSelectionStep - Dedicated step for topic input only
 * Clean, focused interface for users to enter their main topic
 */
const TopicSelectionStep = ({
  formData,
  onInputChange,
  onValidationChange,
  className = ''
}) => {
  // Get dynamic terminology based on document type
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const terminology = getDocumentTerminology(documentType);

  // Language options
  const languageOptions = [
    { id: 'english', name: 'English (British)', icon: '🇬🇧' },
    { id: 'english-us', name: 'English (US)', icon: '🇺🇸' },
    { id: 'spanish', name: 'Spanish', icon: '🇪🇸' },
    { id: 'french', name: 'French', icon: '🇫🇷' },
    { id: 'german', name: 'German', icon: '🇩🇪' },
    { id: 'italian', name: 'Italian', icon: '🇮🇹' },
    { id: 'portuguese', name: 'Portuguese', icon: '🇵🇹' },
    { id: 'dutch', name: 'Dutch', icon: '🇳🇱' },
  ];

  const handleTopicChange = (value) => {
    onInputChange('topicAndNiche.mainTopic', value);
    // Clear any previously selected sub-niches when topic changes
    onInputChange('topicAndNiche.subNiches', []);
    onInputChange('topicAndNiche.availableSubNiches', []);
  };

  // Validation - only require topic and language
  useEffect(() => {
    const isValid = formData.topicAndNiche?.mainTopic?.trim().length > 0 &&
                   formData.topicAndNiche?.language;

    onValidationChange?.(isValid);
  }, [formData.topicAndNiche?.mainTopic, formData.topicAndNiche?.language]);

  // Check if we have extracted content
  const hasExtractedContent = formData.documentPurpose?.importedContent?.extractedContent;

  return (
    <div className={`space-y-8 max-w-2xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          {hasExtractedContent ? `Confirm or Modify Your ${terminology.topicLabel}` : `Enter your ${terminology.topicLabel}`}
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          {hasExtractedContent
            ? 'We\'ve pre-filled the topic based on your imported content. You can modify it if needed.'
            : terminology.topicDescription
          }
        </p>
      </div>

      {/* Main Topic Input */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          {hasExtractedContent ? `${terminology.topicLabel} (from imported content)` : `Enter your ${terminology.topicLabel.toLowerCase()}`}
        </label>
        <Input
          type="text"
          placeholder={hasExtractedContent ? "Topic extracted from your content" : terminology.topicPlaceholder}
          value={formData.topicAndNiche?.mainTopic || ''}
          onChange={(e) => handleTopicChange(e.target.value)}
          className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 focus:border-primary focus:ring-0 px-4"
          autoFocus={!hasExtractedContent}
        />
        {hasExtractedContent ? (
          <p className="text-xs text-green-600">
            ✓ Topic automatically extracted from your imported content. You can modify it if needed.
          </p>
        ) : null}
      </div>

      {/* Language Selection */}
      <div className="space-y-4">
        <label className="block text-sm md:text-base font-medium text-text-primary">
          Language
        </label>
        <CustomDropdown
          value={formData.topicAndNiche?.language || 'english'}
          onChange={(value) => onInputChange('topicAndNiche.language', value)}
          options={languageOptions}
          placeholder="English (British)"
          className="w-full"
        />
      </div>
    </div>
  );
};

export default TopicSelectionStep;
