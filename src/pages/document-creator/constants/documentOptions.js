/**
 * Static Document Creation Options
 * Centralized constants for document types, formats, and workflow configuration
 */

import { isFeatureEnabled, FEATURES } from "../../../config/features";

/**
 * Academic level options for academic documents
 */
export const ACADEMIC_LEVELS = [
  {
    id: "undergraduate",
    name: "Undergraduate",
    description: "Bachelor's degree level students and researchers",
    aiContext:
      "undergraduate level academic writing with foundational concepts and clear explanations suitable for bachelor's degree students",
  },
  {
    id: "masters",
    name: "Masters",
    description: "Master's degree level students and researchers",
    aiContext:
      "graduate level academic writing with advanced analysis and research methodologies appropriate for master's degree students",
  },
  {
    id: "phd",
    name: "PhD",
    description: "Doctoral level researchers and academics",
    aiContext:
      "doctoral level academic writing with original research focus, sophisticated theoretical frameworks, and comprehensive scholarly depth",
  },
];

/**
 * Get academic level by ID
 * @param {string} id - Academic level ID (undergraduate, masters, phd)
 * @returns {Object|null} Academic level object or null if not found
 */
export const getAcademicLevelById = (id) => {
  return ACADEMIC_LEVELS.find((level) => level.id === id) || null;
};

/**
 * Get academic level context for AI prompts
 * @param {string} academicLevel - Academic level ID
 * @returns {string} AI context string for the academic level
 */
export const getAcademicLevelContext = (academicLevel) => {
  const level = getAcademicLevelById(academicLevel);
  return level
    ? level.aiContext
    : "graduate level academic writing with appropriate scholarly depth";
};

/**
 * Available document types with metadata
 */
export const DOCUMENT_TYPES = [
  {
    id: "ebook",
    name: "eBook",
    icon: "Book",
    description: "Digital book with chapters and sections",
  },
  {
    id: "academic",
    name: "Academic Paper",
    icon: "GraduationCap",
    description: "Research papers, essays, theses",
  },
  {
    id: "business",
    name: "Business Document",
    icon: "Briefcase",
    description: "Reports, proposals, presentations",
  },
];

/**
 * Document subtypes for each main document type
 */
export const DOCUMENT_SUBTYPES = {
  academic: [
    {
      id: "essay",
      name: "Essay",
      description: "Short academic writing piece with focused argument",
    },
    {
      id: "research-paper",
      name: "Research Paper",
      description: "In-depth research study with methodology and findings",
    },
    {
      id: "assignment",
      name: "Assignment",
      description: "Course assignment or homework submission",
    },
    {
      id: "thesis",
      name: "Thesis",
      description: "Graduate thesis document with original research",
    },
    {
      id: "dissertation",
      name: "Dissertation",
      description: "Doctoral dissertation with comprehensive research",
    },
  ],
  business: [
    {
      id: "proposal",
      name: "Proposal",
      description: "Business proposal document for projects or services",
    },
    {
      id: "report",
      name: "Report",
      description: "Business report or analysis document",
    },
    {
      id: "business-plan",
      name: "Business Plan",
      description: "Comprehensive business plan document",
    },
    {
      id: "executive-summary",
      name: "Executive Summary",
      description: "High-level summary document for executives",
    },
  ],
  ebook: [], // eBooks don't have subtypes
};

/**
 * Available output formats
 */
export const FORMAT_OPTIONS = [
  {
    id: "pdf",
    name: "PDF",
    description: "Portable Document Format",
  },
  {
    id: "word",
    name: "Word Document",
    description: "Microsoft Word format",
  },
  {
    id: "html",
    name: "Web Page",
    description: "HTML format for web",
  },
];

/**
 * Baseline content options for document creation
 */
const ALL_BASELINE_OPTIONS = [
  {
    id: "scratch",
    name: "Start from scratch",
    description: "Begin with a blank document",
  },
  {
    id: "docgenerate",
    name: "Use DocGenerate",
    description: "Create document using AI-powered generation",
  },
  {
    id: "import-docx",
    name: "Import from DOCX",
    description: "Upload and extract content from DOCX file",
  },
  {
    id: "import-pdf",
    name: "Import from PDF",
    description: "Upload and extract content from PDF file",
  },
];

/**
 * Baseline options filtered by feature flags
 */
export const BASELINE_OPTIONS = ALL_BASELINE_OPTIONS.filter((option) => {
  // Filter out PDF import if feature is disabled
  if (option.id === "import-pdf" && !isFeatureEnabled(FEATURES.PDF_IMPORT)) {
    return false;
  }
  return true;
});

/**
 * High-level workflow phases for document creation
 */
export const WORKFLOW_PHASES = [
  {
    id: 1,
    title: "Generate",
    icon: "Sparkles",
    description: "AI content generation",
  },
  {
    id: 2,
    title: "Edit Content",
    icon: "Edit3",
    description: "Review and modify content",
  },
  {
    id: 3,
    title: "Review",
    icon: "Eye",
    description: "Review document content",
  },
  {
    id: 4,
    title: "Publish",
    icon: "Send",
    description: "Export and share",
  },
];

/**
 * Default document type mapping for auto-inference
 */
export const DOCUMENT_TYPE_MAPPING = {
  "academic-paper": "academic",
  "business-report": "business",
  guide: "guide",
  ebook: "ebook",
};

/**
 * Dynamic terminology mappings for different document types
 * Used to customize UI labels and text based on selected document type
 */
export const DOCUMENT_TERMINOLOGY = {
  ebook: {
    documentName: "eBook",
    documentNameLower: "eBook",
    topicLabel: "Topic or Niche",
    topicPlaceholder: "Life coaching",
    topicDescription: "What topic would you like to create content about?",
    subTopicLabel: "Sub-niches",
    subTopicDescription: "Select specific areas within your topic to focus on",
    audienceLabel: "Audience",
    audienceDescription: "What audience is your eBook targeted towards?",
    audiencePrompt: "Enter the audience for your eBook",
    toneLabel: "Tone of Voice",
    tonePrompt: "Select the tone of voice for your eBook",
    themeLabel: "Topic Category",
    themePrompt: "Choose a topic category that best fits your eBook",
    themeDescription: "Select the main category your eBook content falls under",
    contentType: "chapters",
    structureType: "book structure",
  },
  academic: {
    documentName: "Academic Paper",
    documentNameLower: "academic paper",
    topicLabel: "Research Area",
    topicPlaceholder: "Machine learning applications",
    topicDescription:
      "What research area or field of study will your paper focus on?",
    subTopicLabel: "Research Focus Areas",
    subTopicDescription:
      "Select specific research areas or subtopics within your field",
    audienceLabel: "Target Readership",
    audienceDescription:
      "Who is the intended academic audience for your paper?",
    audiencePrompt: "Enter the target readership for your academic paper",
    toneLabel: "Academic Voice",
    tonePrompt: "Select the academic writing style for your paper",
    themeLabel: "Research Domain",
    themePrompt: "Select the primary research area for your academic paper",
    themeDescription:
      "Choose the academic field or discipline your research belongs to",
    contentType: "sections",
    structureType: "academic structure",
  },
  business: {
    documentName: "Business Document",
    documentNameLower: "business document",
    topicLabel: "Subject Matter",
    topicPlaceholder: "Market analysis for fintech",
    topicDescription:
      "What business area or subject matter will your document address?",
    subTopicLabel: "Key Focus Areas",
    subTopicDescription:
      "Select specific business domains or focus areas to cover",
    audienceLabel: "Stakeholders",
    audienceDescription:
      "Who are the key stakeholders or decision-makers for this document?",
    audiencePrompt: "Enter the stakeholders for your business document",
    toneLabel: "Communication Style",
    tonePrompt: "Select the professional tone for your business document",
    themeLabel: "Business Domain",
    themePrompt: "Choose the business domain that best fits your document",
    themeDescription:
      "Select the primary business area your document addresses",
    contentType: "sections",
    structureType: "business structure",
  },
};

/**
 * Get terminology for a specific document type and subtype
 * @param {string} documentType - The document type (ebook, academic, business)
 * @param {string} subType - The document subtype (optional)
 * @returns {Object} Terminology object for the document type/subtype
 */
export const getDocumentTerminology = (
  documentType = "ebook",
  subType = null
) => {
  const baseTerminology =
    DOCUMENT_TERMINOLOGY[documentType] || DOCUMENT_TERMINOLOGY.ebook;

  // Subtype-specific terminology overrides
  if (documentType === "academic" && subType) {
    const subtypeOverrides = {
      thesis: {
        tonePrompt: "Select the academic writing style for your thesis",
        themePrompt: "Choose the primary research domain for your thesis",
      },
      dissertation: {
        tonePrompt: "Select the academic writing style for your dissertation",
        themePrompt: "Choose the primary research domain for your dissertation",
      },
      essay: {
        tonePrompt: "Select the writing style for your essay",
        themePrompt: "Choose the subject area for your essay",
      },
      "research-paper": {
        tonePrompt: "Select the research writing style for your paper",
        themePrompt: "Choose the research domain for your paper",
      },
    };

    if (subtypeOverrides[subType]) {
      return { ...baseTerminology, ...subtypeOverrides[subType] };
    }
  }

  if (documentType === "business" && subType) {
    const subtypeOverrides = {
      proposal: {
        tonePrompt: "Select the communication style for your proposal",
        themePrompt: "Choose the business domain for your proposal",
      },
      "executive-summary": {
        tonePrompt: "Select the executive communication style",
        themePrompt: "Choose the business area for your summary",
      },
      report: {
        tonePrompt: "Select the communication style for your report",
        themePrompt: "Choose the business domain for your report",
      },
      "business-plan": {
        tonePrompt: "Select the communication style for your business plan",
        themePrompt: "Choose the business domain for your plan",
      },
    };

    if (subtypeOverrides[subType]) {
      return { ...baseTerminology, ...subtypeOverrides[subType] };
    }
  }

  return baseTerminology;
};

/**
 * Helper function to get document type by ID
 * @param {string} id - Document type ID
 * @returns {Object|null} Document type object or null if not found
 */
export const getDocumentTypeById = (id) => {
  return DOCUMENT_TYPES.find((type) => type.id === id) || null;
};

/**
 * Helper function to get format option by ID
 * @param {string} id - Format option ID
 * @returns {Object|null} Format option object or null if not found
 */
export const getFormatOptionById = (id) => {
  return FORMAT_OPTIONS.find((format) => format.id === id) || null;
};

/**
 * Helper function to get baseline option by ID
 * @param {string} id - Baseline option ID
 * @returns {Object|null} Baseline option object or null if not found
 */
export const getBaselineOptionById = (id) => {
  return BASELINE_OPTIONS.find((baseline) => baseline.id === id) || null;
};

/**
 * Helper function to get workflow phase by ID
 * @param {number} id - Phase ID
 * @returns {Object|null} Workflow phase object or null if not found
 */
export const getWorkflowPhaseById = (id) => {
  return WORKFLOW_PHASES.find((phase) => phase.id === id) || null;
};

/**
 * Get subtypes for a specific document type
 * @param {string} documentType - The document type (ebook, academic, business)
 * @returns {Array} Array of subtype options for the document type
 */
export const getSubtypesForDocumentType = (documentType) => {
  return DOCUMENT_SUBTYPES[documentType] || [];
};

/**
 * Get subtype by ID for a specific document type
 * @param {string} documentType - The document type
 * @param {string} subtypeId - The subtype ID
 * @returns {Object|null} Subtype object or null if not found
 */
export const getSubtypeById = (documentType, subtypeId) => {
  const subtypes = getSubtypesForDocumentType(documentType);
  return subtypes.find((subtype) => subtype.id === subtypeId) || null;
};

/**
 * Check if a document type has subtypes
 * @param {string} documentType - The document type
 * @returns {boolean} True if the document type has subtypes
 */
export const hasSubtypes = (documentType) => {
  return getSubtypesForDocumentType(documentType).length > 0;
};

/**
 * Get default subtype for a document type
 * @param {string} documentType - The document type
 * @returns {string|null} Default subtype ID or null if no subtypes
 */
export const getDefaultSubtype = (documentType) => {
  const subtypes = getSubtypesForDocumentType(documentType);
  return subtypes.length > 0 ? subtypes[0].id : null;
};
