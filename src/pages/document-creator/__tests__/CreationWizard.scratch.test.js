import React from 'react';
import { render, waitFor, screen } from '@testing-library/react';
import { jest } from '@jest/globals';
import { MemoryRouter } from 'react-router-dom';
import CreationWizard from '../components/CreationWizard';
import { AuthProvider } from '../../../contexts/AuthContext';
import { documentStorage } from '../../../services/documentStorageService';

// Mock the documentStorage service
jest.mock('../../../services/documentStorageService', () => ({
  documentStorage: {
    createBlankDocument: jest.fn()
  }
}));

// Mock the navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ state: null })
}));

// Mock the AuthContext
const mockUser = { id: 'test-user-id' };
jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => ({ user: mockUser })
}));

// Mock other dependencies
jest.mock('../../../utils/prodLogger.js', () => ({
  prodLogger: {
    debug: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  }
}));

describe('CreationWizard Scratch Baseline Redirect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  const renderCreationWizard = (formData = {}) => {
    const defaultProps = {
      currentStep: 1,
      onStepChange: jest.fn(),
      documentData: {},
      onDocumentDataChange: jest.fn()
    };

    return render(
      <MemoryRouter>
        <CreationWizard {...defaultProps} />
      </MemoryRouter>
    );
  };

  test('should redirect immediately when scratch baseline is selected', async () => {
    // Mock successful document creation
    const mockDocumentId = 'test-document-id';
    documentStorage.createBlankDocument.mockResolvedValueOnce({
      success: true,
      documentId: mockDocumentId
    });

    // Render component with scratch baseline
    const component = renderCreationWizard();

    // Wait for the component to mount and process the effect
    await waitFor(() => {
      expect(documentStorage.createBlankDocument).toHaveBeenCalledWith(mockUser.id);
    }, { timeout: 3000 });

    // Verify navigation to document editor
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(`/document-editor/${mockDocumentId}`);
    });
  });

  test('should show error alert when document creation fails', async () => {
    // Mock failed document creation
    documentStorage.createBlankDocument.mockResolvedValueOnce({
      success: false,
      error: 'Database connection failed'
    });

    // Mock window.alert
    const mockAlert = jest.spyOn(window, 'alert').mockImplementation(() => {});

    renderCreationWizard();

    // Wait for error handling
    await waitFor(() => {
      expect(documentStorage.createBlankDocument).toHaveBeenCalledWith(mockUser.id);
    });

    await waitFor(() => {
      expect(mockAlert).toHaveBeenCalledWith('Unable to create blank document. Please try again.');
    });

    // Should not navigate on error
    expect(mockNavigate).not.toHaveBeenCalled();

    mockAlert.mockRestore();
  });

  test('should handle document creation service exception', async () => {
    // Mock service throwing an exception
    documentStorage.createBlankDocument.mockRejectedValueOnce(
      new Error('Network error')
    );

    // Mock window.alert
    const mockAlert = jest.spyOn(window, 'alert').mockImplementation(() => {});

    renderCreationWizard();

    // Wait for error handling
    await waitFor(() => {
      expect(documentStorage.createBlankDocument).toHaveBeenCalledWith(mockUser.id);
    });

    await waitFor(() => {
      expect(mockAlert).toHaveBeenCalledWith('An error occurred while creating your document. Please try again.');
    });

    // Should not navigate on exception
    expect(mockNavigate).not.toHaveBeenCalled();

    mockAlert.mockRestore();
  });

  test('should not redirect if user is not authenticated', async () => {
    // Mock no user
    jest.mocked(require('../../../contexts/AuthContext').useAuth).mockReturnValueOnce({
      user: null
    });

    renderCreationWizard();

    // Wait a reasonable time to ensure no redirect happens
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Should not create document or navigate
    expect(documentStorage.createBlankDocument).not.toHaveBeenCalled();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  test('should not redirect for other baseline options', async () => {
    // Test that docgenerate baseline doesn't trigger redirect
    renderCreationWizard();

    // Wait a reasonable time to ensure no redirect happens
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Should not create document or navigate for non-scratch baseline
    expect(documentStorage.createBlankDocument).not.toHaveBeenCalled();
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});

describe('usesStandardWorkflow Function', () => {
  test('should return true only for docgenerate baseline', () => {
    // Since usesStandardWorkflow is not exported, we test its behavior indirectly
    // by checking if scratch baseline triggers redirect instead of standard workflow
    
    // This is implicitly tested by the redirect tests above
    // The fact that scratch triggers redirect means it's not using standard workflow
    expect(true).toBe(true); // Placeholder for indirect testing
  });
});