/**
 * Test file to verify dynamic terminology system
 * This file can be used to test that terminology changes correctly based on document type
 */

import { getDocumentTerminology } from "../constants/documentOptions";

// Test function to verify terminology mappings
export const testTerminologyMappings = () => {
  const testResults = [];

  // Test each document type
  const documentTypes = ["ebook", "academic", "business"];

  documentTypes.forEach((docType) => {
    const terminology = getDocumentTerminology(docType);

    testResults.push({
      documentType: docType,
      documentName: terminology.documentName,
      topicLabel: terminology.topicLabel,
      subTopicLabel: terminology.subTopicLabel,
      audienceLabel: terminology.audienceLabel,
      audiencePrompt: terminology.audiencePrompt,
      tonePrompt: terminology.tonePrompt,
      topicPlaceholder: terminology.topicPlaceholder,
    });
  });

  return testResults;
};

// Test fallback behavior
export const testFallbackBehavior = () => {
  // Test with invalid document type - should fallback to ebook
  const fallbackTerminology = getDocumentTerminology("invalid-type");

  return {
    fallbackDocumentName: fallbackTerminology.documentName,
    fallbackTopicLabel: fallbackTerminology.topicLabel,
    shouldBeEbook: fallbackTerminology.documentName === "eBook",
  };
};

// Console test function for development
export const runTerminologyTests = () => {
  console.log("=== Dynamic Terminology System Tests ===");

  console.log("\n1. Testing terminology mappings for each document type:");
  const mappingResults = testTerminologyMappings();
  mappingResults.forEach((result) => {
    console.log(`\n${result.documentType.toUpperCase()}:`);
    console.log(`  Document Name: ${result.documentName}`);
    console.log(`  Topic Label: ${result.topicLabel}`);
    console.log(`  Sub-topic Label: ${result.subTopicLabel}`);
    console.log(`  Audience Label: ${result.audienceLabel}`);
    console.log(`  Audience Prompt: ${result.audiencePrompt}`);
    console.log(`  Tone Prompt: ${result.tonePrompt}`);
    console.log(`  Topic Placeholder: ${result.topicPlaceholder}`);
  });

  console.log("\n2. Testing fallback behavior:");
  const fallbackResult = testFallbackBehavior();
  console.log(
    `  Fallback Document Name: ${fallbackResult.fallbackDocumentName}`
  );
  console.log(`  Fallback Topic Label: ${fallbackResult.fallbackTopicLabel}`);
  console.log(
    `  Correctly falls back to eBook: ${fallbackResult.shouldBeEbook}`
  );

  console.log("\n=== Tests Complete ===");

  return {
    mappingResults,
    fallbackResult,
  };
};

// Example usage in component:
/*
import { getDocumentTerminology } from '../constants/documentOptions';

const MyComponent = ({ formData }) => {
  const documentType = formData.documentPurpose?.primaryType || 'ebook';
  const terminology = getDocumentTerminology(documentType);
  
  return (
    <div>
      <h2>{terminology.audiencePrompt}</h2>
      <p>{terminology.audienceDescription}</p>
      <label>{terminology.audienceLabel}</label>
    </div>
  );
};
*/
