/**
 * Test file to validate dynamic tone selection functionality
 * This file can be run to verify that the tone options and terminology
 * change correctly based on document type selection
 */

import { 
  getToneOptionsForDocumentType, 
  getThemeCategoriesForDocumentType,
  getThemeOptionsForDocumentType 
} from './questionnaireDataStructure.js';
import { getDocumentTerminology } from '../constants/documentOptions.js';

/**
 * Test function to validate tone options for different document types
 */
export const testToneOptions = () => {
  const documentTypes = ['ebook', 'academic', 'business'];
  const results = [];

  documentTypes.forEach(docType => {
    const toneOptions = getToneOptionsForDocumentType(docType);
    const terminology = getDocumentTerminology(docType);
    const themeCategories = getThemeCategoriesForDocumentType(docType);
    const themeOptions = getThemeOptionsForDocumentType(docType);

    results.push({
      documentType: docType,
      toneOptionsCount: toneOptions.length,
      toneOptions: toneOptions.map(t => t.name),
      terminology: {
        toneLabel: terminology.toneLabel,
        tonePrompt: terminology.tonePrompt,
        themeLabel: terminology.themeLabel,
        themePrompt: terminology.themePrompt
      },
      themeCategories: {
        label: themeCategories.label,
        description: themeCategories.description,
        categoriesCount: Object.keys(themeCategories.categories).length
      },
      themeOptions: themeOptions.map(t => t.name)
    });
  });

  return results;
};

/**
 * Test function to validate that terminology changes appropriately
 */
export const testTerminologyChanges = () => {
  const tests = [
    {
      documentType: 'ebook',
      expectedThemeLabel: 'Topic Category',
      expectedToneLabel: 'Tone of Voice'
    },
    {
      documentType: 'academic',
      expectedThemeLabel: 'Research Domain',
      expectedToneLabel: 'Academic Writing Style'
    },
    {
      documentType: 'business',
      expectedThemeLabel: 'Business Domain',
      expectedToneLabel: 'Communication Style'
    }
  ];

  const results = tests.map(test => {
    const terminology = getDocumentTerminology(test.documentType);
    return {
      documentType: test.documentType,
      themeLabel: terminology.themeLabel,
      toneLabel: terminology.toneLabel,
      themeMatches: terminology.themeLabel === test.expectedThemeLabel,
      toneMatches: terminology.toneLabel === test.expectedToneLabel
    };
  });

  return results;
};

/**
 * Test function to validate tone options are document-type specific
 */
export const testToneSpecificity = () => {
  const ebookTones = getToneOptionsForDocumentType('ebook').map(t => t.id);
  const academicTones = getToneOptionsForDocumentType('academic').map(t => t.id);
  const businessTones = getToneOptionsForDocumentType('business').map(t => t.id);

  return {
    ebook: {
      tones: ebookTones,
      hasConversational: ebookTones.includes('conversational'),
      hasInspirational: ebookTones.includes('inspirational')
    },
    academic: {
      tones: academicTones,
      hasFormalAcademic: academicTones.includes('formal-academic'),
      hasAnalytical: academicTones.includes('analytical'),
      hasObjective: academicTones.includes('objective')
    },
    business: {
      tones: businessTones,
      hasProfessional: businessTones.includes('professional'),
      hasExecutive: businessTones.includes('executive'),
      hasConsultative: businessTones.includes('consultative')
    }
  };
};

/**
 * Run all tests and log results
 */
export const runAllTests = () => {
  const toneResults = testToneOptions();
  toneResults.forEach(result => {
    console.log(`${result.documentType.toUpperCase()}:`);
    console.log(`  - Tone Options (${result.toneOptionsCount}): ${result.toneOptions.join(', ')}`);
    console.log(`  - Tone Label: "${result.terminology.toneLabel}"`);
    console.log(`  - Theme Label: "${result.terminology.themeLabel}"`);
    console.log(`  - Theme Categories: ${result.themeCategories.categoriesCount}`);
    console.log('');
  });

  console.log('\n2. Terminology Changes Test:');
  const terminologyResults = testTerminologyChanges();
  terminologyResults.forEach(result => {
    console.log(`${result.documentType.toUpperCase()}:`);
    console.log(`  - Theme Label: "${result.themeLabel}" (${result.themeMatches ? '✓' : '✗'})`);
    console.log(`  - Tone Label: "${result.toneLabel}" (${result.toneMatches ? '✓' : '✗'})`);
  });

  console.log('\n3. Tone Specificity Test:');
  const specificityResults = testToneSpecificity();
  Object.entries(specificityResults).forEach(([docType, data]) => {
    console.log(`${docType.toUpperCase()}:`);
    console.log(`  - Tones: ${data.tones.join(', ')}`);
    if (docType === 'ebook') {
      console.log(`  - Has Conversational: ${data.hasConversational ? '✓' : '✗'}`);
      console.log(`  - Has Inspirational: ${data.hasInspirational ? '✓' : '✗'}`);
    } else if (docType === 'academic') {
      console.log(`  - Has Formal Academic: ${data.hasFormalAcademic ? '✓' : '✗'}`);
      console.log(`  - Has Analytical: ${data.hasAnalytical ? '✓' : '✗'}`);
      console.log(`  - Has Objective: ${data.hasObjective ? '✓' : '✗'}`);
    } else if (docType === 'business') {
      console.log(`  - Has Professional: ${data.hasProfessional ? '✓' : '✗'}`);
      console.log(`  - Has Executive: ${data.hasExecutive ? '✓' : '✗'}`);
      console.log(`  - Has Consultative: ${data.hasConsultative ? '✓' : '✗'}`);
    }
  });

  console.log('\n=== Tests Complete ===');
  return {
    toneResults,
    terminologyResults,
    specificityResults
  };
};

// Export for use in browser console or testing
if (typeof window !== 'undefined') {
  window.toneSelectionTests = {
    testToneOptions,
    testTerminologyChanges,
    testToneSpecificity,
    runAllTests
  };
}
