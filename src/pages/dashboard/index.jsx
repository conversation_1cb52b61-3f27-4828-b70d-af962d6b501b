import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import { useAuth } from '../../contexts/AuthContext';
import { useProjects } from '../../hooks/useProjects';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import { ProjectCard, ProjectsLoading, ProjectsError } from '../../components/projects';
import Button from '../../components/ui/Button';
import { getDocumentRoute } from '../../utils/progressUtils';
import Icon from '../../components/AppIcon';
import { isFeatureEnabled, FEATURES } from '../../config/features';

import { prodLogger } from '../../utils/prodLogger.js';
const Dashboard = () => {
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const { user, profile, loading: authLoading } = useAuth();

  // Fetch recent projects (last 4 accessed)
  const {
    projects: recentProjects,
    isLoading: isLoadingRecent,
    error: recentError,
    refetch: refetchRecent
  } = useProjects({
    sortBy: 'last_accessed_at',
    sortOrder: 'desc',
    limit: 4,
    includeContent: false // No need to include content, we now use extracted_thumbnail_url
  });



  const handleEditDocument = (document) => {
    const route = getDocumentRoute(document);

    // Log for debugging
    prodLogger.debug(`Dashboard: Navigating to project: ${document.title}`, {
      projectId: document.id,
      status: document.status,
      progress: document.progress,
      route: route
    });

    // For document creator route, pass documentId in state for editing existing documents
    if (route === '/document-creator') {
      navigate(route, { state: { documentId: document.id } });
    } else {
      navigate(route);
    }
  };

  const handleEbookOptionClick = (action) => {
    switch (action) {
      case 'start-from-scratch':
        navigate('/document-creator');
        break;
      case 'import-from-docx':
        // Navigate to document creator with DOCX import pre-selected
        navigate('/document-creator', {
          state: {
            preselectedBaseline: 'import-docx'
          }
        });
        break;
      case 'import-from-pdf':
        // Check if PDF import feature is enabled
        if (isFeatureEnabled(FEATURES.PDF_IMPORT)) {
          // Navigate to document creator with PDF import pre-selected
          navigate('/document-creator', {
            state: {
              preselectedBaseline: 'import-pdf'
            }
          });
        } else {
          // Feature is disabled, redirect to general document creator
          prodLogger.debug('PDF import feature is disabled, redirecting to document creator');
          navigate('/document-creator');
        }
        break;
      case 'start-from-template':
        // Template library has been removed, redirect to document creator
        navigate('/document-creator');
        break;
      default:
        // For now, other buttons don't have functionality
        prodLogger.debug(`${action} functionality not yet implemented`);
        break;
    }
  };



  // Simplified loading check - only use auth loading
  if (authLoading) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <Header />
        <main className="lg:ml-64 ml-0 pt-16">
          <div className="px-6 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading your dashboard...</p>
                <p className="mt-2 text-xs text-text-muted">Please wait a moment...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      <Header />

      <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
        <div className="px-6 py-8">

          {/* Welcome Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold text-text-primary mb-2">
                  Welcome back, {profile?.full_name || user?.email?.split('@')[0] || 'User'}! 👋
                </h1>
                <p className="text-text-secondary">
                  {profile?.user_type ? (
                    <>Ready to create amazing documents as a {profile.user_type.replace('_', ' ')}?</>
                  ) : (
                    'Ready to create amazing documents with AI?'
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Action Cards */}
          {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-surface rounded-lg border border-border p-6 hover:shadow-elevated transition-all duration-300 cursor-pointer group">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-light rounded-md flex items-center justify-center">
                    <Icon name="BookOpen" size={20} className="text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary group-hover:text-primary transition-colors">New eBook</h3>
                    <p className="text-sm text-text-secondary">Start writing on a blank page or import</p>
                  </div>
                </div>
                <Icon name="ArrowRight" size={16} className="text-text-muted group-hover:text-primary transition-colors" />
              </div>
            </div>
          </div> */}

          {/* Hero Section - Matching reference design */}
          <div className="mb-12">
            <div className="bg-gradient-to-r from-hero-start via-hero-middle to-hero-end rounded-xl p-8 lg:p-12 text-white relative overflow-hidden">
              <div className="relative z-10 flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-4">
                    <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                      ✨ AI-Powered
                    </span>
                  </div>
                  <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                    {profile?.documents_created > 0
                      ? `Continue your document journey`
                      : `Meet your AI-powered document creator`
                    }
                  </h1>
                  <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    RapidDoc AI
                  </h2>
                  <button
                    onClick={() => navigate('/document-creator')}
                    className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-md font-semibold transition-all duration-300 transform hover:-translate-y-1 shadow-elevated"
                  >
                    Try it now →
                  </button>
                </div>

                {/* Hero Image/Illustration */}
                <div className="hidden lg:block flex-shrink-0 ml-8">
                  <div className="w-64 h-48 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Icon name="Sparkles" size={48} className="mx-auto mb-4 text-white/80" />
                      <p className="text-sm text-white/70">Generate text with RapidDoc</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            </div>
          </div>

          {/* More options to create an eBook - Matching reference design */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">More options to create an eBook</h3>

            <div className="flex space-x-4 overflow-x-auto pb-4">
              {[
                {
                  icon: 'FileText',
                  label: 'Start from scratch',
                  description: '',
                  color: 'bg-blue-50 border-blue-200',
                  iconColor: 'text-blue-500',
                  action: 'start-from-scratch'
                },
                {
                  icon: 'FileText',
                  label: 'Start from template',
                  description: '',
                  color: 'bg-green-50 border-green-200',
                  iconColor: 'text-green-500',
                  action: 'start-from-template'
                },
                {
                  icon: 'FileText',
                  label: 'Import from DOCX',
                  description: '',
                  color: 'bg-orange-50 border-orange-200',
                  iconColor: 'text-orange-500',
                  action: 'import-from-docx'
                },
                // PDF import option - conditionally included based on feature flag
                ...(isFeatureEnabled(FEATURES.PDF_IMPORT) ? [{
                  icon: 'FileText',
                  label: 'Import from PDF',
                  description: '',
                  color: 'bg-blue-50 border-blue-200',
                  iconColor: 'text-blue-500',
                  action: 'import-from-pdf'
                }] : []),
                // {
                //   icon: 'FileText',
                //   label: 'Import from...',
                //   description: '',
                //   color: 'bg-gray-50 border-gray-200',
                //   iconColor: 'text-gray-500',
                //   action: 'import-from-other'
                // }
              ].map((option, index) => (
                <div
                  key={index}
                  className={`flex-shrink-0 w-32 h-32 ${option.color} border-2 border-dashed rounded-lg flex flex-col items-center justify-center p-4 cursor-pointer hover:shadow-md transition-all duration-300 group`}
                  onClick={() => handleEbookOptionClick(option.action)}
                >
                  <Icon name={option.icon} size={24} className={`${option.iconColor} mb-2 group-hover:scale-110 transition-transform`} />
                  <p className="text-xs text-center font-medium text-text-primary leading-tight">{option.label}</p>
                  {option.description && (
                    <p className="text-xs text-center text-text-muted mt-1">{option.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recent Projects - Using real data */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">Recent documents</h3>

            {isLoadingRecent ? (
              <ProjectsLoading count={4} compact={true} />
            ) : recentError ? (
              <ProjectsError error={recentError} onRetry={refetchRecent} compact={true} />
            ) : recentProjects.length === 0 ? (
              <div className="text-center py-12 bg-surface rounded-lg border border-border">
                <div className="text-text-secondary mb-4">
                  <Icon name="FileText" size={48} className="mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">No recent documents</p>
                  <p className="text-sm">Create your first document to get started</p>
                </div>
                <Button onClick={() => navigate('/document-creator')} variant="primary">
                  Create Document
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {recentProjects.map((project) => (
                  <ProjectCard
                    key={project.id}
                    project={project}
                    onClick={handleEditDocument}
                    showActions={false}  // Hide action buttons on dashboard
                    compact={true}       // Use compact mode
                    disableCardClick={false} // Allow click to open project at the right phase
                  />
                ))}
              </div>
            )}
          </div>


        </div>
      </main>

    </div>
  );
};

export default Dashboard;