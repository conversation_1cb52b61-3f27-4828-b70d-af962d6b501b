import React, { useState, useRef, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import CloudSaveIndicator from '../../../components/ui/CloudSaveIndicator';
import { projectsService } from '../../../services/projectsService';
import notificationService, { createSuccessNotification, createErrorNotification } from '../../../services/notificationService';

import { prodLogger } from '../../../utils/prodLogger.js';
/**
 * DocumentInfoHeader - Header component showing document information and action buttons
 * Displays document title, metadata, and provides quick access to key actions like Review, Export, and Zoom
 * Responsive design optimized for mobile, tablet, and desktop views
 */
const DocumentInfoHeader = ({
  documentTitle = 'Untitled Document',
  documentData = null,
  generatedContent = null,
  currentPhase = 'Edit Content',
  onReviewClick = null,
  onExportClick = null,
  onManualSave = null,
  saveStatus = 'saved',
  lastSaved = null,
  className = '',
  // New props for customizing buttons based on phase
  primaryButtonText = null,
  primaryButtonIcon = null,
  primaryButtonAction = null,
  showPrimaryButton = true, // New prop to control primary button visibility
  secondaryButtonText = null,
  secondaryButtonIcon = null,
  secondaryButtonAction = null,
  // Prop to control save status visibility
  showSaveStatus = true,
  // Prop for showing background processing indicator
  isProcessing = false,
  processingMessage = 'Preparing review...',
  // Template navigation props
  onChooseTemplate = null,
  showChooseTemplate = false,
  // Skip template props
  onSkipTemplate = null,
  showSkipTemplate = false,
  // Title update callback
  onTitleUpdate = null
}) => {
  const { documentId } = useParams();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  
  // State for editable title functionality
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editableTitle, setEditableTitle] = useState('');
  const [isSavingTitle, setIsSavingTitle] = useState(false);
  const [originalTitle, setOriginalTitle] = useState('');
  const inputRef = useRef(null);
  
  // Auto-focus and select text when entering edit mode
  useEffect(() => {
    if (isEditingTitle && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditingTitle]);

  // Utility function to format time ago
  const formatTimeAgo = (date) => {
    if (!date) return '';
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 10) return 'just now';
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Get document title from various sources
  const getDocumentTitle = () => {
    if (generatedContent?.title) return generatedContent.title;
    if (documentData?.documentPurpose?.title) return documentData.documentPurpose.title;
    return documentTitle;
  };
  
  // Validate title input
  const validateTitle = (title) => {
    const trimmed = title.trim();
    if (trimmed.length === 0) {
      return { valid: false, error: 'Title cannot be empty' };
    }
    if (trimmed.length > 100) {
      return { valid: false, error: 'Title cannot exceed 100 characters' };
    }
    return { valid: true, error: null };
  };
  
  // Handle title click to enter edit mode
  const handleTitleClick = () => {
    if (!isEditingTitle && !isSavingTitle) {
      const currentTitle = getDocumentTitle();
      setOriginalTitle(currentTitle);
      setEditableTitle(currentTitle);
      setIsEditingTitle(true);
    }
  };
  
  // Handle save title
  const handleSaveTitle = async () => {
    if (!documentId) {
      notificationService.add(createErrorNotification(
        'Save Failed',
        'Unable to save title: Document ID not found'
      ));
      handleCancelEdit();
      return;
    }
    
    const validation = validateTitle(editableTitle);
    if (!validation.valid) {
      notificationService.add(createErrorNotification(
        'Invalid Title',
        validation.error
      ));
      return;
    }
    
    const trimmedTitle = editableTitle.trim();
    
    // Don't save if title hasn't changed
    if (trimmedTitle === originalTitle) {
      setIsEditingTitle(false);
      return;
    }
    
    setIsSavingTitle(true);
    
    try {
      const result = await projectsService.updateProject(documentId, {
        title: trimmedTitle
      });
      
      if (result.success) {
        // Success - notify parent component to update its state
        if (onTitleUpdate) {
          onTitleUpdate(trimmedTitle);
        }
        
        notificationService.add(createSuccessNotification(
          'Title Updated',
          'Document title has been saved successfully',
          { duration: 3000 }
        ));
        setIsEditingTitle(false);
        setOriginalTitle(trimmedTitle);
      } else {
        throw new Error(result.error?.message || 'Failed to update title');
      }
    } catch (error) {
      prodLogger.error('Failed to save document title:', error);
      notificationService.add(createErrorNotification(
        'Save Failed',
        'Failed to save title. Please try again.'
      ));
      // Revert to original title on error
      setEditableTitle(originalTitle);
    } finally {
      setIsSavingTitle(false);
    }
  };
  
  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditingTitle(false);
    setEditableTitle(originalTitle);
  };
  
  // Handle keyboard events
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveTitle();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };
  
  // Handle blur (click outside)
  const handleBlur = () => {
    // Small delay to allow other click handlers to execute first
    setTimeout(() => {
      if (isEditingTitle) {
        handleSaveTitle();
      }
    }, 100);
  };

  // Export action - now opens the export modal instead of direct export
  const handleExportClick = () => {
    prodLogger.debug('Export clicked, onExportClick:', !!onExportClick);
    onExportClick?.(); // Call without format parameter to open modal
  };

  return (
    <div className={`w-full bg-white border-b border-gray-200 shadow-sm ${className}`}>
      <div className="px-4 sm:px-6 lg:px-8 py-3">
        <div className="flex items-center justify-between">
          {/* Left Side - Document Title */}
          <div className="flex-1 min-w-0">
            {isEditingTitle ? (
              <div className="relative">
                <input
                  ref={inputRef}
                  type="text"
                  value={editableTitle}
                  onChange={(e) => setEditableTitle(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onBlur={handleBlur}
                  className="text-lg sm:text-xl font-semibold text-gray-900 bg-white border border-blue-500 rounded px-2 py-1 w-full max-w-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-150"
                  placeholder="Enter document title..."
                  disabled={isSavingTitle}
                  maxLength={100}
                  aria-label="Edit document title"
                />
                {isSavingTitle && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                  </div>
                )}
              </div>
            ) : (
              <div className="relative group">
                <h1 
                  className="text-lg sm:text-xl font-semibold text-gray-900 truncate cursor-text hover:bg-gray-50 hover:border hover:border-dashed hover:border-gray-300 rounded px-2 py-1 transition-all duration-150 select-none pr-8"
                  onClick={handleTitleClick}
                  title="Click to edit title"
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleTitleClick();
                    }
                  }}
                  aria-label="Document title - click to edit"
                >
                  {getDocumentTitle()}
                </h1>
                {/* Edit icon appears on hover */}
                <div className="absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-60 transition-opacity duration-150 pointer-events-none">
                  <Icon name="Edit2" size={14} className="text-gray-500" />
                </div>
              </div>
            )}
            {/* Document metadata - hidden on mobile */}
            <div className="hidden sm:flex items-center space-x-4 mt-1 text-sm text-gray-500">
              {generatedContent?.metadata?.generatedAt && (
                <span className="flex items-center space-x-1">
                  <Icon name="Calendar" size={14} />
                  <span>{new Date(generatedContent.metadata.generatedAt).toLocaleDateString()}</span>
                </span>
              )}
            </div>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-2 ml-4">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-2">
              {/* Auto-save status indicator - only show if showSaveStatus is true */}
              {showSaveStatus && (
                <div className="flex items-center space-x-2 mr-2">
                  <CloudSaveIndicator
                    size="small"
                    showCheckmark={saveStatus === 'saved' && !isProcessing}
                    title={
                      isProcessing ? processingMessage :
                      saveStatus === 'saving' ? 'Saving document...' :
                      saveStatus === 'saved' ? 'Document saved' :
                      'Save failed - please try again'
                    }
                    color="text-gray-600"
                  />
                  {isProcessing && (
                    <span className="text-xs text-gray-500 flex items-center">
                      <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full mr-1"></div>
                      {processingMessage}
                    </span>
                  )}
                  {!isProcessing && saveStatus === 'saved' && lastSaved && (
                    <span className="text-xs text-gray-500">
                      Saved {formatTimeAgo(lastSaved)}
                    </span>
                  )}
                  {!isProcessing && saveStatus === 'saving' && (
                    <span className="text-xs text-gray-500">
                      Saving...
                    </span>
                  )}
                  {!isProcessing && saveStatus === 'error' && (
                    <span className="text-xs text-gray-500">
                      Save failed
                    </span>
                  )}
                </div>
              )}

              {/* Manual Save Button - Only show in Edit Content phase */}
              {currentPhase === 'Edit Content' && onManualSave && (
                <Button
                  variant={saveStatus === 'error' ? 'outline' : 'ghost'}
                  onClick={onManualSave}
                  disabled={saveStatus === 'saving'}
                  className={`px-4 py-2.5 text-sm font-medium touch-manipulation transition-all duration-200 ${
                    saveStatus === 'error' ? 'border-red-300 text-red-600 hover:bg-red-50' :
                    saveStatus === 'saved' ? 'text-green-600 hover:bg-green-50' :
                    'hover:bg-gray-50'
                  }`}
                  title={
                    saveStatus === 'saving' ? 'Saving document...' :
                    saveStatus === 'error' ? 'Save failed - click to retry' :
                    saveStatus === 'saved' ? 'Document saved - click to save again' :
                    'Save document'
                  }
                >
                  <Icon
                    name={
                      saveStatus === 'saving' ? "Loader2" :
                      saveStatus === 'error' ? "AlertCircle" :
                      "Save"
                    }
                    size={16}
                    className={`mr-2 ${saveStatus === 'saving' ? 'animate-spin' : ''}`}
                  />
                  {
                    saveStatus === 'saving' ? 'Saving...' :
                    saveStatus === 'error' ? 'Retry' :
                    'Save'
                  }
                </Button>
              )}

              {/* Secondary Action Button (Back/Navigation) */}
              {secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={secondaryButtonAction}
                  className="px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} className="mr-2" />
                  {secondaryButtonText || "Back"}
                </Button>
              )}

              {/* Primary Action Button - Hide Review button when already in Review phase */}
              {currentPhase !== 'Review' && (
                <Button
                  onClick={primaryButtonAction || onReviewClick}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={primaryButtonIcon || "Eye"} size={16} className="mr-2" />
                  {primaryButtonText || "Review"}
                </Button>
              )}

              {/* Choose Template Button (when review is complete) */}
              {showChooseTemplate && onChooseTemplate && (
                <Button
                  variant="primary"
                  onClick={onChooseTemplate}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <Icon name="Layout" size={16} className="mr-2" />
                  Choose Template
                </Button>
              )}

              {/* Export Button (fallback when no secondary action and no template buttons) */}
              {!secondaryButtonAction && !showChooseTemplate && (
                <Button
                  variant="outline"
                  onClick={handleExportClick}
                  className="px-3 py-2 text-sm font-medium"
                >
                  <Icon name="Download" size={16} className="mr-2" />
                  Export
                </Button>
              )}

            </div>

            {/* Tablet Actions - Touch-friendly sizing */}
            <div className="hidden sm:flex md:hidden items-center space-x-2">
              {/* Auto-save status indicator for tablet - only show if showSaveStatus is true */}
              {showSaveStatus && (
                <div className="flex items-center space-x-2 mr-2">
                  <CloudSaveIndicator
                    size="small"
                    showCheckmark={saveStatus === 'saved'}
                    title={
                      saveStatus === 'saving' ? 'Saving document...' :
                      saveStatus === 'saved' ? 'Document saved' :
                      'Save failed - please try again'
                    }
                    color="text-gray-600"
                  />
                </div>
              )}

              {/* Manual Save Button - Only show in Edit Content phase (Tablet) */}
              {currentPhase === 'Edit Content' && onManualSave && (
                <Button
                  variant={saveStatus === 'error' ? 'outline' : 'ghost'}
                  onClick={onManualSave}
                  disabled={saveStatus === 'saving'}
                  className={`px-4 py-2.5 text-sm font-medium touch-manipulation transition-all duration-200 ${
                    saveStatus === 'error' ? 'border-red-300 text-red-600 hover:bg-red-50' :
                    saveStatus === 'saved' ? 'text-green-600 hover:bg-green-50' :
                    'hover:bg-gray-50'
                  }`}
                  title={
                    saveStatus === 'saving' ? 'Saving document...' :
                    saveStatus === 'error' ? 'Save failed - click to retry' :
                    saveStatus === 'saved' ? 'Document saved - click to save again' :
                    'Save document'
                  }
                >
                  <Icon
                    name={
                      saveStatus === 'saving' ? "Loader2" :
                      saveStatus === 'error' ? "AlertCircle" :
                      "Save"
                    }
                    size={16}
                    className={saveStatus === 'saving' ? 'animate-spin' : ''}
                  />
                </Button>
              )}

              {/* Secondary Action Button (Back/Navigation) */}
              {secondaryButtonAction && (
                <Button
                  variant="outline"
                  onClick={secondaryButtonAction}
                  className="px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} />
                </Button>
              )}

              {/* Primary Action Button - Hide Review button when already in Review phase */}
              {currentPhase !== 'Review' && (
                <Button
                  onClick={primaryButtonAction || onReviewClick}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 text-sm font-medium touch-manipulation"
                >
                  <Icon name={primaryButtonIcon || "Eye"} size={16} />
                </Button>
              )}

              {/* Template Action Buttons (when review is complete) */}
              {showChooseTemplate && (
                <div className="flex items-center space-x-2">
                  {/* Choose Template Button */}
                  {onChooseTemplate && (
                    <Button
                      variant="primary"
                      onClick={onChooseTemplate}
                      className="px-4 py-2.5 touch-manipulation"
                      title="Select a template for your document"
                    >
                      <Icon name="Layout" size={16} />
                    </Button>
                  )}
                </div>
              )}

              {/* Export Button (fallback when no secondary action and no template buttons) */}
              {!secondaryButtonAction && !showChooseTemplate && (
                <Button
                  variant="outline"
                  onClick={handleExportClick}
                  className="px-4 py-2.5 touch-manipulation"
                >
                  <Icon name="Download" size={16} />
                </Button>
              )}

            </div>

            {/* Mobile Menu Button - Touch-friendly */}
            <div className="sm:hidden relative">
              <Button
                variant="ghost"
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="p-3 touch-manipulation"
                aria-label="More options"
              >
                <Icon name="MoreVertical" size={20} />
              </Button>

              {/* Mobile Menu - Overlay dropdown */}
              {showMobileMenu && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-1100">
                  <div className="flex flex-col">
                    {/* Manual Save Button - Only show in Edit Content phase (Mobile) */}
                    {currentPhase === 'Edit Content' && onManualSave && (
                      <button
                        onClick={() => {
                          onManualSave?.();
                          setShowMobileMenu(false);
                        }}
                        disabled={saveStatus === 'saving'}
                        className={`w-full text-left px-3 py-2.5 text-sm flex items-center touch-manipulation disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${
                          saveStatus === 'error' ? 'text-red-600 hover:bg-red-50' :
                          saveStatus === 'saved' ? 'text-green-600 hover:bg-green-50' :
                          'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Icon
                          name={
                            saveStatus === 'saving' ? "Loader2" :
                            saveStatus === 'error' ? "AlertCircle" :
                            "Save"
                          }
                          size={16}
                          className={`mr-3 ${saveStatus === 'saving' ? 'animate-spin' : ''}`}
                        />
                        {
                          saveStatus === 'saving' ? 'Saving...' :
                          saveStatus === 'error' ? 'Retry Save' :
                          'Save'
                        }
                      </button>
                    )}

                    {/* Secondary Action Button (Back/Navigation) */}
                    {secondaryButtonAction && (
                      <button
                        onClick={() => {
                          secondaryButtonAction?.();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center touch-manipulation"
                      >
                        <Icon name={secondaryButtonIcon || "ArrowLeft"} size={16} className="mr-3" />
                        {secondaryButtonText || "Back"}
                      </button>
                    )}

                    {/* Primary Action Button - Hide Review button when already in Review phase */}
                    {currentPhase !== 'Review' && (
                      <button
                        onClick={() => {
                          (primaryButtonAction || onReviewClick)?.();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-blue-600 hover:bg-blue-50 flex items-center touch-manipulation"
                      >
                        <Icon name={primaryButtonIcon || "Eye"} size={16} className="mr-3" />
                        {primaryButtonText || "Review"}
                      </button>
                    )}

                    {/* Choose Template Button (when review is complete) */}
                    {showChooseTemplate && onChooseTemplate && (
                      <button
                        onClick={() => {
                          onChooseTemplate();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-blue-700 hover:bg-blue-50 flex items-center touch-manipulation font-medium"
                      >
                        <Icon name="Layout" size={16} className="mr-3" />
                        Choose Template
                      </button>
                    )}

                    {/* Export Button (fallback when no secondary action and no template buttons) */}
                    {!secondaryButtonAction && !showChooseTemplate && (
                      <button
                        onClick={() => {
                          handleExportClick();
                          setShowMobileMenu(false);
                        }}
                        className="w-full text-left px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 flex items-center touch-manipulation"
                      >
                        <Icon name="Download" size={16} className="mr-3" />
                        Export
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Backdrop overlay to close mobile menu */}
        {showMobileMenu && (
          <div
            className="fixed inset-0 z-1090"
            onClick={() => setShowMobileMenu(false)}
          />
        )}
      </div>
    </div>
  );
};

export default DocumentInfoHeader;
