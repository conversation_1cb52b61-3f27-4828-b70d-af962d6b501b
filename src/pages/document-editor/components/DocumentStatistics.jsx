import React from 'react';
import Icon from '../../../components/AppIcon';
import { calculateComprehensiveStatistics } from '../../../services/documentStatisticsService';

/**
 * DocumentStatistics - Component to display document statistics and metrics
 * Shows word counts, reading time, structure info, and other metrics
 */
const DocumentStatistics = ({ 
  generatedContent, 
  validationResults = null,
  editorContent = null,
  className = '' 
}) => {
  if (!generatedContent) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="text-center py-4">
          <Icon name="BarChart3" size={24} className="text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No document data available</p>
        </div>
      </div>
    );
  }

  // Calculate statistics using the comprehensive service
  const stats = calculateComprehensiveStatistics(generatedContent, editorContent);
  const validationMetrics = validationResults?.details || {};

  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const StatCard = ({ icon, label, value, subtext = null, color = 'text-gray-600' }) => (
    <div className="bg-gray-50 rounded-lg p-3">
      <div className="flex items-center space-x-2 mb-1">
        <Icon name={icon} size={16} className={color} />
        <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">{label}</span>
      </div>
      <div className="text-lg font-semibold text-gray-900">{value}</div>
      {subtext && <div className="text-xs text-gray-500">{subtext}</div>}
    </div>
  );

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Icon name="BarChart3" size={20} className="text-primary" />
          <h3 className="text-lg font-semibold text-gray-900">Document Statistics</h3>
        </div>
      </div>

      {/* Main Statistics Grid */}
      <div className="p-4 space-y-4">
        {/* Word Count & Reading Time */}
        <div className="grid grid-cols-2 gap-3">
          <StatCard
            icon="FileText"
            label="Total Words"
            value={formatNumber(stats.words)}
            subtext={`${stats.characters} characters`}
            color="text-blue-600"
          />
          <StatCard
            icon="Clock"
            label="Reading Time"
            value={formatDuration(stats.readTime)}
            subtext={`200 WPM`}
            color="text-green-600"
          />
        </div>

        {/* Structure */}
        <div className="grid grid-cols-2 gap-3">
          <StatCard
            icon="List"
            label="Chapters"
            value={stats.chapters}
            subtext={stats.chapters > 0 ? `Avg ${formatNumber(Math.round(stats.words / stats.chapters))} words/chapter` : 'No chapters'}
            color="text-purple-600"
          />
          <StatCard
            icon="Hash"
            label="Sections"
            value={stats.sections}
            subtext="Including intro & conclusion"
            color="text-orange-600"
          />
        </div>

        {/* Content Analysis */}
        <div className="grid grid-cols-2 gap-3">
          <StatCard
            icon="MessageSquare"
            label="Paragraphs"
            value={stats.paragraphs || 'N/A'}
            subtext={stats.paragraphs > 0 ? `Avg ${Math.round(stats.words / stats.paragraphs)} words/paragraph` : 'No paragraphs'}
            color="text-indigo-600"
          />
          <StatCard
            icon="Type"
            label="Sentences"
            value={stats.sentences || 'N/A'}
            subtext={stats.sentences > 0 ? `Avg ${Math.round(stats.words / stats.sentences)} words/sentence` : 'No sentences'}
            color="text-pink-600"
          />
        </div>

        {/* Quality Metrics (if validation results available) */}
        {validationResults && (
          <>
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Quality Metrics</h4>
              <div className="grid grid-cols-2 gap-3">
                {validationMetrics.content?.metrics?.readabilityScore && (
                  <StatCard
                    icon="Eye"
                    label="Readability"
                    value={`${validationMetrics.content.metrics.readabilityScore}/100`}
                    subtext="Flesch Reading Ease"
                    color="text-teal-600"
                  />
                )}
                {validationMetrics.structure?.metrics?.totalWords && (
                  <StatCard
                    icon="Target"
                    label="Completion"
                    value={`${Math.round((stats.words / 2000) * 100)}%`}
                    subtext="Based on 2000 word target"
                    color="text-emerald-600"
                  />
                )}
              </div>
            </div>
          </>
        )}

        {/* Document Breakdown */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Content Breakdown</h4>
          <div className="space-y-2">
            {/* Show content source */}
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Content Source</span>
              <span className="font-medium capitalize">{stats.source}</span>
            </div>
            
            {/* Show estimated pages */}
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600">Estimated Pages</span>
              <span className="font-medium">{stats.pages} pages</span>
            </div>
          </div>
        </div>

        {/* Export Information */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Estimated Export Sizes</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-sm font-medium text-gray-900">{stats.pages}</div>
              <div className="text-xs text-gray-500">Pages (PDF)</div>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded">
              <div className="text-sm font-medium text-gray-900">{Math.round(stats.characters / 1024)}KB</div>
              <div className="text-xs text-gray-500">File Size</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentStatistics;
