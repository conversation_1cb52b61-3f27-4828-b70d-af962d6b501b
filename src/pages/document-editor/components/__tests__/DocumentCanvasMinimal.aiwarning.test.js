/**
 * Tests for AI Warning conditional display in DocumentCanvasMinimal
 * Tests that AI warnings only show for AI-generated content (baseline === 'docgenerate')
 */

import React from "react";
import { render, screen } from "@testing-library/react";
import DocumentCanvasMinimal from "../DocumentCanvasMinimal";

// Mock the required dependencies
jest.mock("../../../contexts/AuthContext", () => ({
  useAuth: () => ({ user: { id: "test-user" } }),
}));

jest.mock("../../../services/usageTrackingService", () => ({
  default: {
    trackImageGeneration: jest.fn(),
    trackImageUpload: jest.fn(),
  },
}));

jest.mock("../../../utils/prodLogger.js", () => ({
  prodLogger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock all the complex editor dependencies
jest.mock("@tiptap/react", () => ({
  EditorContent: ({ editor }) => (
    <div data-testid="editor-content">Editor Content</div>
  ),
  useEditor: () => ({
    on: jest.fn(),
    off: jest.fn(),
    commands: {
      focus: jest.fn(),
      setContent: jest.fn(),
    },
    getHTML: () => "<p>Test content</p>",
  }),
  ReactNodeViewRenderer: jest.fn(),
}));

jest.mock("@tiptap/starter-kit", () => jest.fn());
jest.mock("@tiptap/extension-placeholder", () => jest.fn());

// Mock other components
jest.mock("../ImageUrlInput", () => () => (
  <div data-testid="image-url-input" />
));
jest.mock("../ContextualImageSelectionModal", () => () => (
  <div data-testid="contextual-image-modal" />
));
jest.mock("../ImageImportModal", () => () => (
  <div data-testid="image-import-modal" />
));
jest.mock("../../../components/modals/ContentPreviewModal.jsx", () => () => (
  <div data-testid="content-preview-modal" />
));
jest.mock("../ToastNotification.jsx", () => () => (
  <div data-testid="toast-notification" />
));
jest.mock("../ImageSuggestionCardExtension", () => ({
  ImageSuggestionCardExtension: jest.fn(),
}));
jest.mock("../EnhancedImageExtension", () => ({
  EnhancedImageExtension: jest.fn(),
}));
jest.mock("../AIImageGenerationExtension", () => ({
  AIImageGenerationExtension: jest.fn(),
}));
jest.mock("../../../hooks/useScrollAwarePosition", () => () => ({
  position: { top: 0, left: 0 },
  isVisible: true,
}));
jest.mock("../../../components/AppIcon", () => ({ icon }) => (
  <span data-testid={`icon-${icon}`} />
));

describe("DocumentCanvasMinimal AI Warning", () => {
  const defaultProps = {
    content: { chapters: [{ title: "Test Chapter", content: "Test content" }] },
    onContentChange: jest.fn(),
    isLoading: false,
    isReadOnly: false,
    imageSuggestions: {},
    onOpenImageModal: jest.fn(),
    onEditorReady: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("shows AI warning for AI-generated content (docgenerate baseline)", () => {
    render(<DocumentCanvasMinimal {...defaultProps} baseline="docgenerate" />);

    expect(
      screen.getByText(
        /This content was generated by AI and may contain errors/
      )
    ).toBeInTheDocument();
  });

  test("hides AI warning for start from scratch baseline", () => {
    render(<DocumentCanvasMinimal {...defaultProps} baseline="scratch" />);

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning for DOCX import baseline", () => {
    render(<DocumentCanvasMinimal {...defaultProps} baseline="import-docx" />);

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning for PDF import baseline", () => {
    render(<DocumentCanvasMinimal {...defaultProps} baseline="import-pdf" />);

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning when no baseline provided", () => {
    render(<DocumentCanvasMinimal {...defaultProps} baseline={null} />);

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning when undefined baseline", () => {
    render(<DocumentCanvasMinimal {...defaultProps} baseline={undefined} />);

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning when in read-only mode (even for AI content)", () => {
    render(
      <DocumentCanvasMinimal
        {...defaultProps}
        baseline="docgenerate"
        isReadOnly={true}
      />
    );

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning when loading (even for AI content)", () => {
    render(
      <DocumentCanvasMinimal
        {...defaultProps}
        baseline="docgenerate"
        isLoading={true}
      />
    );

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });

  test("hides AI warning when no content (even for AI baseline)", () => {
    render(
      <DocumentCanvasMinimal
        {...defaultProps}
        baseline="docgenerate"
        content={null}
      />
    );

    expect(
      screen.queryByText(
        /This content was generated by AI and may contain errors/
      )
    ).not.toBeInTheDocument();
  });
});
