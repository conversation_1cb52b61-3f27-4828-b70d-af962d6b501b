import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DocumentCanvasMinimal from '../DocumentCanvasMinimal';

// Mock TipTap editor
const mockEditor = {
  commands: {
    setContent: jest.fn(),
    focus: jest.fn(),
    setTextSelection: jest.fn(),
  },
  state: {
    selection: {
      from: 0,
      to: 0,
      empty: true,
    },
    doc: {
      content: { size: 100 },
    },
  },
  isFocused: false,
  isEditable: true,
  getHTML: jest.fn(() => '<p>Test content</p>'),
  on: jest.fn(),
  off: jest.fn(),
  storage: {},
};

// Mock useEditor hook
jest.mock('@tiptap/react', () => ({
  useEditor: jest.fn(() => mockEditor),
  EditorContent: ({ editor, ...props }) => (
    <div data-testid="editor-content" {...props}>
      {editor ? 'Editor loaded' : 'Loading...'}
    </div>
  ),
}));

// Mock content converter
jest.mock('../../../../utils/contentConverter', () => ({
  convertAIContentToHTML: jest.fn((content) => `<h1>${content.title}</h1><p>Generated content</p>`),
  removeImageSuggestionCards: jest.fn((html) => html),
}));

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('DocumentCanvasMinimal - Cursor Positioning', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial Content Loading', () => {
    test('should position cursor at start of document after initial content load', async () => {
      const mockContent = {
        title: 'Test Document',
        chapters: [
          {
            title: 'Chapter 1',
            content: 'This is test content'
          }
        ]
      };

      render(
        <DocumentCanvasMinimal
          content={mockContent}
          onContentChange={jest.fn()}
          isLoading={false}
        />
      );

      // Wait for the content to be set and cursor positioning to occur
      await waitFor(() => {
        expect(mockEditor.commands.setContent).toHaveBeenCalled();
      }, { timeout: 3000 });

      // Verify that focus('start') was called to position cursor at beginning
      await waitFor(() => {
        expect(mockEditor.commands.focus).toHaveBeenCalledWith('start');
      }, { timeout: 1000 });
    });

    test('should fallback to regular focus if start positioning fails', async () => {
      // Mock focus('start') to throw an error
      mockEditor.commands.focus.mockImplementation((position) => {
        if (position === 'start') {
          throw new Error('Start positioning failed');
        }
      });

      const mockContent = {
        title: 'Test Document',
        chapters: [{ title: 'Chapter 1', content: 'Test content' }]
      };

      render(
        <DocumentCanvasMinimal
          content={mockContent}
          onContentChange={jest.fn()}
          isLoading={false}
        />
      );

      await waitFor(() => {
        expect(mockEditor.commands.setContent).toHaveBeenCalled();
      });

      // Should attempt start positioning first, then fallback to regular focus
      await waitFor(() => {
        expect(mockEditor.commands.focus).toHaveBeenCalledWith('start');
        expect(mockEditor.commands.focus).toHaveBeenCalledWith(); // Fallback call
      });
    });
  });

  describe('Image Generation Cursor Positioning', () => {
    test('should preserve cursor position during image insertion when no saved position', async () => {
      // Mock editor with current selection
      mockEditor.state.selection = {
        from: 50,
        to: 50,
        empty: true,
      };
      mockEditor.isFocused = true;

      render(
        <DocumentCanvasMinimal
          content={{ title: 'Test', chapters: [] }}
          onContentChange={jest.fn()}
          isLoading={false}
        />
      );

      // Simulate image insertion (this would normally be triggered by user action)
      // The component should preserve the current cursor position
      expect(mockEditor.commands.focus).not.toHaveBeenCalledWith('end');
    });

    test('should only move to end as last resort when cursor is at position 0', async () => {
      // Mock editor with cursor at beginning (position 0)
      mockEditor.state.selection = {
        from: 0,
        to: 0,
        empty: true,
      };
      mockEditor.isFocused = false;

      const component = render(
        <DocumentCanvasMinimal
          content={{ title: 'Test', chapters: [] }}
          onContentChange={jest.fn()}
          isLoading={false}
        />
      );

      // This test verifies the logic in handleInsertGeneratedImage
      // When cursor is at position 0 and editor is not focused, it should move to end
      // But in normal editing scenarios, it should preserve position
    });
  });

  describe('Content Update Scenarios', () => {
    test('should not reposition cursor when content has editorHTML (user has made edits)', async () => {
      const mockContentWithEdits = {
        title: 'Test Document',
        chapters: [{ title: 'Chapter 1', content: 'Test content' }],
        editorHTML: '<p>User edited content</p>' // This indicates user has made edits
      };

      render(
        <DocumentCanvasMinimal
          content={mockContentWithEdits}
          onContentChange={jest.fn()}
          isLoading={false}
        />
      );

      // Should not call setContent when editorHTML exists
      await waitFor(() => {
        expect(mockEditor.commands.setContent).not.toHaveBeenCalled();
      });

      // Should not reposition cursor
      expect(mockEditor.commands.focus).not.toHaveBeenCalledWith('start');
    });

    test('should handle read-only mode without cursor positioning', async () => {
      const mockContent = {
        title: 'Test Document',
        chapters: [{ title: 'Chapter 1', content: 'Test content' }]
      };

      render(
        <DocumentCanvasMinimal
          content={mockContent}
          onContentChange={jest.fn()}
          isLoading={false}
          isReadOnly={true}
        />
      );

      await waitFor(() => {
        expect(mockEditor.commands.setContent).toHaveBeenCalled();
      });

      // In read-only mode, cursor positioning should still work for initial load
      await waitFor(() => {
        expect(mockEditor.commands.focus).toHaveBeenCalledWith('start');
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle setContent errors gracefully', async () => {
      mockEditor.commands.setContent.mockImplementation(() => {
        throw new Error('setContent failed');
      });

      const mockContent = {
        title: 'Test Document',
        chapters: [{ title: 'Chapter 1', content: 'Test content' }]
      };

      // Should not crash when setContent fails
      expect(() => {
        render(
          <DocumentCanvasMinimal
            content={mockContent}
            onContentChange={jest.fn()}
            isLoading={false}
          />
        );
      }).not.toThrow();
    });
  });
});
