import React, { useState, useEffect } from 'react';

/**
 * FloatingTableOfContents - Floating navigation panel for document structure
 * Provides quick navigation to different sections of the document
 */
const FloatingTableOfContents = ({ 
  generatedContent, 
  isVisible = false, 
  onToggle 
}) => {
  const [activeSection, setActiveSection] = useState(null);

  // Track active section based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const sections = ['introduction', ...generatedContent?.chapters?.map(ch => ch.id) || [], 'conclusion'];
      
      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [generatedContent]);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  if (!generatedContent) return null;

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="fixed top-1/2 right-4 transform -translate-y-1/2 z-40 w-10 h-10 bg-white border border-gray-300 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center text-gray-600 hover:text-gray-800"
        title="Table of Contents"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
        </svg>
      </button>

      {/* Table of Contents Panel */}
      {isVisible && (
        <div className="fixed top-1/2 right-16 transform -translate-y-1/2 z-30 w-80 bg-white border border-gray-200 rounded-lg shadow-xl max-h-96 overflow-y-auto">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Table of Contents</h3>
              <button
                onClick={onToggle}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Navigation Items */}
          <div className="p-2">
            {/* Introduction */}
            <button
              onClick={() => scrollToSection('introduction')}
              className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                activeSection === 'introduction'
                  ? 'bg-primary/10 text-primary border-l-2 border-primary'
                  : 'hover:bg-gray-50 text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activeSection === 'introduction' ? 'bg-primary' : 'bg-blue-400'
                }`}></div>
                <div>
                  <div className="font-medium">Introduction</div>
                  <div className="text-xs text-gray-500">
                    {generatedContent.introduction?.wordCount || 0} words
                  </div>
                </div>
              </div>
            </button>

            {/* Chapters */}
            {generatedContent.chapters?.map((chapter) => (
              <button
                key={chapter.id}
                onClick={() => scrollToSection(chapter.id)}
                className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                  activeSection === chapter.id
                    ? 'bg-primary/10 text-primary border-l-2 border-primary'
                    : 'hover:bg-gray-50 text-gray-700'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activeSection === chapter.id ? 'bg-primary' : 'bg-primary/60'
                  }`}></div>
                  <div>
                    <div className="font-medium">
                      {chapter.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {chapter.wordCount || 0} words • {chapter.sections?.length || 0} sections
                    </div>
                  </div>
                </div>
              </button>
            ))}

            {/* Conclusion */}
            <button
              onClick={() => scrollToSection('conclusion')}
              className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                activeSection === 'conclusion'
                  ? 'bg-primary/10 text-primary border-l-2 border-primary'
                  : 'hover:bg-gray-50 text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activeSection === 'conclusion' ? 'bg-primary' : 'bg-green-400'
                }`}></div>
                <div>
                  <div className="font-medium">Conclusion</div>
                  <div className="text-xs text-gray-500">
                    {generatedContent.conclusion?.wordCount || 0} words
                  </div>
                </div>
              </div>
            </button>
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="text-xs text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>Total Words:</span>
                <span className="font-medium">{generatedContent.wordCount?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Reading Time:</span>
                <span className="font-medium">{generatedContent.estimatedReadingTime}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isVisible && (
        <div 
          className="fixed inset-0 z-20 bg-black bg-opacity-10"
          onClick={onToggle}
        />
      )}
    </>
  );
};

export default FloatingTableOfContents;
