import React, { useState, useRef, useEffect } from 'react';
import { formatOverlayDisplayName } from '../../../utils/overlayUtils';
import { prodLogger } from '../../../utils/prodLogger.js';

/**
 * Logo Overlay Editor Component
 * Visual editor for positioning and styling logo overlays on template backgrounds
 * Follows the same patterns as TextOverlayEditor but adapted for logo properties
 */
const LogoOverlayEditor = ({ template, onTemplateChange }) => {
  const canvasRef = useRef(null);
  const [selectedOverlay, setSelectedOverlay] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [backgroundImage, setBackgroundImage] = useState(null);
  const [scale, setScale] = useState(1);
  const [validationErrors, setValidationErrors] = useState({});

  // Initialize logo_overlays if it doesn't exist
  const logoOverlays = template?.logo_overlays?.overlays || [];

  // Validation functions
  const validateOverlay = (overlay, index, allOverlays) => {
    const errors = {};

    // ID validation
    if (!overlay.id || overlay.id.trim() === '') {
      errors.id = 'ID is required';
    } else if (allOverlays.some((o, i) => i !== index && o.id === overlay.id)) {
      errors.id = 'ID must be unique';
    }

    // Position validation
    if (overlay.position.x < 0) {
      errors.x = 'X position cannot be negative';
    }
    if (overlay.position.y < 0) {
      errors.y = 'Y position cannot be negative';
    }
    if (backgroundImage) {
      if (overlay.position.x + overlay.position.width > backgroundImage.width) {
        errors.x = 'Logo extends beyond image width';
      }
      if (overlay.position.y + overlay.position.height > backgroundImage.height) {
        errors.y = 'Logo extends beyond image height';
      }
    }

    // Size validation
    if (overlay.position.width <= 0) {
      errors.width = 'Width must be positive';
    }
    if (overlay.position.height <= 0) {
      errors.height = 'Height must be positive';
    }
    if (overlay.position.width < 10) {
      errors.width = 'Width should be at least 10px';
    }
    if (overlay.position.height < 10) {
      errors.height = 'Height should be at least 10px';
    }

    // Placeholder validation
    if (!overlay.placeholder || !overlay.placeholder.match(/^{{[a-zA-Z_][a-zA-Z0-9_]*}}$/)) {
      errors.placeholder = 'Placeholder must be in format {{variable_name}}';
    }

    // Styling validation
    if (overlay.styling?.opacity < 0 || overlay.styling?.opacity > 1) {
      errors.opacity = 'Opacity must be between 0 and 1';
    }
    if (overlay.styling?.borderRadius < 0) {
      errors.borderRadius = 'Border radius cannot be negative';
    }

    return errors;
  };

  // Update validation when overlays change
  useEffect(() => {
    const newErrors = {};
    logoOverlays.forEach((overlay, index) => {
      const overlayErrors = validateOverlay(overlay, index, logoOverlays);
      if (Object.keys(overlayErrors).length > 0) {
        newErrors[index] = overlayErrors;
      }
    });
    setValidationErrors(newErrors);
  }, [logoOverlays, backgroundImage]);

  // Load background image
  useEffect(() => {
    if (template?.background_image_url) {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        setBackgroundImage(img);
        drawCanvas();
      };
      img.src = template.background_image_url;
    }
  }, [template?.background_image_url]);

  // Redraw canvas when overlays change
  useEffect(() => {
    drawCanvas();
  }, [logoOverlays, selectedOverlay, backgroundImage]);

  const drawCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas || !backgroundImage) return;

    const ctx = canvas.getContext('2d');
    const canvasWidth = 600;
    const canvasHeight = 400;

    // Calculate scale to fit background image
    const scaleX = canvasWidth / backgroundImage.width;
    const scaleY = canvasHeight / backgroundImage.height;
    const newScale = Math.min(scaleX, scaleY);
    setScale(newScale);

    // Clear canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Draw background image
    const scaledWidth = backgroundImage.width * newScale;
    const scaledHeight = backgroundImage.height * newScale;
    const offsetX = (canvasWidth - scaledWidth) / 2;
    const offsetY = (canvasHeight - scaledHeight) / 2;

    ctx.drawImage(backgroundImage, offsetX, offsetY, scaledWidth, scaledHeight);

    // Draw logo overlay rectangles
    logoOverlays.forEach((overlay, index) => {
      const x = offsetX + (overlay.position.x * newScale);
      const y = offsetY + (overlay.position.y * newScale);
      const width = overlay.position.width * newScale;
      const height = overlay.position.height * newScale;

      // Draw overlay rectangle
      ctx.strokeStyle = selectedOverlay === index ? '#3B82F6' : '#10B981';
      ctx.lineWidth = selectedOverlay === index ? 3 : 2;
      ctx.setLineDash(selectedOverlay === index ? [] : [5, 5]);
      ctx.strokeRect(x, y, width, height);

      // Draw overlay label
      ctx.fillStyle = selectedOverlay === index ? '#3B82F6' : '#10B981';
      ctx.font = '12px Arial';
      ctx.fillText(formatOverlayDisplayName(overlay.id), x, y - 5);

      // Draw logo placeholder icon
      ctx.fillStyle = 'rgba(16, 185, 129, 0.2)';
      ctx.fillRect(x, y, width, height);
      
      // Draw logo icon
      ctx.fillStyle = '#10B981';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('🖼️', x + width/2, y + height/2 + 6);
      ctx.textAlign = 'left';
    });
  };

  const getMousePosition = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const canvasWidth = 600;
    const canvasHeight = 400;
    
    const scaleX = canvasWidth / rect.width;
    const scaleY = canvasHeight / rect.height;
    
    return {
      x: (e.clientX - rect.left) * scaleX,
      y: (e.clientY - rect.top) * scaleY
    };
  };

  const handleMouseDown = (e) => {
    const mousePos = getMousePosition(e);
    const canvasWidth = 600;
    const canvasHeight = 400;
    
    if (!backgroundImage) return;

    const scaledWidth = backgroundImage.width * scale;
    const scaledHeight = backgroundImage.height * scale;
    const offsetX = (canvasWidth - scaledWidth) / 2;
    const offsetY = (canvasHeight - scaledHeight) / 2;

    // Check if clicking on an existing overlay
    for (let i = logoOverlays.length - 1; i >= 0; i--) {
      const overlay = logoOverlays[i];
      const x = offsetX + (overlay.position.x * scale);
      const y = offsetY + (overlay.position.y * scale);
      const width = overlay.position.width * scale;
      const height = overlay.position.height * scale;

      if (mousePos.x >= x && mousePos.x <= x + width &&
          mousePos.y >= y && mousePos.y <= y + height) {
        setSelectedOverlay(i);
        setIsDragging(true);
        setDragStart({
          x: mousePos.x - x,
          y: mousePos.y - y
        });
        return;
      }
    }

    // If not clicking on an overlay, deselect
    setSelectedOverlay(null);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || selectedOverlay === null || !backgroundImage) return;

    const mousePos = getMousePosition(e);
    const canvasWidth = 600;
    const canvasHeight = 400;
    
    const scaledWidth = backgroundImage.width * scale;
    const scaledHeight = backgroundImage.height * scale;
    const offsetX = (canvasWidth - scaledWidth) / 2;
    const offsetY = (canvasHeight - scaledHeight) / 2;

    // Calculate new position in original image coordinates
    const newX = Math.max(0, Math.min(
      (mousePos.x - offsetX - dragStart.x) / scale,
      backgroundImage.width - logoOverlays[selectedOverlay].position.width
    ));
    const newY = Math.max(0, Math.min(
      (mousePos.y - offsetY - dragStart.y) / scale,
      backgroundImage.height - logoOverlays[selectedOverlay].position.height
    ));

    updateOverlayProperty(selectedOverlay, 'position.x', Math.round(newX));
    updateOverlayProperty(selectedOverlay, 'position.y', Math.round(newY));
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add mouse event listeners
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);

    return () => {
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, selectedOverlay, backgroundImage, scale, dragStart]);

  const updateOverlayProperty = (overlayIndex, property, value) => {
    const updatedOverlays = [...logoOverlays];

    if (property.includes('.')) {
      const [parent, child] = property.split('.');
      updatedOverlays[overlayIndex][parent][child] = value;
    } else {
      updatedOverlays[overlayIndex][property] = value;
    }

    // Apply constraints for certain properties
    if (property === 'position.x' && backgroundImage) {
      value = Math.max(0, Math.min(value, backgroundImage.width - updatedOverlays[overlayIndex].position.width));
      updatedOverlays[overlayIndex].position.x = value;
    }
    if (property === 'position.y' && backgroundImage) {
      value = Math.max(0, Math.min(value, backgroundImage.height - updatedOverlays[overlayIndex].position.height));
      updatedOverlays[overlayIndex].position.y = value;
    }
    if (property === 'position.width') {
      value = Math.max(10, value);
      updatedOverlays[overlayIndex].position.width = value;
      // Adjust X if overlay extends beyond image
      if (backgroundImage && updatedOverlays[overlayIndex].position.x + value > backgroundImage.width) {
        updatedOverlays[overlayIndex].position.x = backgroundImage.width - value;
      }
    }
    if (property === 'position.height') {
      value = Math.max(10, value);
      updatedOverlays[overlayIndex].position.height = value;
      // Adjust Y if overlay extends beyond image
      if (backgroundImage && updatedOverlays[overlayIndex].position.y + value > backgroundImage.height) {
        updatedOverlays[overlayIndex].position.y = backgroundImage.height - value;
      }
    }
    if (property === 'styling.opacity') {
      value = Math.max(0, Math.min(1, value));
      updatedOverlays[overlayIndex].styling.opacity = value;
    }
    if (property === 'styling.borderRadius') {
      value = Math.max(0, value);
      updatedOverlays[overlayIndex].styling.borderRadius = value;
    }

    onTemplateChange({
      ...template,
      logo_overlays: {
        ...template.logo_overlays,
        overlays: updatedOverlays
      }
    });
  };

  const addNewOverlay = () => {
    const newOverlay = {
      id: `logo_${Date.now()}`,
      type: 'logo',
      placeholder: '{{logo}}',
      position: {
        x: 50,
        y: 50,
        width: 100,
        height: 60
      },
      styling: {
        opacity: 1.0,
        borderRadius: 0
      }
    };

    const updatedOverlays = [...logoOverlays, newOverlay];
    onTemplateChange({
      ...template,
      logo_overlays: {
        overlays: updatedOverlays
      }
    });

    setSelectedOverlay(updatedOverlays.length - 1);
  };

  const deleteOverlay = (index) => {
    const updatedOverlays = logoOverlays.filter((_, i) => i !== index);
    onTemplateChange({
      ...template,
      logo_overlays: {
        overlays: updatedOverlays
      }
    });

    if (selectedOverlay === index) {
      setSelectedOverlay(null);
    } else if (selectedOverlay > index) {
      setSelectedOverlay(selectedOverlay - 1);
    }
  };

  const moveOverlay = (fromIndex, toIndex) => {
    const updatedOverlays = [...logoOverlays];
    const [movedOverlay] = updatedOverlays.splice(fromIndex, 1);
    updatedOverlays.splice(toIndex, 0, movedOverlay);

    onTemplateChange({
      ...template,
      logo_overlays: {
        overlays: updatedOverlays
      }
    });

    // Update selected overlay index
    if (selectedOverlay === fromIndex) {
      setSelectedOverlay(toIndex);
    } else if (selectedOverlay > fromIndex && selectedOverlay <= toIndex) {
      setSelectedOverlay(selectedOverlay - 1);
    } else if (selectedOverlay < fromIndex && selectedOverlay >= toIndex) {
      setSelectedOverlay(selectedOverlay + 1);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-4 border-b">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Logo Overlay Editor</h3>
            {Object.keys(validationErrors).length > 0 && (
              <p className="text-sm text-red-600 mt-1">
                {Object.keys(validationErrors).length} overlay(s) have validation errors
              </p>
            )}
          </div>
          <button
            onClick={addNewOverlay}
            className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
          >
            Add Logo Overlay
          </button>
        </div>
      </div>

      <div className="p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Canvas */}
          <div className="lg:col-span-2">
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <canvas
                ref={canvasRef}
                width={600}
                height={400}
                className={`w-full h-auto ${isDragging ? 'cursor-grabbing' : 'cursor-crosshair'}`}
                onMouseDown={handleMouseDown}
                style={{ userSelect: 'none' }}
              />
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Click and drag logo overlays to reposition them. Selected overlay is highlighted in blue.
            </p>
          </div>

          {/* Logo Overlay Properties */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Logo Overlay Properties</h4>
            
            {selectedOverlay !== null && logoOverlays[selectedOverlay] ? (
              <LogoOverlayPropertiesPanel
                overlay={logoOverlays[selectedOverlay]}
                overlayIndex={selectedOverlay}
                onUpdate={updateOverlayProperty}
                onDelete={() => deleteOverlay(selectedOverlay)}
                validationErrors={validationErrors[selectedOverlay] || {}}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Select a logo overlay to edit its properties</p>
              </div>
            )}

            {/* Overlay List */}
            <div className="mt-6">
              <h5 className="font-medium text-gray-900 mb-2">All Logo Overlays</h5>
              <div className="space-y-2">
                {logoOverlays.map((overlay, index) => (
                  <div
                    key={overlay.id}
                    className={`p-2 border rounded cursor-pointer ${
                      selectedOverlay === index
                        ? 'border-green-500 bg-green-50'
                        : validationErrors[index]
                          ? 'border-red-300 bg-red-50 hover:border-red-400'
                          : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedOverlay(index)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{formatOverlayDisplayName(overlay.id)}</span>
                        {validationErrors[index] && (
                          <span className="text-xs text-red-600" title="Has validation errors">⚠️</span>
                        )}
                      </div>
                      <div className="flex gap-1">
                        {index > 0 && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              moveOverlay(index, index - 1);
                            }}
                            className="text-gray-400 hover:text-gray-600 text-xs"
                            title="Move up"
                          >
                            ↑
                          </button>
                        )}
                        {index < logoOverlays.length - 1 && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              moveOverlay(index, index + 1);
                            }}
                            className="text-gray-400 hover:text-gray-600 text-xs"
                            title="Move down"
                          >
                            ↓
                          </button>
                        )}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteOverlay(index);
                          }}
                          className="text-red-500 hover:text-red-700 text-xs"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600">{overlay.placeholder}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Logo Overlay Properties Panel Component
 */
const LogoOverlayPropertiesPanel = ({
  overlay,
  overlayIndex,
  onUpdate,
  onDelete,
  validationErrors = {}
}) => {
  const placeholderOptions = [
    '{{logo}}',
    '{{company_logo}}',
    '{{brand_logo}}',
    '{{institution_logo}}',
    '{{partner_logo}}'
  ];

  return (
    <div className="space-y-4">
      {/* Basic Properties */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">ID</label>
        <input
          type="text"
          value={overlay.id}
          onChange={(e) => onUpdate(overlayIndex, 'id', e.target.value)}
          className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 ${
            validationErrors.id ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-green-500'
          }`}
        />
        {validationErrors.id && (
          <p className="text-xs text-red-600 mt-1">{validationErrors.id}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Placeholder</label>
        <select
          value={overlay.placeholder}
          onChange={(e) => onUpdate(overlayIndex, 'placeholder', e.target.value)}
          className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 ${
            validationErrors.placeholder ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-green-500'
          }`}
        >
          {placeholderOptions.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
        {validationErrors.placeholder && (
          <p className="text-xs text-red-600 mt-1">{validationErrors.placeholder}</p>
        )}
      </div>

      {/* Position */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-600 mb-1">X</label>
            <input
              type="number"
              value={overlay.position.x}
              onChange={(e) => onUpdate(overlayIndex, 'position.x', parseInt(e.target.value) || 0)}
              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 ${
                validationErrors.x ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-green-500'
              }`}
            />
            {validationErrors.x && (
              <p className="text-xs text-red-600 mt-1">{validationErrors.x}</p>
            )}
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Y</label>
            <input
              type="number"
              value={overlay.position.y}
              onChange={(e) => onUpdate(overlayIndex, 'position.y', parseInt(e.target.value) || 0)}
              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 ${
                validationErrors.y ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-green-500'
              }`}
            />
            {validationErrors.y && (
              <p className="text-xs text-red-600 mt-1">{validationErrors.y}</p>
            )}
          </div>
        </div>
      </div>

      {/* Size */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Width</label>
            <input
              type="number"
              value={overlay.position.width}
              onChange={(e) => onUpdate(overlayIndex, 'position.width', parseInt(e.target.value) || 1)}
              min="10"
              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 ${
                validationErrors.width ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-green-500'
              }`}
            />
            {validationErrors.width && (
              <p className="text-xs text-red-600 mt-1">{validationErrors.width}</p>
            )}
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Height</label>
            <input
              type="number"
              value={overlay.position.height}
              onChange={(e) => onUpdate(overlayIndex, 'position.height', parseInt(e.target.value) || 1)}
              min="10"
              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 ${
                validationErrors.height ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-green-500'
              }`}
            />
            {validationErrors.height && (
              <p className="text-xs text-red-600 mt-1">{validationErrors.height}</p>
            )}
          </div>
        </div>
      </div>

      {/* Styling */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Styling</label>
        <div className="space-y-2">
          <div>
            <label className="block text-xs text-gray-600 mb-1">Opacity (%)</label>
            <input
              type="range"
              min="0"
              max="100"
              value={(overlay.styling?.opacity || 1) * 100}
              onChange={(e) => onUpdate(overlayIndex, 'styling.opacity', parseFloat(e.target.value) / 100)}
              className="w-full"
            />
            <div className="text-xs text-gray-500 text-center">
              {Math.round((overlay.styling?.opacity || 1) * 100)}%
            </div>
          </div>
          <div>
            <label className="block text-xs text-gray-600 mb-1">Border Radius (px)</label>
            <input
              type="number"
              value={overlay.styling?.borderRadius || 0}
              onChange={(e) => onUpdate(overlayIndex, 'styling.borderRadius', parseInt(e.target.value) || 0)}
              min="0"
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-green-500"
            />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={onDelete}
          className="w-full bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
        >
          Delete Logo Overlay
        </button>
      </div>
    </div>
  );
};

export default LogoOverlayEditor;
