import React, { useState, useRef } from 'react';
import { validateTemplateData, uploadBackgroundImage, createTemplate, getDefaultTextOverlays } from '../../../services/templateService';
import { formatOverlayDisplayName } from '../../../utils/overlayUtils';
import { prodLogger } from '../../../utils/prodLogger.js';
import {
  validateTemplateName,
  validateTemplateDescription,
  validateTemplateCategory,
  validateTemplateTags,
  validateImageFile,
  validateImageDimensions,
  validateTextOverlay,
  validateCompleteTemplate
} from '../../../utils/templateValidation';

/**
 * Template Creation Form Component
 * Comprehensive form for creating new templates with validation and preview
 */
const TemplateCreationForm = ({ onClose, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'Business',
    tags: [],
    layoutType: 'standard',
    is_premium: false,
    status: 'active',
    supported_formats: ['pdf', 'png', 'jpg']
  });

  // Image upload state
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [imageDimensions, setImageDimensions] = useState(null);
  
  // Text overlays state
  const [textOverlays, setTextOverlays] = useState(getDefaultTextOverlays('standard'));
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState(1); // 1: Basic Info, 2: Image Upload, 3: Text Overlays, 4: Preview
  const [tagInput, setTagInput] = useState('');
  
  const fileInputRef = useRef(null);

  // Available categories
  const categories = [
    'Business', 'Academic', 'Creative', 'Literature', 'Technical', 
    'Marketing', 'Education', 'Personal', 'Professional', 'Minimal'
  ];

  // Available layout types
  const layoutTypes = [
    { value: 'standard', label: 'Standard (Title, Author, Description)' },
    { value: 'minimal', label: 'Minimal (Title Only)' },
    { value: 'professional', label: 'Professional (Title, Author, Subtitle)' },
    { value: 'custom', label: 'Custom (Title, Author)' }
  ];

  // Handle form field changes with validation
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Real-time validation for specific fields
    let fieldError = null;
    switch (field) {
      case 'name':
        const nameValidation = validateTemplateName(value);
        if (!nameValidation.isValid) {
          fieldError = nameValidation.error;
        }
        break;
      case 'description':
        const descValidation = validateTemplateDescription(value);
        if (!descValidation.isValid) {
          fieldError = descValidation.error;
        }
        break;
      case 'category':
        const catValidation = validateTemplateCategory(value);
        if (!catValidation.isValid) {
          fieldError = catValidation.error;
        }
        break;
      case 'tags':
        const tagsValidation = validateTemplateTags(value);
        if (!tagsValidation.isValid) {
          fieldError = tagsValidation.error;
        }
        break;
    }

    // Update errors
    setErrors(prev => ({
      ...prev,
      [field]: fieldError
    }));
  };

  // Handle layout type change
  const handleLayoutTypeChange = (layoutType) => {
    setFormData(prev => ({ ...prev, layoutType }));
    setTextOverlays(getDefaultTextOverlays(layoutType));
  };

  // Handle tag management
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      handleInputChange('tags', [...formData.tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove));
  };

  // Handle image upload with comprehensive validation
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate image file
    const fileValidation = validateImageFile(file);
    if (!fileValidation.isValid) {
      setErrors(prev => ({ ...prev, image: fileValidation.error }));
      return;
    }

    setImageFile(file);
    setErrors(prev => ({ ...prev, image: null }));

    // Create preview and get dimensions
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target.result);

      // Get image dimensions and validate them
      const img = new Image();
      img.onload = () => {
        const dimensions = {
          width: img.naturalWidth,
          height: img.naturalHeight
        };

        // Validate dimensions
        const dimensionsValidation = validateImageDimensions(dimensions);
        if (!dimensionsValidation.isValid) {
          setErrors(prev => ({ ...prev, image: dimensionsValidation.error }));
          setImageFile(null);
          setImagePreview(null);
          setImageDimensions(null);
          return;
        }

        setImageDimensions(dimensions);
      };
      img.onerror = () => {
        setErrors(prev => ({ ...prev, image: 'Failed to load image. Please try a different file.' }));
        setImageFile(null);
        setImagePreview(null);
        setImageDimensions(null);
      };
      img.src = e.target.result;
    };
    reader.onerror = () => {
      setErrors(prev => ({ ...prev, image: 'Failed to read file. Please try again.' }));
    };
    reader.readAsDataURL(file);
  };

  // Comprehensive step validation
  const validateStep = (step) => {
    const stepErrors = {};

    if (step === 1) {
      // Validate name
      const nameValidation = validateTemplateName(formData.name);
      if (!nameValidation.isValid) {
        stepErrors.name = nameValidation.error;
      }

      // Validate description
      const descValidation = validateTemplateDescription(formData.description);
      if (!descValidation.isValid) {
        stepErrors.description = descValidation.error;
      }

      // Validate category
      const catValidation = validateTemplateCategory(formData.category);
      if (!catValidation.isValid) {
        stepErrors.category = catValidation.error;
      }

      // Validate tags
      const tagsValidation = validateTemplateTags(formData.tags);
      if (!tagsValidation.isValid) {
        stepErrors.tags = tagsValidation.error;
      }
    }

    if (step === 2) {
      // Validate image file
      const fileValidation = validateImageFile(imageFile);
      if (!fileValidation.isValid) {
        stepErrors.image = fileValidation.error;
      }

      // Validate image dimensions if available
      if (imageDimensions) {
        const dimensionsValidation = validateImageDimensions(imageDimensions);
        if (!dimensionsValidation.isValid) {
          stepErrors.image = dimensionsValidation.error;
        }
      }
    }

    if (step === 3) {
      // Validate text overlays
      if (!textOverlays || !textOverlays.overlays || textOverlays.overlays.length === 0) {
        stepErrors.overlays = 'At least one text overlay is required';
      } else {
        const overlayErrors = [];
        textOverlays.overlays.forEach((overlay, index) => {
          const overlayValidation = validateTextOverlay(overlay, index);
          if (!overlayValidation.isValid) {
            overlayErrors.push(overlayValidation.error);
          }
        });

        if (overlayErrors.length > 0) {
          stepErrors.overlays = overlayErrors.join('; ');
        }
      }
    }

    setErrors(prev => ({ ...prev, ...stepErrors }));
    return Object.keys(stepErrors).length === 0;
  };

  // Handle step navigation
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle form submission with comprehensive validation
  const handleSubmit = async () => {
    // Validate all steps before submission
    const step1Valid = validateStep(1);
    const step2Valid = validateStep(2);
    const step3Valid = validateStep(3);

    if (!step1Valid || !step2Valid || !step3Valid) {
      // Navigate to first invalid step
      if (!step1Valid) setCurrentStep(1);
      else if (!step2Valid) setCurrentStep(2);
      else if (!step3Valid) setCurrentStep(3);
      return;
    }

    setLoading(true);
    setErrors({});

    try {
      // Final comprehensive validation
      const templateData = {
        ...formData,
        background_image_width: imageDimensions.width,
        background_image_height: imageDimensions.height,
        text_overlays: textOverlays
      };

      const finalValidation = validateCompleteTemplate(templateData);
      if (!finalValidation.isValid) {
        setErrors({
          submit: 'Validation failed',
          validationErrors: Object.values(finalValidation.errors)
        });
        setLoading(false);
        return;
      }

      // Generate template ID for image upload
      const templateId = `template-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Upload background image
      const uploadResult = await uploadBackgroundImage(imageFile, templateId);
      if (!uploadResult.success) {
        setErrors({ submit: uploadResult.error });
        setLoading(false);
        return;
      }

      // Prepare final template data
      const finalTemplateData = {
        ...templateData,
        background_image_url: uploadResult.url,
        background_image_width: uploadResult.dimensions.width,
        background_image_height: uploadResult.dimensions.height
      };

      // Create template
      const createResult = await createTemplate(finalTemplateData);
      if (!createResult.success) {
        setErrors({
          submit: createResult.error,
          validationErrors: createResult.errors
        });
        setLoading(false);
        return;
      }

      // Success
      onSuccess(createResult.template);
      onClose();

    } catch (error) {
      prodLogger.error('Template creation failed:', error);
      setErrors({ submit: 'An unexpected error occurred. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Create New Template</h2>
            <p className="text-sm text-gray-600">Step {currentStep} of 4</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={loading}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-2 bg-gray-50">
          <div className="flex items-center space-x-2">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 4 && (
                  <div className={`w-12 h-1 mx-2 ${
                    step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between text-xs text-gray-600 mt-2">
            <span>Basic Info</span>
            <span>Image Upload</span>
            <span>Text Overlays</span>
            <span>Preview</span>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <BasicInfoStep
              formData={formData}
              errors={errors}
              categories={categories}
              layoutTypes={layoutTypes}
              tagInput={tagInput}
              setTagInput={setTagInput}
              onInputChange={handleInputChange}
              onLayoutTypeChange={handleLayoutTypeChange}
              onAddTag={addTag}
              onRemoveTag={removeTag}
            />
          )}

          {/* Step 2: Image Upload */}
          {currentStep === 2 && (
            <ImageUploadStep
              imageFile={imageFile}
              imagePreview={imagePreview}
              imageDimensions={imageDimensions}
              errors={errors}
              fileInputRef={fileInputRef}
              onImageUpload={handleImageUpload}
            />
          )}

          {/* Step 3: Text Overlays */}
          {currentStep === 3 && (
            <TextOverlaysStep
              textOverlays={textOverlays}
              setTextOverlays={setTextOverlays}
              imagePreview={imagePreview}
              imageDimensions={imageDimensions}
            />
          )}

          {/* Step 4: Preview */}
          {currentStep === 4 && (
            <PreviewStep
              formData={formData}
              textOverlays={textOverlays}
              imagePreview={imagePreview}
              imageDimensions={imageDimensions}
            />
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
          <div>
            {errors.submit && (
              <p className="text-red-600 text-sm">{errors.submit}</p>
            )}
            {errors.validationErrors && (
              <div className="text-red-600 text-sm">
                <p>Validation errors:</p>
                <ul className="list-disc list-inside">
                  {errors.validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            {currentStep > 1 && (
              <button
                onClick={prevStep}
                disabled={loading}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                Previous
              </button>
            )}
            
            {currentStep < 4 ? (
              <button
                onClick={nextStep}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {loading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                )}
                <span>{loading ? 'Creating...' : 'Create Template'}</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Step 1: Basic Information Component
 */
const BasicInfoStep = ({
  formData,
  errors,
  categories,
  layoutTypes,
  tagInput,
  setTagInput,
  onInputChange,
  onLayoutTypeChange,
  onAddTag,
  onRemoveTag
}) => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Template Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Template Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => onInputChange('name', e.target.value)}
          placeholder="Enter template name"
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.name ? 'border-red-500' : 'border-gray-300'
          }`}
          maxLength={100}
        />
        {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
        <p className="text-gray-500 text-xs mt-1">{formData.name.length}/100 characters</p>
      </div>

      {/* Category */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Category *
        </label>
        <select
          value={formData.category}
          onChange={(e) => onInputChange('category', e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.category ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>
        {errors.category && <p className="text-red-600 text-sm mt-1">{errors.category}</p>}
      </div>
    </div>

    {/* Description */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Description
      </label>
      <textarea
        value={formData.description}
        onChange={(e) => onInputChange('description', e.target.value)}
        placeholder="Enter template description (optional)"
        rows={3}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
          errors.description ? 'border-red-500' : 'border-gray-300'
        }`}
        maxLength={500}
      />
      {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
      <p className="text-gray-500 text-xs mt-1">{formData.description.length}/500 characters</p>
    </div>

    {/* Layout Type */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Layout Type
      </label>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {layoutTypes.map(layout => (
          <label key={layout.value} className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              name="layoutType"
              value={layout.value}
              checked={formData.layoutType === layout.value}
              onChange={(e) => onLayoutTypeChange(e.target.value)}
              className="text-blue-600 focus:ring-blue-500"
            />
            <div>
              <div className="font-medium text-gray-900">{layout.label}</div>
            </div>
          </label>
        ))}
      </div>
    </div>

    {/* Tags */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Tags
      </label>
      <div className="flex space-x-2 mb-2">
        <input
          type="text"
          value={tagInput}
          onChange={(e) => setTagInput(e.target.value)}
          placeholder="Add a tag"
          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), onAddTag())}
        />
        <button
          type="button"
          onClick={onAddTag}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Add
        </button>
      </div>
      {formData.tags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {formData.tags.map(tag => (
            <span
              key={tag}
              className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
            >
              {tag}
              <button
                type="button"
                onClick={() => onRemoveTag(tag)}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      )}
    </div>

    {/* Advanced Options */}
    <div className="border-t pt-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Advanced Options</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Premium Status */}
        <div>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={formData.is_premium}
              onChange={(e) => onInputChange('is_premium', e.target.checked)}
              className="text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700">Premium Template</span>
          </label>
          <p className="text-gray-500 text-xs mt-1">Mark as premium template (requires subscription)</p>
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Status
          </label>
          <select
            value={formData.status}
            onChange={(e) => onInputChange('status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
      </div>
    </div>
  </div>
);

/**
 * Step 2: Image Upload Component
 */
const ImageUploadStep = ({
  imageFile,
  imagePreview,
  imageDimensions,
  errors,
  fileInputRef,
  onImageUpload
}) => (
  <div className="space-y-6">
    <div className="text-center">
      <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Background Image</h3>
      <p className="text-gray-600 mb-6">Choose a high-quality background image for your template</p>
    </div>

    {/* Upload Area */}
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
      {!imagePreview ? (
        <div className="text-center">
          <svg className="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className="text-gray-600 mb-2">Click to upload or drag and drop</p>
          <p className="text-gray-500 text-sm">JPEG, PNG, or WebP (max 10MB)</p>
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Choose File
          </button>
        </div>
      ) : (
        <div className="text-center">
          <img
            src={imagePreview}
            alt="Preview"
            className="max-w-full max-h-64 mx-auto rounded-lg shadow-lg mb-4"
          />
          <div className="text-sm text-gray-600 mb-4">
            <p><strong>File:</strong> {imageFile?.name}</p>
            <p><strong>Size:</strong> {(imageFile?.size / 1024 / 1024).toFixed(2)} MB</p>
            {imageDimensions && (
              <p><strong>Dimensions:</strong> {imageDimensions.width} × {imageDimensions.height} pixels</p>
            )}
          </div>
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Change Image
          </button>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={onImageUpload}
        className="hidden"
      />
    </div>

    {errors.image && (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600 text-sm">{errors.image}</p>
      </div>
    )}

    {/* Image Guidelines */}
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h4 className="font-medium text-blue-900 mb-2">Image Guidelines</h4>
      <ul className="text-blue-800 text-sm space-y-1">
        <li>• Recommended dimensions: 1200×1600 pixels (3:4 aspect ratio)</li>
        <li>• Minimum resolution: 800×600 pixels</li>
        <li>• File formats: JPEG, PNG, or WebP</li>
        <li>• Maximum file size: 10MB</li>
        <li>• High contrast areas work best for text overlays</li>
      </ul>
    </div>
  </div>
);

/**
 * Step 3: Text Overlays Component
 */
const TextOverlaysStep = ({
  textOverlays,
  setTextOverlays,
  imagePreview,
  imageDimensions
}) => {
  const [selectedOverlay, setSelectedOverlay] = useState(0);

  const updateOverlay = (index, field, value) => {
    const updatedOverlays = { ...textOverlays };
    const fieldPath = field.split('.');

    if (fieldPath.length === 1) {
      updatedOverlays.overlays[index][field] = value;
    } else if (fieldPath.length === 2) {
      updatedOverlays.overlays[index][fieldPath[0]][fieldPath[1]] = value;
    }

    setTextOverlays(updatedOverlays);
  };

  const addOverlay = () => {
    const newOverlay = {
      id: `overlay_${Date.now()}`,
      type: 'text',
      placeholder: '{{title}}',
      position: { x: 50, y: 50, width: 200, height: 40 },
      styling: {
        fontSize: 24,
        fontFamily: 'Arial',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'left',
        lineHeight: 1.2,
        verticalAlign: 'top'
      }
    };

    setTextOverlays(prev => ({
      ...prev,
      overlays: [...prev.overlays, newOverlay]
    }));
    setSelectedOverlay(textOverlays.overlays.length);
  };

  const removeOverlay = (index) => {
    setTextOverlays(prev => ({
      ...prev,
      overlays: prev.overlays.filter((_, i) => i !== index)
    }));
    setSelectedOverlay(Math.max(0, selectedOverlay - 1));
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Configure Text Overlays</h3>
        <p className="text-gray-600 mb-6">Position and style text elements on your template</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Preview Panel */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Preview</h4>
          <div className="border border-gray-300 rounded-lg overflow-hidden bg-gray-50">
            {imagePreview ? (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Template preview"
                  className="w-full h-auto"
                />
                {/* Overlay indicators */}
                {textOverlays.overlays.map((overlay, index) => (
                  <div
                    key={overlay.id}
                    className={`absolute border-2 ${
                      index === selectedOverlay ? 'border-blue-500' : 'border-red-400'
                    } bg-black bg-opacity-20 cursor-pointer`}
                    style={{
                      left: `${(overlay.position.x / (imageDimensions?.width || 600)) * 100}%`,
                      top: `${(overlay.position.y / (imageDimensions?.height || 800)) * 100}%`,
                      width: `${(overlay.position.width / (imageDimensions?.width || 600)) * 100}%`,
                      height: `${(overlay.position.height / (imageDimensions?.height || 800)) * 100}%`,
                    }}
                    onClick={() => setSelectedOverlay(index)}
                  >
                    <div className="text-white text-xs p-1 bg-black bg-opacity-50">
                      {formatOverlayDisplayName(overlay.id)}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center text-gray-500">
                Upload an image to see preview
              </div>
            )}
          </div>
        </div>

        {/* Controls Panel */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="font-medium text-gray-900">Text Overlays</h4>
            <button
              onClick={addOverlay}
              className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
            >
              Add Overlay
            </button>
          </div>

          {/* Overlay List */}
          <div className="space-y-2">
            {textOverlays.overlays.map((overlay, index) => (
              <div
                key={overlay.id}
                className={`p-3 border rounded-lg cursor-pointer ${
                  index === selectedOverlay ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onClick={() => setSelectedOverlay(index)}
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium">{formatOverlayDisplayName(overlay.id)}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeOverlay(index);
                    }}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>
                <p className="text-sm text-gray-600">{overlay.placeholder}</p>
              </div>
            ))}
          </div>

          {/* Selected Overlay Properties */}
          {textOverlays.overlays[selectedOverlay] && (
            <OverlayPropertiesEditor
              overlay={textOverlays.overlays[selectedOverlay]}
              overlayIndex={selectedOverlay}
              onUpdate={updateOverlay}
            />
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Overlay Properties Editor Component
 */
const OverlayPropertiesEditor = ({ overlay, overlayIndex, onUpdate }) => (
  <div className="border-t pt-4 space-y-4">
    <h5 className="font-medium text-gray-900">Edit: {formatOverlayDisplayName(overlay.id)}</h5>

    {/* Basic Properties */}
    <div className="grid grid-cols-2 gap-3">
      <div>
        <label className="block text-xs text-gray-600 mb-1">ID</label>
        <input
          type="text"
          value={overlay.id}
          onChange={(e) => onUpdate(overlayIndex, 'id', e.target.value)}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
        />
      </div>
      <div>
        <label className="block text-xs text-gray-600 mb-1">Placeholder</label>
        <input
          type="text"
          value={overlay.placeholder}
          onChange={(e) => onUpdate(overlayIndex, 'placeholder', e.target.value)}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
        />
      </div>
    </div>

    {/* Position */}
    <div>
      <label className="block text-xs text-gray-600 mb-2">Position & Size</label>
      <div className="grid grid-cols-2 gap-2">
        <div>
          <label className="block text-xs text-gray-500">X</label>
          <input
            type="number"
            value={overlay.position.x}
            onChange={(e) => onUpdate(overlayIndex, 'position.x', parseInt(e.target.value))}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-xs text-gray-500">Y</label>
          <input
            type="number"
            value={overlay.position.y}
            onChange={(e) => onUpdate(overlayIndex, 'position.y', parseInt(e.target.value))}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-xs text-gray-500">Width</label>
          <input
            type="number"
            value={overlay.position.width}
            onChange={(e) => onUpdate(overlayIndex, 'position.width', parseInt(e.target.value))}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-xs text-gray-500">Height</label>
          <input
            type="number"
            value={overlay.position.height}
            onChange={(e) => onUpdate(overlayIndex, 'position.height', parseInt(e.target.value))}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>

    {/* Styling */}
    <div>
      <label className="block text-xs text-gray-600 mb-2">Styling</label>
      <div className="space-y-2">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-500">Font Size</label>
            <input
              type="number"
              value={overlay.styling.fontSize}
              onChange={(e) => onUpdate(overlayIndex, 'styling.fontSize', parseInt(e.target.value))}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-500">Font Family</label>
            <select
              value={overlay.styling.fontFamily}
              onChange={(e) => onUpdate(overlayIndex, 'styling.fontFamily', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            >
              <option value="Arial">Arial</option>
              <option value="Georgia">Georgia</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Verdana">Verdana</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-500">Font Weight</label>
            <select
              value={overlay.styling.fontWeight}
              onChange={(e) => onUpdate(overlayIndex, 'styling.fontWeight', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            >
              <option value="normal">Normal</option>
              <option value="bold">Bold</option>
              <option value="lighter">Lighter</option>
            </select>
          </div>
          <div>
            <label className="block text-xs text-gray-500">Color</label>
            <input
              type="color"
              value={overlay.styling.color}
              onChange={(e) => onUpdate(overlayIndex, 'styling.color', e.target.value)}
              className="w-full h-8 border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-500">Text Align</label>
            <select
              value={overlay.styling.textAlign}
              onChange={(e) => onUpdate(overlayIndex, 'styling.textAlign', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            >
              <option value="left">Left</option>
              <option value="center">Center</option>
              <option value="right">Right</option>
            </select>
          </div>
          <div>
            <label className="block text-xs text-gray-500">Vertical Align</label>
            <select
              value={overlay.styling.verticalAlign}
              onChange={(e) => onUpdate(overlayIndex, 'styling.verticalAlign', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
            >
              <option value="top">Top</option>
              <option value="center">Center</option>
              <option value="bottom">Bottom</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
);

/**
 * Step 4: Preview Component
 */
const PreviewStep = ({ formData, textOverlays, imagePreview, imageDimensions }) => {
  const [sampleData, setSampleData] = useState({
    title: 'Sample Document Title',
    author: 'John Doe',
    description: 'This is a sample description to show how the template will look with your content.',
    subtitle: 'A Professional Subtitle'
  });

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Template Preview</h3>
        <p className="text-gray-600 mb-6">Review your template with sample data</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Summary */}
        <div className="lg:col-span-1 space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Template Details</h4>
            <div className="space-y-2 text-sm">
              <div><strong>Name:</strong> {formData.name}</div>
              <div><strong>Category:</strong> {formData.category}</div>
              <div><strong>Layout:</strong> {formData.layoutType}</div>
              <div><strong>Status:</strong> {formData.status}</div>
              <div><strong>Premium:</strong> {formData.is_premium ? 'Yes' : 'No'}</div>
              {formData.tags.length > 0 && (
                <div>
                  <strong>Tags:</strong>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {formData.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              {imageDimensions && (
                <div><strong>Dimensions:</strong> {imageDimensions.width} × {imageDimensions.height}</div>
              )}
              <div><strong>Overlays:</strong> {textOverlays.overlays.length}</div>
            </div>
          </div>

          {/* Sample Data Editor */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Sample Data</h4>
            <div className="space-y-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Title</label>
                <input
                  type="text"
                  value={sampleData.title}
                  onChange={(e) => setSampleData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Author</label>
                <input
                  type="text"
                  value={sampleData.author}
                  onChange={(e) => setSampleData(prev => ({ ...prev, author: e.target.value }))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Description</label>
                <textarea
                  value={sampleData.description}
                  onChange={(e) => setSampleData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Subtitle</label>
                <input
                  type="text"
                  value={sampleData.subtitle}
                  onChange={(e) => setSampleData(prev => ({ ...prev, subtitle: e.target.value }))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Template Preview */}
        <div className="lg:col-span-2">
          <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
            {imagePreview ? (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Template preview"
                  className="w-full h-auto"
                />
                {/* Text overlays with sample data */}
                {textOverlays.overlays.map((overlay) => {
                  let displayText = overlay.placeholder;
                  Object.entries(sampleData).forEach(([key, value]) => {
                    displayText = displayText.replace(new RegExp(`{{${key}}}`, 'g'), value);
                  });

                  return (
                    <div
                      key={overlay.id}
                      className="absolute flex items-center justify-center"
                      style={{
                        left: `${(overlay.position.x / (imageDimensions?.width || 600)) * 100}%`,
                        top: `${(overlay.position.y / (imageDimensions?.height || 800)) * 100}%`,
                        width: `${(overlay.position.width / (imageDimensions?.width || 600)) * 100}%`,
                        height: `${(overlay.position.height / (imageDimensions?.height || 800)) * 100}%`,
                        fontSize: `${overlay.styling.fontSize * 0.8}px`, // Scale down for preview
                        fontFamily: overlay.styling.fontFamily,
                        fontWeight: overlay.styling.fontWeight,
                        color: overlay.styling.color,
                        textAlign: overlay.styling.textAlign,
                        lineHeight: overlay.styling.lineHeight,
                        alignItems: overlay.styling.verticalAlign === 'center' ? 'center' :
                                   overlay.styling.verticalAlign === 'bottom' ? 'flex-end' : 'flex-start'
                      }}
                    >
                      <div className="w-full px-1">
                        {displayText}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center text-gray-500">
                No image uploaded
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateCreationForm;
