/**
 * Unit Tests for Profile Placeholder Population
 * Tests the enhanced imageOverlayService placeholder functionality
 */

import imageOverlayService from '../imageOverlayService.js';
import ProfileDataService from '../profileDataService.js';

describe('Profile Placeholder Population', () => {
  
  describe('populatePlaceholder', () => {
    it('should populate profile placeholders correctly', () => {
      const placeholder = 'Hello {{user_name}} from {{company_name}}!';
      const documentData = {
        title: 'Test Document'
      };
      const userProfile = {
        full_name: '<PERSON>',
        organization: 'Acme Corp',
        website: 'www.acme.com',
        phone: '******-123-4567',
        email: '<EMAIL>'
      };

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData, userProfile);
      
      expect(result).toBe('Hello John <PERSON> from Acme Corp!');
    });

    it('should handle missing profile fields gracefully', () => {
      const placeholder = 'Contact {{user_name}} at {{phone_number}}';
      const documentData = { title: 'Test Document' };
      const userProfile = {
        full_name: '<PERSON>'
        // phone is missing
      };

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData, userProfile);
      
      expect(result).toBe('Contact John Doe at '); // phone_number is empty, not replaced
    });

    it('should fall back to document placeholders when profile is null', () => {
      const placeholder = '{{title}} by {{author}} - {{user_name}}';
      const documentData = {
        title: 'My Document',
        author: 'Jane Smith'
      };
      const userProfile = null;

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData, userProfile);
      
      expect(result).toBe('My Document by Jane Smith - '); // user_name is empty
    });

    it('should handle complex mixed placeholders', () => {
      const placeholder = '{{title}} by {{user_name}} ({{company_name}}) - {{date}}';
      const documentData = {
        title: 'Annual Report'
      };
      const userProfile = {
        full_name: 'John Doe',
        organization: 'Acme Corp'
      };

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData, userProfile);
      
      expect(result).toContain('Annual Report by John Doe (Acme Corp)');
      expect(result).toContain(new Date().toLocaleDateString());
    });

    it('should handle empty placeholders', () => {
      const placeholder = '';
      const documentData = {};
      const userProfile = {};

      const result = imageOverlayService.populatePlaceholder(placeholder, documentData, userProfile);
      
      expect(result).toBe('');
    });

    it('should handle null/undefined input gracefully', () => {
      expect(imageOverlayService.populatePlaceholder(null, {}, {})).toBe('');
      expect(imageOverlayService.populatePlaceholder(undefined, {}, {})).toBe('');
    });
  });

  describe('ProfileDataService', () => {
    
    it('should provide available profile placeholders', () => {
      const placeholders = ProfileDataService.getAvailableProfilePlaceholders();
      
      expect(placeholders).toHaveLength(5);
      expect(placeholders[0]).toHaveProperty('placeholder', '{{user_name}}');
      expect(placeholders[0]).toHaveProperty('field', 'full_name');
      expect(placeholders[1]).toHaveProperty('placeholder', '{{company_name}}');
      expect(placeholders[1]).toHaveProperty('field', 'organization');
    });

    it('should provide available document placeholders', () => {
      const placeholders = ProfileDataService.getAvailableDocumentPlaceholders();
      
      expect(placeholders).toHaveLength(5);
      expect(placeholders[0]).toHaveProperty('placeholder', '{{title}}');
      expect(placeholders[1]).toHaveProperty('placeholder', '{{author}}');
    });

    it('should get profile value by field name', () => {
      const userProfile = {
        full_name: 'John Doe',
        organization: 'Acme Corp',
        website: 'www.acme.com'
      };

      expect(ProfileDataService.getProfileValue('user_name', userProfile)).toBe('John Doe');
      expect(ProfileDataService.getProfileValue('company_name', userProfile)).toBe('Acme Corp');
      expect(ProfileDataService.getProfileValue('website', userProfile)).toBe('www.acme.com');
      expect(ProfileDataService.getProfileValue('phone_number', userProfile)).toBe('');
    });

    it('should format values correctly', () => {
      expect(ProfileDataService.formatValue('john doe', { transform: 'capitalize' })).toBe('John doe');
      expect(ProfileDataService.formatValue('john doe', { transform: 'uppercase' })).toBe('JOHN DOE');
      expect(ProfileDataService.formatValue('JOHN DOE', { transform: 'lowercase' })).toBe('john doe');
      
      expect(ProfileDataService.formatValue('John', { prefix: 'Mr. ' })).toBe('Mr. John');
      expect(ProfileDataService.formatValue('John', { suffix: ' Doe' })).toBe('John Doe');
    });

    it('should validate profile data', () => {
      const validProfile = {
        website: 'https://example.com',
        phone: '******-123-4567',
        email: '<EMAIL>'
      };

      const invalidProfile = {
        website: 'not-a-url',
        phone: 'invalid-phone',
        email: 'not-an-email'
      };

      const validResult = ProfileDataService.validateProfileData(validProfile);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      const invalidResult = ProfileDataService.validateProfileData(invalidProfile);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });
  });
});