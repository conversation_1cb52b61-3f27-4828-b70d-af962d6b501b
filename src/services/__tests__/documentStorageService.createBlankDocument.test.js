import { jest } from '@jest/globals';
import { documentStorage } from '../documentStorageService';
import { projectsService } from '../projectsService';

// Mock the projectsService
jest.mock('../projectsService', () => ({
  projectsService: {
    createProject: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/prodLogger.js', () => ({
  prodLogger: {
    debug: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  }
}));

describe('DocumentStorageService - createBlankDocument', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the cache
    documentStorage.clearCache();
  });

  test('should create blank document with correct structure', async () => {
    const userId = 'test-user-123';
    const mockDocumentId = 'doc-456';
    
    // Mock successful project creation
    projectsService.createProject.mockResolvedValueOnce({
      success: true,
      data: { id: mockDocumentId }
    });

    const result = await documentStorage.createBlankDocument(userId);

    expect(result.success).toBe(true);
    expect(result.documentId).toBe(mockDocumentId);
    expect(result.data.id).toBe(mockDocumentId);

    // Verify the project was created with correct structure
    expect(projectsService.createProject).toHaveBeenCalledWith(
      expect.objectContaining({
        questionnaire_data: expect.objectContaining({
          documentPurpose: expect.objectContaining({
            primaryType: 'ebook',
            format: 'pdf',
            baseline: 'scratch'
          }),
          topicAndNiche: expect.objectContaining({
            mainTopic: '',
            language: 'english'
          })
        }),
        generated_content: expect.objectContaining({
          title: 'Untitled Document',
          chapters: [],
          wordCount: 0,
          totalPages: 1
        }),
        user_id: userId,
        status: 'in_progress',
        progress: 25
      })
    );

    // Verify timestamps are included
    const calledWith = projectsService.createProject.mock.calls[0][0];
    expect(calledWith.created_at).toBeDefined();
    expect(calledWith.updated_at).toBeDefined();
    expect(new Date(calledWith.created_at)).toBeInstanceOf(Date);
    expect(new Date(calledWith.updated_at)).toBeInstanceOf(Date);
  });

  test('should cache the created document', async () => {
    const userId = 'test-user-123';
    const mockDocumentId = 'doc-456';
    
    projectsService.createProject.mockResolvedValueOnce({
      success: true,
      data: { id: mockDocumentId, title: 'Test Doc' }
    });

    await documentStorage.createBlankDocument(userId);

    // Check that document is cached
    const cached = documentStorage.getCachedDocument(mockDocumentId);
    expect(cached).toBeDefined();
    expect(cached.documentId).toBe(mockDocumentId);
    expect(cached.projectId).toBe(mockDocumentId);
  });

  test('should handle project creation failure', async () => {
    const userId = 'test-user-123';
    
    // Mock failed project creation
    projectsService.createProject.mockResolvedValueOnce({
      success: false,
      error: { message: 'Database connection failed' }
    });

    const result = await documentStorage.createBlankDocument(userId);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Database connection failed');
    expect(result.documentId).toBeUndefined();
  });

  test('should handle project creation exception', async () => {
    const userId = 'test-user-123';
    
    // Mock projectsService throwing an exception
    projectsService.createProject.mockRejectedValueOnce(
      new Error('Network timeout')
    );

    const result = await documentStorage.createBlankDocument(userId);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Network timeout');
    expect(result.documentId).toBeUndefined();
  });

  test('should handle project creation with no error message', async () => {
    const userId = 'test-user-123';
    
    // Mock failed project creation without error message
    projectsService.createProject.mockResolvedValueOnce({
      success: false,
      error: {}
    });

    const result = await documentStorage.createBlankDocument(userId);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Failed to create blank document');
  });

  test('should use correct default values for blank document', async () => {
    const userId = 'test-user-123';
    
    projectsService.createProject.mockResolvedValueOnce({
      success: true,
      data: { id: 'doc-123' }
    });

    await documentStorage.createBlankDocument(userId);

    const calledWith = projectsService.createProject.mock.calls[0][0];
    
    // Verify all default values
    expect(calledWith.questionnaire_data.documentPurpose.primaryType).toBe('ebook');
    expect(calledWith.questionnaire_data.documentPurpose.format).toBe('pdf');
    expect(calledWith.questionnaire_data.documentPurpose.baseline).toBe('scratch');
    expect(calledWith.questionnaire_data.topicAndNiche.mainTopic).toBe('');
    expect(calledWith.questionnaire_data.topicAndNiche.language).toBe('english');
    expect(calledWith.generated_content.title).toBe('Untitled Document');
    expect(calledWith.generated_content.chapters).toEqual([]);
    expect(calledWith.generated_content.wordCount).toBe(0);
    expect(calledWith.generated_content.totalPages).toBe(1);
    expect(calledWith.user_id).toBe(userId);
    expect(calledWith.status).toBe('in_progress');
    expect(calledWith.progress).toBe(25);
  });
});