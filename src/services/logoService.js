/**
 * Logo Service
 * Handles upload, storage, and management of user logos
 * Follows patterns established by customCoverImageService.js
 */

import { supabase } from "../lib/supabase.js";
import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor.js";
import { prodLogger } from "../utils/prodLogger.js";

// Configuration for user logos
const LOGO_CONFIG = {
  // Storage bucket for user logos
  STORAGE_BUCKET: "user-logos",

  // File size limits (in bytes)
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  RECOMMENDED_MAX_SIZE: 2 * 1024 * 1024, // 2MB recommended

  // Supported image formats
  SUPPORTED_FORMATS: ["image/jpeg", "image/jpg", "image/png", "image/webp"],

  // Image optimization settings
  OPTIMIZATION: {
    maxWidth: 800, // Smaller than cover images since logos are typically smaller
    maxHeight: 800,
    quality: 0.9, // High quality for logos
    format: "png", // PNG preserves transparency for logos
  },

  // File naming convention
  FILE_PREFIX: "logo-",

  // Logo positioning options for documents
  POSITIONS: {
    TOP_LEFT: "top-left",
    TOP_CENTER: "top-center",
    TOP_RIGHT: "top-right",
    CENTER: "center",
    BOTTOM_LEFT: "bottom-left",
    BOTTOM_CENTER: "bottom-center",
    BOTTOM_RIGHT: "bottom-right",
  },

  // Default logo settings
  DEFAULTS: {
    position: "top-right",
    size: "medium", // small, medium, large
    opacity: 1.0,
  },
};

/**
 * Validate uploaded logo file
 * @param {File} file - The uploaded file
 * @returns {Object} Validation result with success flag and error message
 */
export const validateLogo = (file) => {
  try {
    // Check if file exists
    if (!file) {
      return { success: false, error: "No file provided" };
    }

    // Check file size
    if (file.size > LOGO_CONFIG.MAX_FILE_SIZE) {
      const maxSizeMB = LOGO_CONFIG.MAX_FILE_SIZE / (1024 * 1024);
      return {
        success: false,
        error: `File size exceeds ${maxSizeMB}MB limit. Current size: ${(
          file.size /
          (1024 * 1024)
        ).toFixed(2)}MB`,
      };
    }

    // Check file type
    if (!LOGO_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
      return {
        success: false,
        error: `Unsupported file format. Supported formats: ${LOGO_CONFIG.SUPPORTED_FORMATS.join(
          ", "
        )}`,
      };
    }

    // Warn if file is larger than recommended
    const warnings = [];
    if (file.size > LOGO_CONFIG.RECOMMENDED_MAX_SIZE) {
      const recommendedMB = LOGO_CONFIG.RECOMMENDED_MAX_SIZE / (1024 * 1024);
      warnings.push(
        `File size is larger than recommended ${recommendedMB}MB. Consider optimizing for better performance.`
      );
    }

    return { success: true, warnings };
  } catch (error) {
    prodLogger.error("❌ Error validating logo:", error);
    return { success: false, error: "Failed to validate logo file" };
  }
};

/**
 * Optimize logo for storage and use
 * @param {File} file - The original logo file
 * @returns {Promise<Blob>} Optimized logo blob
 */
export const optimizeLogo = async (file) => {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        try {
          const { maxWidth, maxHeight, quality } = LOGO_CONFIG.OPTIMIZATION;

          // Calculate optimal dimensions while maintaining aspect ratio
          let { width, height } = img;

          if (width > maxWidth || height > maxHeight) {
            const aspectRatio = width / height;

            if (width > height) {
              width = maxWidth;
              height = width / aspectRatio;
            } else {
              height = maxHeight;
              width = height * aspectRatio;
            }
          }

          // Set canvas dimensions
          canvas.width = width;
          canvas.height = height;

          // Draw and optimize image
          ctx.drawImage(img, 0, 0, width, height);

          // Convert to blob with optimization
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob);
              } else {
                reject(new Error("Failed to optimize logo"));
              }
            },
            `image/${LOGO_CONFIG.OPTIMIZATION.format}`,
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error("Failed to load image for optimization"));
      };

      img.src = URL.createObjectURL(file);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Get image dimensions from blob
 * @param {Blob} blob - Image blob
 * @returns {Promise<Object>} Dimensions object with width and height
 */
export const getImageDimensions = (blob) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
      URL.revokeObjectURL(img.src);
    };
    img.onerror = () => {
      reject(new Error("Failed to get image dimensions"));
      URL.revokeObjectURL(img.src);
    };
    img.src = URL.createObjectURL(blob);
  });
};

/**
 * Cleanup any existing default logo constraint violations for a user
 * @param {string} userId - User ID to clean up
 * @returns {Promise<Object>} Cleanup result
 */
const cleanupDefaultLogoConstraints = async (userId) => {
  try {
    // Get all default logos for this user, ordered by most recent first
    const { data: defaultLogos, error: queryError } = await supabase
      .from("user_logos")
      .select("id, created_at, updated_at")
      .eq("user_id", userId)
      .eq("is_default", true)
      .order("created_at", { ascending: false });

    if (queryError) {
      prodLogger.error("❌ Error querying default logos:", queryError);
      return { success: false, error: queryError.message };
    }

    // If more than one default logo exists, fix the constraint violation
    if (defaultLogos && defaultLogos.length > 1) {
      const [keepDefault, ...removeDefaults] = defaultLogos;

      prodLogger.warn(
        `⚠️ Found ${defaultLogos.length} default logos for user ${userId}, fixing constraint violation`
      );

      // Set all but the most recent to false
      const { error: fixError } = await supabase
        .from("user_logos")
        .update({ is_default: false })
        .in(
          "id",
          removeDefaults.map((logo) => logo.id)
        );

      if (fixError) {
        prodLogger.error("❌ Error fixing constraint violation:", fixError);
        return { success: false, error: fixError.message };
      }

      prodLogger.info(
        `✅ Fixed constraint violation: kept ${keepDefault.id} as default, removed ${removeDefaults.length} duplicates`
      );
      return {
        success: true,
        fixed: removeDefaults.length,
        keptDefault: keepDefault.id,
      };
    }

    return { success: true, fixed: 0 };
  } catch (error) {
    prodLogger.error("❌ Error in cleanupDefaultLogoConstraints:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Safely set a logo as default, ensuring no constraint violations
 * @param {string} userId - User ID
 * @param {string} logoId - Logo ID to set as default
 * @returns {Promise<Object>} Result indicating if logo was set as default
 */
const safelySetLogoAsDefault = async (userId, logoId) => {
  try {
    // First, cleanup any existing constraint violations
    const cleanup = await cleanupDefaultLogoConstraints(userId);
    if (!cleanup.success) {
      return { wasSet: false, error: cleanup.error };
    }

    // Check if user has any default logos now
    const { data: existingDefault, error: checkError } = await supabase
      .from("user_logos")
      .select("id")
      .eq("user_id", userId)
      .eq("is_default", true)
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      // PGRST116 = no rows returned
      prodLogger.error("❌ Error checking existing default:", checkError);
      return { wasSet: false, error: checkError.message };
    }

    // If no default exists, set this logo as default
    if (!existingDefault) {
      const { data, error: updateError } = await supabase
        .from("user_logos")
        .update({ is_default: true })
        .eq("id", logoId)
        .eq("user_id", userId)
        .select()
        .single();

      if (updateError) {
        prodLogger.error("❌ Error setting logo as default:", updateError);
        return { wasSet: false, error: updateError.message };
      }

      prodLogger.debug("✅ Set logo as default:", data);
      return { wasSet: true, logo: data };
    }

    return { wasSet: false, reason: "User already has a default logo" };
  } catch (error) {
    prodLogger.error("❌ Error in safelySetLogoAsDefault:", error);
    return { wasSet: false, error: error.message };
  }
};

/**
 * Check if user has any logos and set the provided logo as default if it's their first
 * @param {string} userId - User ID
 * @param {string} logoId - Logo ID to potentially set as default
 * @returns {Promise<Object>} Result indicating if logo was set as default
 */
const checkAndSetFirstLogoAsDefault = async (userId, logoId) => {
  try {
    // First, ensure database consistency
    const cleanup = await cleanupDefaultLogoConstraints(userId);
    if (!cleanup.success) {
      prodLogger.warn("⚠️ Could not cleanup constraints, proceeding carefully");
    }

    // Check how many active logos the user has
    const { count, error: countError } = await supabase
      .from("user_logos")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId)
      .eq("is_active", true);

    if (countError) {
      prodLogger.error("❌ Error checking user logo count:", countError);
      return { wasSet: false, error: countError.message };
    }

    // If this is their first logo, set it as default safely
    if (count === 1) {
      return await safelySetLogoAsDefault(userId, logoId);
    }

    return { wasSet: false, reason: "User already has other logos" };
  } catch (error) {
    prodLogger.error("❌ Error in checkAndSetFirstLogoAsDefault:", error);
    return { wasSet: false, error: error.message };
  }
};

/**
 * Upload logo to Supabase storage
 * @param {File} file - The logo file to upload
 * @param {string} userId - User ID for file organization
 * @param {Object} metadata - Additional metadata (name, description)
 * @returns {Promise<Object>} Upload result with logo data
 */
export const uploadLogo = async (file, userId, metadata = {}) => {
  try {
    prodLogger.debug("🔄 Starting logo upload", {
      fileName: file.name,
      fileSize: file.size,
      userId,
      metadata,
    });

    // Proactively cleanup any constraint violations before upload
    prodLogger.debug("🔧 Proactive constraint cleanup before upload");
    const preCleanup = await cleanupDefaultLogoConstraints(userId);
    if (preCleanup.fixed > 0) {
      prodLogger.info(
        `✅ Pre-upload cleanup: fixed ${preCleanup.fixed} constraint violations`
      );
    }

    // AGGRESSIVE FIX: Ensure this specific user has no default logos before proceeding
    const { error: aggressiveCleanupError } = await supabase
      .from("user_logos")
      .update({ is_default: false })
      .eq("user_id", userId)
      .eq("is_default", true);

    if (aggressiveCleanupError) {
      prodLogger.warn("⚠️ Aggressive cleanup failed:", aggressiveCleanupError);
    } else {
      prodLogger.debug(
        "✅ Aggressive cleanup completed - all logos set to is_default=false"
      );
    }

    // Validate the logo
    const validation = validateLogo(file);
    if (!validation.success) {
      throw new Error(validation.error);
    }

    // Optimize the logo
    const optimizedBlob = await optimizeLogo(file);

    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = LOGO_CONFIG.OPTIMIZATION.format;
    const fileName = `${LOGO_CONFIG.FILE_PREFIX}${timestamp}.${fileExtension}`;
    const filePath = `${userId}/${fileName}`;

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(LOGO_CONFIG.STORAGE_BUCKET)
      .upload(filePath, optimizedBlob, {
        contentType: `image/${fileExtension}`,
        upsert: false, // Don't overwrite existing files
        cacheControl: "3600",
      });

    if (error) {
      throw error;
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage
      .from(LOGO_CONFIG.STORAGE_BUCKET)
      .getPublicUrl(filePath);

    // Get image dimensions
    const dimensions = await getImageDimensions(optimizedBlob);

    // Save logo metadata to database
    // NEVER set is_default during upload to avoid constraint violations
    const logoData = {
      user_id: userId,
      name: metadata.name || file.name.replace(/\.[^/.]+$/, ""), // Remove extension
      description: metadata.description || null,
      file_name: file.name,
      file_size: optimizedBlob.size,
      file_type: `image/${fileExtension}`,
      storage_path: filePath,
      public_url: publicUrl,
      width: dimensions.width,
      height: dimensions.height,
      is_default: false, // Always false during upload - use setDefaultLogo() separately
    };

    prodLogger.debug("🔄 Inserting logo data:", logoData);
    prodLogger.debug("🔍 Confirming is_default value:", logoData.is_default);

    const { data: savedLogo, error: dbError } = await supabase
      .from("user_logos")
      .insert([logoData])
      .select()
      .single();

    if (dbError) {
      // Clean up uploaded file if database save fails
      await supabase.storage
        .from(LOGO_CONFIG.STORAGE_BUCKET)
        .remove([filePath]);

      // Log the actual database error for debugging
      prodLogger.error("❌ Database error during logo save:", dbError);

      // Handle unique constraint violations with improved logic
      if (
        dbError.code === "23505" &&
        dbError.message.includes("unique_user_default_logo")
      ) {
        prodLogger.warn(
          "⚠️ Constraint violation detected, attempting comprehensive fix"
        );

        try {
          // Use our improved cleanup function
          const cleanup = await cleanupDefaultLogoConstraints(userId);

          if (cleanup.success && cleanup.fixed > 0) {
            prodLogger.info(
              `✅ Fixed ${cleanup.fixed} constraint violations, retrying upload`
            );

            // Retry the original insert after cleanup
            const { data: retryLogo, error: retryError } = await supabase
              .from("user_logos")
              .insert([logoData])
              .select()
              .single();

            if (!retryError) {
              // Success after cleanup - continue with normal flow
              const result = {
                success: true,
                logo: retryLogo,
                warnings: validation.warnings || [],
                fixedConstraints: cleanup.fixed,
              };

              // Check if this should be set as default (first logo)
              const shouldSetAsDefault = await checkAndSetFirstLogoAsDefault(
                userId,
                retryLogo.id
              );
              if (shouldSetAsDefault.wasSet) {
                result.logo.is_default = true;
                result.setAsFirstDefault = true;
              }

              prodLogger.debug(
                "✅ Logo uploaded successfully after constraint fix",
                result
              );
              return result;
            } else {
              prodLogger.error(
                "❌ Retry failed even after cleanup:",
                retryError
              );
            }
          }
        } catch (fixError) {
          prodLogger.error("❌ Failed to fix constraint violation:", fixError);
        }

        throw new Error(
          "Database constraint error: Please try uploading again. If the issue persists, contact support."
        );
      }

      throw dbError;
    }

    const result = {
      success: true,
      logo: savedLogo,
      warnings: validation.warnings || [],
    };

    // Check if this is the user's first logo and set as default if so
    const shouldSetAsDefault = await checkAndSetFirstLogoAsDefault(
      userId,
      savedLogo.id
    );
    if (shouldSetAsDefault.wasSet) {
      result.logo.is_default = true;
      result.setAsFirstDefault = true;
    }

    prodLogger.debug("✅ Logo uploaded successfully", result);
    return result;
  } catch (error) {
    prodLogger.error("❌ Error uploading logo:", error);

    errorMonitor.captureError(
      error,
      {
        userId,
        fileName: file?.name,
        fileSize: file?.size,
        metadata,
      },
      ErrorSeverity.MEDIUM
    );

    return {
      success: false,
      error: error.message || "Failed to upload logo",
    };
  }
};

/**
 * Get user's logos
 * @param {string} userId - User ID
 * @param {Object} options - Query options (activeOnly, includeDefault)
 * @returns {Promise<Object>} User's logos
 */
export const getUserLogos = async (userId, options = {}) => {
  try {
    const {
      activeOnly = true,
      includeDefault = true,
      unifiedGrid = false,
    } = options;

    let query = supabase.from("user_logos").select("*").eq("user_id", userId);

    if (activeOnly) {
      query = query.eq("is_active", true);
    }

    const { data: logos, error } = await query;

    if (error) {
      throw error;
    }

    if (unifiedGrid) {
      // For unified grid, sort with default first, then by creation date
      const sortedLogos = logos.sort((a, b) => {
        // Default logo comes first
        if (a.is_default && !b.is_default) return -1;
        if (!a.is_default && b.is_default) return 1;
        // Then sort by creation date (newest first)
        return new Date(b.created_at) - new Date(a.created_at);
      });

      // Map database fields to frontend expected format
      const formattedLogos = sortedLogos.map((logo) => ({
        id: logo.id,
        name: logo.name,
        description: logo.description,
        previewUrl: logo.public_url,
        dimensions: `${logo.width}x${logo.height}`,
        fileSize: `${(logo.file_size / (1024 * 1024)).toFixed(2)} MB`,
        usageCount: logo.usage_count,
        lastUsed: logo.last_used_at,
        isDefault: logo.is_default, // Map snake_case to camelCase
        width: logo.width,
        height: logo.height,
        aspectRatio: logo.aspect_ratio,
        createdAt: logo.created_at,
      }));

      return {
        success: true,
        logos: formattedLogos,
        defaultLogo: formattedLogos.find((logo) => logo.isDefault) || null,
        total: formattedLogos.length,
      };
    }

    // Legacy behavior for backward compatibility
    const sortedLogos = logos.sort(
      (a, b) => new Date(b.created_at) - new Date(a.created_at)
    );

    // Separate default logo if requested
    let defaultLogo = null;
    let otherLogos = sortedLogos;

    if (includeDefault) {
      defaultLogo = sortedLogos.find((logo) => logo.is_default);
      otherLogos = sortedLogos.filter((logo) => !logo.is_default);
    }

    return {
      success: true,
      logos: otherLogos,
      defaultLogo,
      total: sortedLogos.length,
    };
  } catch (error) {
    prodLogger.error("❌ Error fetching user logos:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch logos",
    };
  }
};

/**
 * Check if user has a default logo
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Result with default logo info
 */
export const getUserDefaultLogo = async (userId) => {
  try {
    const { data, error } = await supabase
      .from("user_logos")
      .select("*")
      .eq("user_id", userId)
      .eq("is_default", true)
      .eq("is_active", true)
      .single();

    if (error && error.code !== "PGRST116") {
      // PGRST116 = no rows returned
      throw error;
    }

    return {
      success: true,
      hasDefault: !!data,
      defaultLogo: data || null,
    };
  } catch (error) {
    prodLogger.error("❌ Error checking user default logo:", error);
    return {
      success: false,
      hasDefault: false,
      error: error.message || "Failed to check default logo",
    };
  }
};

/**
 * Set logo as default for user
 * @param {string} logoId - Logo ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Update result
 */
export const setDefaultLogo = async (logoId, userId) => {
  try {
    // The database trigger will handle unsetting other default logos
    const { data, error } = await supabase
      .from("user_logos")
      .update({ is_default: true })
      .eq("id", logoId)
      .eq("user_id", userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      logo: data,
    };
  } catch (error) {
    prodLogger.error("❌ Error setting default logo:", error);
    return {
      success: false,
      error: error.message || "Failed to set default logo",
    };
  }
};

/**
 * Delete logo
 * @param {string} logoId - Logo ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Delete result
 */
export const deleteLogo = async (logoId, userId) => {
  try {
    // Get logo data first
    const { data: logo, error: fetchError } = await supabase
      .from("user_logos")
      .select("*")
      .eq("id", logoId)
      .eq("user_id", userId)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    // Delete from storage
    const { error: storageError } = await supabase.storage
      .from(LOGO_CONFIG.STORAGE_BUCKET)
      .remove([logo.storage_path]);

    if (storageError) {
      prodLogger.warn("⚠️ Failed to delete logo from storage:", storageError);
      // Continue with database deletion even if storage deletion fails
    }

    // Delete from database
    const { error: dbError } = await supabase
      .from("user_logos")
      .delete()
      .eq("id", logoId)
      .eq("user_id", userId);

    if (dbError) {
      throw dbError;
    }

    return {
      success: true,
      message: "Logo deleted successfully",
    };
  } catch (error) {
    prodLogger.error("❌ Error deleting logo:", error);
    return {
      success: false,
      error: error.message || "Failed to delete logo",
    };
  }
};

/**
 * Increment logo usage count (called when logo is used in document generation)
 * @param {string} logoId - Logo ID
 * @returns {Promise<void>}
 */
export const incrementLogoUsage = async (logoId) => {
  try {
    await supabase.rpc("increment_logo_usage", { logo_uuid: logoId });
  } catch (error) {
    prodLogger.warn("⚠️ Failed to increment logo usage:", error);
    // Don't throw error as this is not critical
  }
};

/**
 * Clean up database constraint violations for a specific user
 * This function can be called independently to fix data integrity issues
 * @param {string} userId - User ID to clean up constraints for
 * @returns {Promise<Object>} Cleanup result
 */
export const fixUserLogoConstraints = async (userId) => {
  try {
    prodLogger.info(`🔧 Starting constraint cleanup for user: ${userId}`);

    const cleanup = await cleanupDefaultLogoConstraints(userId);

    if (cleanup.success) {
      if (cleanup.fixed > 0) {
        prodLogger.info(
          `✅ Fixed ${cleanup.fixed} constraint violations for user ${userId}`
        );
        return {
          success: true,
          message: `Fixed ${cleanup.fixed} constraint violations`,
          fixed: cleanup.fixed,
        };
      } else {
        prodLogger.debug(
          `✅ No constraint violations found for user ${userId}`
        );
        return {
          success: true,
          message: "No constraint violations found",
          fixed: 0,
        };
      }
    } else {
      prodLogger.error(
        `❌ Failed to fix constraints for user ${userId}:`,
        cleanup.error
      );
      return {
        success: false,
        error: cleanup.error,
        message: "Failed to fix constraint violations",
      };
    }
  } catch (error) {
    prodLogger.error(
      `❌ Error in fixUserLogoConstraints for user ${userId}:`,
      error
    );
    return {
      success: false,
      error: error.message,
      message: "Unexpected error during constraint cleanup",
    };
  }
};

// Export configuration for use in components
export { LOGO_CONFIG };
