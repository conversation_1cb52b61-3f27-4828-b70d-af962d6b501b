/**
 * Database-Only Document Storage Service
 * Replaces localStorage approach with direct database operations
 * Following Designrr's pattern of server-side content storage
 */

import { projectsService } from "./projectsService";
import { documentStorageCalculationService } from "./documentStorageCalculationService";

import { prodLogger } from "../utils/prodLogger.js";
// Configuration for database-only storage
const STORAGE_CONFIG = {
  // Auto-save configuration
  AUTO_SAVE_DEBOUNCE: 2000, // 2 seconds debounce for auto-save
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second base delay

  // Cache configuration
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes cache TTL
  MAX_CACHE_SIZE: 10, // Maximum cached documents

  // Database save configuration
  SAVE_TIMEOUT: 60000, // 60 seconds timeout for large documents
  LARGE_DOCUMENT_THRESHOLD: 2 * 1024 * 1024, // 2MB threshold for large documents
  LARGE_DOCUMENT_TIMEOUT: 90000, // 90 seconds timeout for very large documents
};

class DocumentStorageService {
  constructor() {
    this.cache = new Map();
    this.saveTimeouts = new Map();
    this.pendingSaves = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the service
   */
  initialize() {
    if (this.isInitialized) return;
    this.isInitialized = true;
  }

  /**
   * Load document from database with caching
   */
  async loadDocument(documentId, options = {}) {
    const { forceFresh = false } = options;

    try {
      // Check cache first (unless forcing fresh load)
      if (!forceFresh) {
        const cached = this.getCachedDocument(documentId);
        if (cached) {
          prodLogger.debug(`📖 Loading document ${documentId} from cache`);
          return { success: true, data: cached, source: "cache" };
        }
      }

      // Fetch from database
      const result = await projectsService.getProject(documentId, true); // Include content

      if (result.success) {
        // Cache the document
        this.cacheDocument(documentId, result.data);

        prodLogger.debug(`✅ Document ${documentId} loaded from database`);
        return { success: true, data: result.data, source: "database" };
      } else {
        prodLogger.error(
          `❌ Failed to load document ${documentId}:`,
          result.error
        );
        return { success: false, error: result.error };
      }
    } catch (error) {
      prodLogger.error(`❌ Error loading document ${documentId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Save document content to database with debounced auto-save
   */
  async saveDocument(documentId, documentData, options = {}) {
    const { immediate = false, skipCache = false } = options;

    try {
      // Update cache immediately for optimistic UI
      if (!skipCache) {
        this.cacheDocument(documentId, documentData);
      }

      if (immediate) {
        // Immediate save (for critical operations)
        return await this.performDatabaseSave(documentId, documentData);
      } else {
        // Debounced auto-save
        return this.scheduleAutoSave(documentId, documentData);
      }
    } catch (error) {
      prodLogger.error(`❌ Error saving document ${documentId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Schedule debounced auto-save
   */
  scheduleAutoSave(documentId, documentData) {
    // Clear existing timeout
    if (this.saveTimeouts.has(documentId)) {
      clearTimeout(this.saveTimeouts.get(documentId));
    }

    // Store pending data
    this.pendingSaves.set(documentId, documentData);

    // Schedule new save
    const timeout = setTimeout(async () => {
      const pendingData = this.pendingSaves.get(documentId);
      if (pendingData) {
        await this.performDatabaseSave(documentId, pendingData);
        this.pendingSaves.delete(documentId);
      }
      this.saveTimeouts.delete(documentId);
    }, STORAGE_CONFIG.AUTO_SAVE_DEBOUNCE);

    this.saveTimeouts.set(documentId, timeout);

    prodLogger.debug(`⏰ Auto-save scheduled for document ${documentId}`);
    return { success: true, scheduled: true };
  }

  /**
   * Optimize document data before save to reduce size and prevent timeouts
   */
  async optimizeDocumentForSave(documentData) {
    try {
      // Check if document has editor HTML with potential large images
      if (!documentData.generatedContent?.editorHTML) {
        return documentData; // No HTML content to optimize
      }

      const editorHTML = documentData.generatedContent.editorHTML;
      const originalSize = JSON.stringify(documentData).length;

      // Check if optimization is needed (document > 1.5MB)
      if (originalSize < 1.5 * 1024 * 1024) {
        return documentData; // Document is small enough
      }

      prodLogger.debug(
        `🗜️ Optimizing large document (${(originalSize / 1024 / 1024).toFixed(
          2
        )}MB)...`
      );

      // Import compression utility
      const { compressBase64Image } = await import(
        "../utils/imageOptimization.js"
      );

      // Find and compress large base64 images that aren't already compressed
      let optimizedHTML = editorHTML;
      let compressionCount = 0;
      let totalSizeSaved = 0;

      // Regex to find large base64 images (>100KB estimated)
      const largeImageRegex =
        /<img([^>]*?)src="data:image\/([^;]+);base64,([A-Za-z0-9+/]{100000,}[=]{0,2})"([^>]*?)>/gi;

      const imageMatches = [...editorHTML.matchAll(largeImageRegex)];

      for (const match of imageMatches) {
        try {
          const [fullMatch, beforeSrc, imageType, base64Data, afterSrc] = match;
          const originalImageSize = Math.ceil(base64Data.length * 0.75);

          // Skip if already compressed (WebP format usually indicates compression)
          if (imageType === "webp" && base64Data.length < 500000) {
            continue; // Already optimized
          }

          // Compress the image
          const compressionResult = await compressBase64Image(base64Data, {
            format: "webp",
            quality: 0.75,
            maxWidth: 1024,
            maxHeight: 1024,
          });

          // Replace in HTML
          const newImageTag = `<img${beforeSrc}src="data:${compressionResult.mimeType};base64,${compressionResult.base64Data}"${afterSrc}>`;
          optimizedHTML = optimizedHTML.replace(fullMatch, newImageTag);

          compressionCount++;
          totalSizeSaved +=
            originalImageSize - compressionResult.compressedSize;

          prodLogger.debug(
            `✅ Compressed image ${compressionCount}: ${(
              originalImageSize / 1024
            ).toFixed(1)}KB → ${(
              compressionResult.compressedSize / 1024
            ).toFixed(1)}KB`
          );
        } catch (compressionError) {
          prodLogger.warn(
            `⚠️ Failed to compress image ${compressionCount + 1}:`,
            compressionError.message
          );
          // Continue with other images
        }
      }

      if (compressionCount > 0) {
        const optimizedData = {
          ...documentData,
          generatedContent: {
            ...documentData.generatedContent,
            editorHTML: optimizedHTML,
          },
        };

        const newSize = JSON.stringify(optimizedData).length;
        const sizeSaved = originalSize - newSize;

        prodLogger.debug(
          `🎉 Document optimization complete: ${compressionCount} images compressed, ${(
            sizeSaved /
            1024 /
            1024
          ).toFixed(2)}MB saved`
        );
        prodLogger.debug(
          `📊 Document size: ${(originalSize / 1024 / 1024).toFixed(2)}MB → ${(
            newSize /
            1024 /
            1024
          ).toFixed(2)}MB`
        );

        return optimizedData;
      }

      return documentData; // No optimization needed
    } catch (error) {
      prodLogger.warn(
        `⚠️ Document optimization failed, proceeding with original:`,
        error.message
      );
      return documentData; // Return original data if optimization fails
    }
  }

  /**
   * Perform actual database save with retry logic and transaction safety
   */
  async performDatabaseSave(documentId, documentData, attempt = 1) {
    try {
      prodLogger.debug(
        `💾 Saving document ${documentId} to database (attempt ${attempt})`
      );

      // Validate document data before saving
      if (!documentData) {
        throw new Error("Document data is required");
      }

      // 🗜️ OPTIMIZE: Compress large images before save to prevent timeouts
      const optimizedDocumentData = await this.optimizeDocumentForSave(
        documentData
      );

      // Prepare data for database with safe JSONB handling (use optimized data)
      const updateData = {
        generated_content: optimizedDocumentData.generatedContent || null,
        questionnaire_data: optimizedDocumentData,
        word_count: optimizedDocumentData.generatedContent?.wordCount || 0,
        chapter_count:
          optimizedDocumentData.generatedContent?.chapters?.length || 0,
        updated_at: new Date().toISOString(),
      };

      // Validate JSONB size to prevent database errors
      const jsonSize = JSON.stringify(updateData).length;
      const isLargeDocument =
        jsonSize > STORAGE_CONFIG.LARGE_DOCUMENT_THRESHOLD;

      if (jsonSize > 1048576) {
        // 1MB limit
        prodLogger.warn(
          `Document data is large (${(jsonSize / 1024 / 1024).toFixed(
            2
          )}MB), consider optimization`
        );
      }

      // Use dynamic timeout based on document size
      const saveTimeout = isLargeDocument
        ? STORAGE_CONFIG.LARGE_DOCUMENT_TIMEOUT
        : STORAGE_CONFIG.SAVE_TIMEOUT;

      prodLogger.debug(
        `💾 Using ${saveTimeout / 1000}s timeout for ${
          isLargeDocument ? "large" : "normal"
        } document (${(jsonSize / 1024 / 1024).toFixed(2)}MB)`
      );

      // Save to database with dynamic timeout
      const savePromise = projectsService.updateProject(documentId, updateData);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () =>
            reject(
              new Error(`Database save timeout after ${saveTimeout / 1000}s`)
            ),
          saveTimeout
        )
      );

      const result = await Promise.race([savePromise, timeoutPromise]);

      if (result.success) {
        // Update cache with optimized data
        this.cacheDocument(documentId, {
          ...optimizedDocumentData,
          ...result.data,
          lastSaved: new Date().toISOString(),
        });

        // Recalculate storage usage for the user (async, don't wait)
        if (optimizedDocumentData.userId) {
          documentStorageCalculationService.updateUserStorageUsage(optimizedDocumentData.userId)
            .then(storageUsage => {
              prodLogger.debug(`✅ Storage usage updated for user: ${storageUsage}MB`);
            })
            .catch(error => {
              prodLogger.error('Failed to update storage usage:', error);
              // Don't fail the document save if storage calculation fails
            });
        }

        prodLogger.debug(`✅ Document ${documentId} saved to database`);
        return { success: true, data: result.data };
      } else {
        throw new Error(result.error?.message || "Database save failed");
      }
    } catch (error) {
      prodLogger.error(
        `❌ Database save failed for document ${documentId}:`,
        error
      );

      // Enhanced retry logic with exponential backoff
      if (attempt < STORAGE_CONFIG.RETRY_ATTEMPTS) {
        // Calculate delay with exponential backoff
        const baseDelay = STORAGE_CONFIG.RETRY_DELAY * Math.pow(2, attempt - 1);
        const jitter = Math.random() * 0.1 * baseDelay; // Add 10% jitter
        const delay = Math.min(baseDelay + jitter, 10000); // Cap at 10 seconds

        // For large documents, add extra delay to allow database recovery
        const isLargeDocument =
          JSON.stringify(documentData).length >
          STORAGE_CONFIG.LARGE_DOCUMENT_THRESHOLD;
        const finalDelay = isLargeDocument ? delay * 1.5 : delay;

        prodLogger.debug(
          `🔄 Retrying save in ${Math.round(finalDelay)}ms (attempt ${
            attempt + 1
          }/${STORAGE_CONFIG.RETRY_ATTEMPTS})...`
        );

        await new Promise((resolve) => setTimeout(resolve, finalDelay));
        return this.performDatabaseSave(documentId, documentData, attempt + 1);
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Force immediate save of all pending documents
   */
  async flushPendingSaves() {
    const pendingIds = Array.from(this.pendingSaves.keys());

    if (pendingIds.length === 0) {
      return { success: true, saved: 0 };
    }

    const results = await Promise.allSettled(
      pendingIds.map(async (documentId) => {
        const data = this.pendingSaves.get(documentId);
        const result = await this.performDatabaseSave(documentId, data);

        if (result.success) {
          this.pendingSaves.delete(documentId);
          if (this.saveTimeouts.has(documentId)) {
            clearTimeout(this.saveTimeouts.get(documentId));
            this.saveTimeouts.delete(documentId);
          }
        }

        return result;
      })
    );

    const successCount = results.filter(
      (r) => r.status === "fulfilled" && r.value.success
    ).length;
    prodLogger.debug(
      `✅ Flush completed: ${successCount}/${pendingIds.length} successful`
    );

    return { success: true, saved: successCount, total: pendingIds.length };
  }

  /**
   * Ensure specific document is saved immediately (for critical operations)
   */
  async ensureDocumentSaved(documentId) {
    // Check if document has pending saves
    if (this.pendingSaves.has(documentId)) {
      prodLogger.debug(
        `⚡ Ensuring document ${documentId} is saved immediately`
      );

      const data = this.pendingSaves.get(documentId);
      const result = await this.performDatabaseSave(documentId, data);

      if (result.success) {
        this.pendingSaves.delete(documentId);
        if (this.saveTimeouts.has(documentId)) {
          clearTimeout(this.saveTimeouts.get(documentId));
          this.saveTimeouts.delete(documentId);
        }
        prodLogger.debug(`✅ Document ${documentId} saved successfully`);
      }

      return result;
    }

    prodLogger.debug(`📝 Document ${documentId} has no pending saves`);
    return { success: true, message: "No pending saves" };
  }

  /**
   * Create a blank document for "start from scratch" functionality
   */
  async createBlankDocument(userId) {
    try {
      prodLogger.debug(`📄 Creating blank document for user ${userId}`);

      const blankDocumentData = {
        // Required fields for projectsService validation
        title: "Untitled Document",
        document_type: "ebook",
        category: "eBooks",
        
        // Additional project fields
        description: "A new document created from scratch",
        status: 'in_progress',
        progress: 25,
        format: "pdf",
        
        // Questionnaire data structure
        questionnaire_data: {
          documentPurpose: {
            primaryType: "ebook",
            format: "pdf", 
            baseline: "scratch"
          },
          topicAndNiche: {
            mainTopic: "",
            language: "english"
          }
        },
        
        // Generated content structure
        generated_content: {
          title: "Untitled Document",
          chapters: [],
          wordCount: 0,
          totalPages: 1
        },
        
        // Timestamps
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Create the project in the database
      const result = await projectsService.createProject(blankDocumentData);
      
      if (result.success) {
        const documentId = result.data.id;
        prodLogger.debug(`✅ Blank document created with ID: ${documentId}`);
        
        // Cache the newly created document
        this.cacheDocument(documentId, {
          ...blankDocumentData,
          ...result.data,
          documentId: documentId,
          projectId: documentId
        });
        
        return { success: true, documentId: documentId, data: result.data };
      } else {
        throw new Error(result.error?.message || 'Failed to create blank document');
      }
    } catch (error) {
      prodLogger.error(`❌ Error creating blank document:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cache management methods
   */
  cacheDocument(documentId, documentData) {
    // Implement LRU cache
    if (this.cache.size >= STORAGE_CONFIG.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(documentId, {
      data: documentData,
      timestamp: Date.now(),
      ttl: Date.now() + STORAGE_CONFIG.CACHE_TTL,
    });
  }

  getCachedDocument(documentId) {
    const cached = this.cache.get(documentId);

    if (!cached) return null;

    // Check TTL
    if (Date.now() > cached.ttl) {
      this.cache.delete(documentId);
      return null;
    }

    // Move to end (LRU)
    this.cache.delete(documentId);
    this.cache.set(documentId, cached);

    return cached.data;
  }

  invalidateCache(documentId) {
    this.cache.delete(documentId);
    prodLogger.debug(`🗑️ Cache invalidated for document ${documentId}`);
  }

  clearCache() {
    this.cache.clear();
    prodLogger.debug("🧹 Document cache cleared");
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: STORAGE_CONFIG.MAX_CACHE_SIZE,
      documents: Array.from(this.cache.keys()),
      pendingSaves: Array.from(this.pendingSaves.keys()).length,
    };
  }

  /**
   * Cleanup method for component unmount
   */
  cleanup() {
    // Clear all timeouts
    for (const timeout of this.saveTimeouts.values()) {
      clearTimeout(timeout);
    }
    this.saveTimeouts.clear();

    // Flush any pending saves
    this.flushPendingSaves();

    prodLogger.debug("🧹 Document storage service cleaned up");
  }
}

// Create singleton instance
export const documentStorage = new DocumentStorageService();

// Auto-initialize
if (typeof window !== "undefined") {
  documentStorage.initialize();

  // Flush pending saves before page unload
  window.addEventListener("beforeunload", () => {
    documentStorage.flushPendingSaves();
  });
}

export default documentStorage;
