/**
 * RapidDoc AI - Projects Service
 * Clean implementation for core project management operations
 */

import { supabase } from "../lib/supabase";
import notificationService, {
  createProjectNotification,
} from "./notificationService.js";
import { NOTIFICATION_TYPES } from "./userNotificationService.js";
import { userActivityService } from "./userActivityService.js";
import { documentStorageCalculationService } from "./documentStorageCalculationService.js";

import { prodLogger } from "../utils/prodLogger.js";
// Error handling utility
class ProjectsServiceError extends Error {
  constructor(message, code = "UNKNOWN_ERROR", details = null) {
    super(message);
    this.name = "ProjectsServiceError";
    this.code = code;
    this.details = details;
  }
}

// Service response wrapper
const createResponse = (success, data = null, error = null) => ({
  success,
  data,
  error: error
    ? {
        message: error.message,
        code: error.code || "UNKNOWN_ERROR",
        details: error.details || null,
      }
    : null,
});

// Validation utilities
const validateProjectData = (projectData) => {
  const required = ["title", "document_type", "category"];
  const missing = required.filter((field) => !projectData[field]);

  if (missing.length > 0) {
    throw new ProjectsServiceError(
      `Missing required fields: ${missing.join(", ")}`,
      "VALIDATION_ERROR",
      { missingFields: missing }
    );
  }

  const validTypes = ["ebook", "academic", "business"];
  if (!validTypes.includes(projectData.document_type)) {
    throw new ProjectsServiceError(
      `Invalid document type: ${projectData.document_type}`,
      "VALIDATION_ERROR",
      { validTypes }
    );
  }

  const validCategories = ["eBooks", "Academic", "Business"];
  if (!validCategories.includes(projectData.category)) {
    throw new ProjectsServiceError(
      `Invalid category: ${projectData.category}`,
      "VALIDATION_ERROR",
      { validCategories }
    );
  }
};

/**
 * Projects Service Class
 * Handles all project-related database operations
 */
export class ProjectsService {
  constructor() {
    this.supabase = supabase;
  }

  /**
   * Get all projects for the current user
   * @param {Object} options - Query options
   * @param {string} options.category - Filter by category
   * @param {string} options.status - Filter by status
   * @param {string} options.sortBy - Sort field (created_at, updated_at, title, progress)
   * @param {string} options.sortOrder - Sort order (asc, desc)
   * @param {number} options.limit - Limit results
   * @param {number} options.offset - Offset for pagination
   * @param {boolean} options.includeContent - Whether to include generated_content for thumbnail extraction
   * @returns {Promise<Object>} Service response with projects array
   */
  async getUserProjects(options = {}) {
    try {
      const {
        category,
        status,
        sortBy = "updated_at",
        sortOrder = "desc",
        limit = 50,
        offset = 0,
        includeContent = false,
      } = options;

      // Base fields always included
      let selectFields = `
        id, title, description, document_type, category, status, progress,
        word_count, chapter_count, thumbnail_url, extracted_thumbnail_url, format,
        created_at, updated_at, last_accessed_at
      `;

      // Optionally include content for thumbnail extraction
      if (includeContent) {
        selectFields += ", generated_content";
      }

      let query = this.supabase
        .from("projects")
        .select(selectFields)
        .is("deleted_at", null)
        .range(offset, offset + limit - 1);

      // Apply filters
      if (category) {
        query = query.eq("category", category);
      }
      if (status) {
        query = query.eq("status", status);
      }

      // Apply sorting
      const ascending = sortOrder === "asc";
      query = query.order(sortBy, { ascending });

      const { data, error, count } = await query;

      if (error) {
        throw new ProjectsServiceError(
          `Failed to fetch projects: ${error.message}`,
          "DATABASE_ERROR",
          error
        );
      }

      return createResponse(true, {
        projects: data || [],
        total: count,
        limit,
        offset,
      });
    } catch (error) {
      prodLogger.error("Error fetching user projects:", error);
      return createResponse(false, null, error);
    }
  }

  /**
   * Get a single project by ID
   * @param {string} projectId - Project UUID
   * @param {boolean} includeContent - Whether to include questionnaire and generated content
   * @returns {Promise<Object>} Service response with project data
   */
  async getProject(projectId, includeContent = false) {
    try {
      if (!projectId) {
        throw new ProjectsServiceError(
          "Project ID is required",
          "VALIDATION_ERROR"
        );
      }

      let selectFields = `
        id, title, description, document_type, category, status, progress,
        word_count, chapter_count, thumbnail_url, extracted_thumbnail_url, format,
        created_at, updated_at, last_accessed_at
      `;

      if (includeContent) {
        selectFields += ", questionnaire_data, generated_content";
      }

      const { data, error } = await this.supabase
        .from("projects")
        .select(selectFields)
        .eq("id", projectId)
        .is("deleted_at", null)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          throw new ProjectsServiceError("Project not found", "NOT_FOUND");
        }
        throw new ProjectsServiceError(
          `Failed to fetch project: ${error.message}`,
          "DATABASE_ERROR",
          error
        );
      }

      // Update last accessed time
      await this.updateProject(projectId, {
        last_accessed_at: new Date().toISOString(),
      });

      return createResponse(true, data);
    } catch (error) {
      prodLogger.error("Error fetching project:", error);
      return createResponse(false, null, error);
    }
  }

  /**
   * Create a new project
   * @param {Object} projectData - Project data including questionnaire_data and generated_content
   * @returns {Promise<Object>} Service response with created project
   */
  async createProject(projectData) {
    try {
      // Validate project data
      validateProjectData(projectData);

      if (!projectData.questionnaire_data) {
        throw new ProjectsServiceError(
          "Questionnaire data is required",
          "VALIDATION_ERROR"
        );
      }

      // Get current user
      const {
        data: { user },
        error: userError,
      } = await this.supabase.auth.getUser();
      if (userError || !user) {
        throw new ProjectsServiceError("User not authenticated", "AUTH_ERROR");
      }

      // Calculate word count and chapter count from generated content
      const wordCount = projectData.generated_content
        ? this.calculateWordCount(projectData.generated_content)
        : 0;
      const chapterCount = projectData.generated_content?.chapters?.length || 0;

      // Prepare project data
      const projectToCreate = {
        user_id: user.id,
        title: projectData.title,
        description: projectData.description || null,
        document_type: projectData.document_type,
        category: projectData.category,
        status: projectData.status || "draft",
        progress: projectData.progress || 0,
        format: projectData.format || "pdf",
        thumbnail_url: projectData.thumbnail_url || null,
        questionnaire_data: projectData.questionnaire_data,
        generated_content: projectData.generated_content || null,
        word_count: wordCount,
        chapter_count: chapterCount,
      };

      // Create project
      const { data: project, error: projectError } = await this.supabase
        .from("projects")
        .insert([projectToCreate])
        .select()
        .single();

      if (projectError) {
        // Show error notification
        const errorNotification = createProjectNotification(
          NOTIFICATION_TYPES.ERROR,
          "Project Creation Failed",
          `Failed to create project "${projectData.title}": ${projectError.message}`,
          { persistent: true }
        );
        notificationService.add(errorNotification);

        throw new ProjectsServiceError(
          `Failed to create project: ${projectError.message}`,
          "DATABASE_ERROR",
          projectError
        );
      }

      // Show success notification
      const successNotification = createProjectNotification(
        NOTIFICATION_TYPES.SUCCESS,
        "Project Created",
        `Project "${project.title}" has been created successfully.`,
        { duration: 4000 }
      );
      notificationService.add(successNotification);

      // Track document creation usage
      try {
        await userActivityService.trackDocumentCreation(user.id);
        prodLogger.debug("✅ Document creation usage tracked successfully");
      } catch (usageError) {
        // Non-fatal error - log but don't fail the entire operation
        prodLogger.error(
          "Failed to track document creation usage:",
          usageError
        );
      }

      // Recalculate storage usage for the user (async, don't wait)
      try {
        documentStorageCalculationService
          .updateUserStorageUsage(user.id)
          .then((storageUsage) => {
            prodLogger.debug(
              `✅ Storage usage updated after document creation: ${storageUsage}MB`
            );
          })
          .catch((error) => {
            prodLogger.error(
              "Failed to update storage usage after document creation:",
              error
            );
            // Don't fail the document creation if storage calculation fails
          });
      } catch (error) {
        prodLogger.error("Error initiating storage usage calculation:", error);
      }

      return createResponse(true, project);
    } catch (error) {
      prodLogger.error("Error creating project:", error);

      // Show error notification if not already shown
      if (!(error instanceof ProjectsServiceError)) {
        const errorNotification = createProjectNotification(
          NOTIFICATION_TYPES.ERROR,
          "Project Creation Failed",
          `An error occurred while creating the project: ${error.message}`,
          { persistent: true }
        );
        notificationService.add(errorNotification);
      }

      return createResponse(false, null, error);
    }
  }

  /**
   * Update an existing project
   * @param {string} projectId - Project UUID
   * @param {Object} updates - Fields to update
   * @returns {Promise<Object>} Service response with updated project
   */
  async updateProject(projectId, updates) {
    try {
      if (!projectId) {
        throw new ProjectsServiceError(
          "Project ID is required",
          "VALIDATION_ERROR"
        );
      }

      // Remove fields that shouldn't be updated directly
      const { id, user_id, created_at, deleted_at, ...allowedUpdates } =
        updates;

      if (Object.keys(allowedUpdates).length === 0) {
        throw new ProjectsServiceError(
          "No valid fields to update",
          "VALIDATION_ERROR"
        );
      }

      // Update last_accessed_at
      allowedUpdates.last_accessed_at = new Date().toISOString();

      // If content has changed, we let the database trigger handle thumbnail extraction

      const { data, error } = await this.supabase
        .from("projects")
        .update(allowedUpdates)
        .eq("id", projectId)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          throw new ProjectsServiceError(
            "Project not found or access denied",
            "NOT_FOUND"
          );
        }
        throw new ProjectsServiceError(
          `Failed to update project: ${error.message}`,
          "DATABASE_ERROR",
          error
        );
      }

      return createResponse(true, data);
    } catch (error) {
      prodLogger.error("Error updating project:", error);
      return createResponse(false, null, error);
    }
  }

  /**
   * Delete a project (soft delete)
   * @param {string} projectId - Project UUID
   * @returns {Promise<Object>} Service response
   */
  async deleteProject(projectId) {
    try {
      if (!projectId) {
        throw new ProjectsServiceError(
          "Project ID is required",
          "VALIDATION_ERROR"
        );
      }

      // Soft delete by setting deleted_at timestamp
      const { data, error } = await this.supabase
        .from("projects")
        .update({ deleted_at: new Date().toISOString() })
        .eq("id", projectId)
        .select()
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          const errorNotification = createProjectNotification(
            NOTIFICATION_TYPES.ERROR,
            "Project Not Found",
            "The project could not be found or you do not have permission to delete it.",
            { persistent: true }
          );
          notificationService.add(errorNotification);

          throw new ProjectsServiceError(
            "Project not found or access denied",
            "NOT_FOUND"
          );
        }

        const errorNotification = createProjectNotification(
          NOTIFICATION_TYPES.ERROR,
          "Project Deletion Failed",
          `Failed to delete project: ${error.message}`,
          { persistent: true }
        );
        notificationService.add(errorNotification);

        throw new ProjectsServiceError(
          `Failed to delete project: ${error.message}`,
          "DATABASE_ERROR",
          error
        );
      }

      // Show success notification
      const successNotification = createProjectNotification(
        NOTIFICATION_TYPES.INFO,
        "Project Deleted",
        `Project "${data.title}" has been deleted successfully.`,
        { duration: 4000 }
      );
      notificationService.add(successNotification);

      // Recalculate storage usage for the user after deletion (async, don't wait)
      try {
        documentStorageCalculationService
          .updateUserStorageUsage(data.user_id)
          .then((storageUsage) => {
            prodLogger.debug(
              `✅ Storage usage updated after document deletion: ${storageUsage}MB`
            );
          })
          .catch((error) => {
            prodLogger.error(
              "Failed to update storage usage after document deletion:",
              error
            );
          });
      } catch (error) {
        prodLogger.error(
          "Error initiating storage usage calculation after deletion:",
          error
        );
      }

      return createResponse(true, { deleted: true, project: data });
    } catch (error) {
      prodLogger.error("Error deleting project:", error);

      // Show error notification if not already shown
      if (!(error instanceof ProjectsServiceError)) {
        const errorNotification = createProjectNotification(
          NOTIFICATION_TYPES.ERROR,
          "Project Deletion Failed",
          `An error occurred while deleting the project: ${error.message}`,
          { persistent: true }
        );
        notificationService.add(errorNotification);
      }

      return createResponse(false, null, error);
    }
  }

  // Duplicate functionality removed as per requirements

  // Utility methods
  calculateWordCount(generatedContent) {
    try {
      let totalWords = 0;

      if (generatedContent.introduction?.content) {
        totalWords += this.countWordsInText(
          generatedContent.introduction.content
        );
      }

      if (generatedContent.chapters) {
        generatedContent.chapters.forEach((chapter) => {
          if (chapter.content) {
            totalWords += this.countWordsInText(chapter.content);
          }
        });
      }

      if (generatedContent.conclusion?.content) {
        totalWords += this.countWordsInText(
          generatedContent.conclusion.content
        );
      }

      return totalWords;
    } catch (error) {
      prodLogger.warn("Error calculating word count:", error);
      return 0;
    }
  }

  countWordsInText(text) {
    if (!text || typeof text !== "string") return 0;
    // Remove markdown formatting and count words
    const cleanText = text.replace(/[#*_`\[\]()]/g, "").trim();
    return cleanText ? cleanText.split(/\s+/).length : 0;
  }
}

// Export singleton instance
export const projectsService = new ProjectsService();
export default projectsService;
