import { supabase } from "../lib/supabase";
import { prodLogger } from "../utils/prodLogger";
import stripeService from "./stripeService";
import usageTrackingService from "./usageTrackingService";

/**
 * Subscription Service
 * Manages subscription tiers, upgrades, downgrades, and tier-specific logic
 */
class SubscriptionService {
  constructor() {
    this.tierHierarchy = ["free", "basic", "standard", "pro"];
    this.cache = new Map();
    this.cacheExpiry = 10 * 60 * 1000; // 10 minutes cache
  }

  /**
   * Get subscription plans from database
   */
  async getSubscriptionPlans() {
    try {
      const cacheKey = "subscription_plans";
      const cached = this.cache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }

      const { data, error } = await supabase
        .from("subscription_plans")
        .select("*")
        .eq("is_active", true)
        .order("price_monthly", { ascending: true });

      if (error) {
        prodLogger.error("Failed to get subscription plans:", error);
        throw error;
      }

      // Cache the result
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now(),
      });

      return data;
    } catch (error) {
      prodLogger.error("Error getting subscription plans:", error);
      throw error;
    }
  }

  /**
   * Get user's current subscription details
   */
  async getUserSubscription(userId) {
    try {
      const { data, error } = await supabase
        .from("user_profiles")
        .select(
          `
          subscription_tier,
          subscription_status,
          subscription_expires_at,
          trial_ends_at,
          current_period_start,
          current_period_end,
          cancel_at_period_end,
          stripe_customer_id,
          stripe_subscription_id
        `
        )
        .eq("id", userId)
        .single();

      if (error) {
        prodLogger.error("Failed to get user subscription:", error);
        throw error;
      }

      // Enrich with plan details
      const plans = await this.getSubscriptionPlans();
      const currentPlan = plans.find(
        (plan) => plan.tier === data.subscription_tier
      );

      return {
        ...data,
        plan_details: currentPlan,
        is_trial: data.subscription_status === "trial",
        is_active: ["active", "trial"].includes(data.subscription_status),
        days_until_trial_end: data.trial_ends_at
          ? Math.ceil(
              (new Date(data.trial_ends_at) - new Date()) /
                (1000 * 60 * 60 * 24)
            )
          : null,
        days_until_renewal: data.current_period_end
          ? Math.ceil(
              (new Date(data.current_period_end) - new Date()) /
                (1000 * 60 * 60 * 24)
            )
          : null,
      };
    } catch (error) {
      prodLogger.error("Error getting user subscription:", error);
      throw error;
    }
  }

  /**
   * Start a subscription for a user
   */
  async startSubscription(userId, tier, billingPeriod = "monthly") {
    try {
      const { data: user } = await supabase
        .from("user_profiles")
        .select("email, full_name")
        .eq("id", userId)
        .single();

      if (!user) {
        throw new Error("User not found");
      }

      // Use Stripe service to create checkout session
      await stripeService.subscribeToTier(
        tier,
        billingPeriod,
        userId,
        user.email
      );

      prodLogger.info(`Subscription started for user ${userId}, tier: ${tier}`);
    } catch (error) {
      prodLogger.error("Error starting subscription:", error);
      throw error;
    }
  }

  /**
   * Upgrade user's subscription
   */
  async upgradeSubscription(userId, newTier) {
    try {
      const currentSub = await this.getUserSubscription(userId);

      if (!this.isUpgrade(currentSub.subscription_tier, newTier)) {
        throw new Error("Invalid upgrade path");
      }

      // For now, redirect to Stripe customer portal for upgrades
      await stripeService.redirectToCustomerPortal(userId);

      prodLogger.info(
        `Upgrade initiated for user ${userId} from ${currentSub.subscription_tier} to ${newTier}`
      );
    } catch (error) {
      prodLogger.error("Error upgrading subscription:", error);
      throw error;
    }
  }

  /**
   * Cancel user's subscription
   */
  async cancelSubscription(userId, cancelImmediately = false) {
    try {
      if (cancelImmediately) {
        // Cancel immediately and downgrade to free
        await this.updateSubscriptionTier(userId, "free", "cancelled");
      } else {
        // Cancel at period end
        await stripeService.cancelSubscription(userId);
      }

      prodLogger.info(`Subscription cancelled for user ${userId}`);
    } catch (error) {
      prodLogger.error("Error cancelling subscription:", error);
      throw error;
    }
  }

  /**
   * Resume a cancelled subscription
   */
  async resumeSubscription(userId) {
    try {
      await stripeService.resumeSubscription(userId);
      prodLogger.info(`Subscription resumed for user ${userId}`);
    } catch (error) {
      prodLogger.error("Error resuming subscription:", error);
      throw error;
    }
  }

  /**
   * Update subscription tier (internal use, typically called by webhooks)
   */
  async updateSubscriptionTier(
    userId,
    newTier,
    newStatus = "active",
    stripeData = {}
  ) {
    try {
      const { error } = await supabase.rpc("update_subscription_tier", {
        user_uuid: userId,
        new_tier: newTier,
        new_status: newStatus,
        stripe_customer_id_param: stripeData.customerId || null,
        stripe_subscription_id_param: stripeData.subscriptionId || null,
      });

      if (error) {
        prodLogger.error("Failed to update subscription tier:", error);
        throw error;
      }

      // Clear cache
      this.cache.clear();
      usageTrackingService.clearCache();

      prodLogger.info(
        `Subscription tier updated for user ${userId}: ${newTier} (${newStatus})`
      );
    } catch (error) {
      prodLogger.error("Error updating subscription tier:", error);
      throw error;
    }
  }

  /**
   * Check if user has access to a feature
   */
  async hasFeatureAccess(userId, feature) {
    try {
      const subscription = await this.getUserSubscription(userId);

      if (!subscription.is_active) {
        return false;
      }

      const features = await usageTrackingService.getFeatureAccess(userId);
      return features[feature] || false;
    } catch (error) {
      prodLogger.error("Error checking feature access:", error);
      return false; // Fail safe
    }
  }

  /**
   * Get subscription history for a user
   */
  async getSubscriptionHistory(userId, limit = 10) {
    try {
      const { data, error } = await supabase
        .from("subscription_history")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .limit(limit);

      if (error) {
        prodLogger.error("Failed to get subscription history:", error);
        throw error;
      }

      return data;
    } catch (error) {
      prodLogger.error("Error getting subscription history:", error);
      throw error;
    }
  }

  /**
   * Get billing history for a user
   */
  async getBillingHistory(userId, limit = 10) {
    try {
      const { data, error } = await supabase
        .from("billing_history")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .limit(limit);

      if (error) {
        prodLogger.error("Failed to get billing history:", error);
        throw error;
      }

      return data;
    } catch (error) {
      prodLogger.error("Error getting billing history:", error);
      throw error;
    }
  }

  /**
   * Check if a tier change is an upgrade
   */
  isUpgrade(currentTier, newTier) {
    const currentIndex = this.tierHierarchy.indexOf(currentTier);
    const newIndex = this.tierHierarchy.indexOf(newTier);
    return newIndex > currentIndex;
  }

  /**
   * Check if a tier change is a downgrade
   */
  isDowngrade(currentTier, newTier) {
    const currentIndex = this.tierHierarchy.indexOf(currentTier);
    const newIndex = this.tierHierarchy.indexOf(newTier);
    return newIndex < currentIndex;
  }

  /**
   * Get tier comparison data
   */
  async getTierComparison() {
    try {
      const plans = await this.getSubscriptionPlans();

      return plans.map((plan) => ({
        tier: plan.tier,
        name: plan.name,
        price_monthly: plan.price_monthly / 100, // Convert from cents
        price_yearly: plan.price_yearly / 100,
        savings_yearly: (plan.price_monthly * 12 - plan.price_yearly) / 100,
        features: {
          documents_limit:
            plan.documents_limit === -1 ? "Unlimited" : plan.documents_limit,
          ai_generations_limit:
            plan.ai_generations_limit === -1
              ? "Unlimited"
              : plan.ai_generations_limit,
          ai_image_generations_limit:
            plan.ai_image_generations_limit === -1
              ? "Unlimited"
              : plan.ai_image_generations_limit,
          storage_limit_gb:
            Math.round((plan.storage_limit_mb / 1024) * 10) / 10,
          max_file_upload_mb: plan.max_file_upload_mb,
          custom_templates: plan.custom_templates,
          priority_processing: plan.priority_processing,
          remove_watermark: plan.remove_watermark,
          advanced_analytics: plan.advanced_analytics,
          api_access: plan.api_access,
          ai_model_selection: plan.ai_model_selection,
          phone_support: plan.phone_support,
        },
      }));
    } catch (error) {
      prodLogger.error("Error getting tier comparison:", error);
      throw error;
    }
  }

  /**
   * Redirect to Stripe Customer Portal
   */
  async redirectToCustomerPortal(userId) {

    try {
      await stripeService.redirectToCustomerPortal(userId);
    } catch (error) {
      prodLogger.error("Error redirecting to customer portal:", error);
      throw error;
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }
}

// Create singleton instance
const subscriptionService = new SubscriptionService();

export default subscriptionService;
