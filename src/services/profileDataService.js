/**
 * Profile Data Service for DocForge AI
 * Handles extraction and formatting of user profile data for template overlays
 *
 * Features:
 * - Profile field mapping and extraction
 * - Data formatting and transformation
 * - Fallback handling for missing data
 * - Profile data caching for performance
 */

import { prodLogger } from '../utils/prodLogger.js';

/**
 * Profile Data Service Class
 * Manages user profile data for template overlay population
 */
class ProfileDataService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiration = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Format date to "15 February 2024" format
   * @param {Date} date - Date to format (defaults to current date)
   * @returns {string} Formatted date string
   */
  static formatDate(date = new Date()) {
    const options = {
      day: 'numeric',
      month: 'long', 
      year: 'numeric'
    };
    return date.toLocaleDateString('en-GB', options);
  }

  /**
   * Get profile value by field name
   * @param {string} fieldName - Profile field name (user_name, company_name, etc.)
   * @param {Object} userProfile - User profile data
   * @returns {string} Profile field value or empty string
   */
  static getProfileValue(fieldName, userProfile) {
    const fieldMap = {
      'user_name': 'full_name',
      'company_name': 'organization', 
      'website': 'website',
      'phone_number': 'phone',
      'email_address': 'email'
    };

    const profileField = fieldMap[fieldName];
    if (!profileField || !userProfile) {
      return '';
    }

    return userProfile[profileField] || '';
  }

  /**
   * Format profile value with optional transformations
   * @param {string} value - Raw profile value
   * @param {Object} formatting - Formatting options
   * @returns {string} Formatted value
   */
  static formatValue(value, formatting = {}) {
    if (!value || typeof value !== 'string') {
      return '';
    }

    let formatted = value.trim();

    // Apply text transformation
    if (formatting?.transform) {
      switch (formatting.transform) {
        case 'uppercase':
          formatted = formatted.toUpperCase();
          break;
        case 'lowercase':
          formatted = formatted.toLowerCase();
          break;
        case 'capitalize':
          formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1).toLowerCase();
          break;
        case 'capitalize-first':
          formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);
          break;
      }
    }

    // Add prefix/suffix
    if (formatting?.prefix) {
      formatted = formatting.prefix + formatted;
    }
    if (formatting?.suffix) {
      formatted = formatted + formatting.suffix;
    }

    return formatted;
  }

  /**
   * Get all available profile placeholders
   * @returns {Array} Array of profile placeholder objects
   */
  static getAvailableProfilePlaceholders() {
    return [
      {
        placeholder: '{{user_name}}',
        label: 'Full Name',
        description: 'User\'s full name from profile',
        field: 'full_name',
        example: 'John Doe'
      },
      {
        placeholder: '{{company_name}}',
        label: 'Company/Organization',
        description: 'Organization or company name',
        field: 'organization',
        example: 'Acme Corporation'
      },
      {
        placeholder: '{{website}}',
        label: 'Website',
        description: 'User\'s website URL',
        field: 'website',
        example: 'www.example.com'
      },
      {
        placeholder: '{{phone_number}}',
        label: 'Phone Number',
        description: 'User\'s phone number',
        field: 'phone',
        example: '+****************'
      },
      {
        placeholder: '{{email_address}}',
        label: 'Email Address',
        description: 'User\'s email address',
        field: 'email',
        example: '<EMAIL>'
      }
    ];
  }

  /**
   * Get all available document placeholders
   * @returns {Array} Array of document placeholder objects
   */
  static getAvailableDocumentPlaceholders() {
    return [
      {
        placeholder: '{{title}}',
        label: 'Document Title',
        description: 'Title of the document',
        example: 'My Document'
      },
      {
        placeholder: '{{author}}',
        label: 'Document Author',
        description: 'Author of the document',
        example: 'John Doe'
      },
      {
        placeholder: '{{description}}',
        label: 'Document Description',
        description: 'Brief description of the document',
        example: 'A comprehensive guide...'
      },
      {
        placeholder: '{{date}}',
        label: 'Current Date',
        description: 'Current date (formatted)',
        example: new Date().toLocaleDateString()
      },
      {
        placeholder: '{{year}}',
        label: 'Current Year',
        description: 'Current year',
        example: new Date().getFullYear().toString()
      },
      {
        placeholder: '{{desc}}',
        label: 'Document Description',
        description: 'Detailed description or purpose of the document',
        example: 'A comprehensive guide to modern business practices'
      },
      {
        placeholder: '{{for}}',
        label: 'Target Audience',
        description: 'Intended audience or recipient of the document',
        example: 'Business Professionals'
      }
    ];
  }

  /**
   * Validate profile data format
   * @param {Object} profile - User profile data
   * @returns {Object} Validation result with errors
   */
  static validateProfileData(profile) {
    if (!profile || typeof profile !== 'object') {
      return { isValid: false, errors: ['Invalid profile data'] };
    }

    const validations = {
      website: (url) => this.isValidURL(url),
      phone: (phone) => this.isValidPhoneNumber(phone),
      email: (email) => this.isValidEmail(email)
    };

    const errors = [];
    Object.entries(validations).forEach(([field, validator]) => {
      if (profile[field] && !validator(profile[field])) {
        errors.push(`Invalid ${field} format: ${profile[field]}`);
      }
    });

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Simple URL validation
   * @param {string} url - URL to validate
   * @returns {boolean} True if valid URL
   */
  static isValidURL(url) {
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Simple phone number validation
   * @param {string} phone - Phone number to validate
   * @returns {boolean} True if valid phone format
   */
  static isValidPhoneNumber(phone) {
    // Basic phone validation - allows various formats
    const phoneRegex = /^[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  /**
   * Simple email validation
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid email format
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Cache user profile data
   * @param {string} userId - User ID
   * @param {Object} profileData - Profile data to cache
   */
  cacheProfile(userId, profileData) {
    try {
      this.cache.set(userId, {
        data: profileData,
        timestamp: Date.now()
      });

      // Auto-expire after cache expiration time
      setTimeout(() => {
        this.cache.delete(userId);
      }, this.cacheExpiration);

      prodLogger.debug(`✅ Profile cached for user: ${userId}`);
    } catch (error) {
      prodLogger.error('Failed to cache profile:', error);
    }
  }

  /**
   * Get cached profile data
   * @param {string} userId - User ID
   * @returns {Object|null} Cached profile data or null
   */
  getCachedProfile(userId) {
    try {
      const cached = this.cache.get(userId);
      if (!cached) return null;

      // Check if cache is expired
      if (Date.now() - cached.timestamp > this.cacheExpiration) {
        this.cache.delete(userId);
        return null;
      }

      return cached.data;
    } catch (error) {
      prodLogger.error('Failed to get cached profile:', error);
      return null;
    }
  }

  /**
   * Clear profile cache
   * @param {string} userId - Optional user ID to clear specific cache
   */
  clearCache(userId = null) {
    if (userId) {
      this.cache.delete(userId);
    } else {
      this.cache.clear();
    }
  }
}

// Create singleton instance
const profileDataService = new ProfileDataService();

export default ProfileDataService;
export { profileDataService };