/**
 * Logo Overlay Service
 * Handles logo overlay functionality for templates, mirroring text overlay patterns
 * Only operates on templates that explicitly define logo overlays
 */

import { supabase } from "../lib/supabase.js";
import { getUserLogos } from "./logoService.js";
import { prodLogger } from "../utils/prodLogger.js";

/**
 * Check if template has logo overlays
 * @param {Object} template - Template object
 * @returns {boolean} True if template has logo overlays
 */
export const templateHasLogoOverlays = (template) => {
  // Check unified structure (text_overlays.overlays with type='logo')
  const unifiedOverlays = template?.text_overlays?.overlays;
  if (unifiedOverlays && Array.isArray(unifiedOverlays)) {
    const logoOverlays = unifiedOverlays.filter(
      (overlay) => overlay.type === "logo"
    );
    return logoOverlays.length > 0;
  }

  return false;
};

/**
 * Get logo overlays from template
 * @param {Object} template - Template object
 * @returns {Array} Array of logo overlays or empty array
 */
export const getLogoOverlays = (template) => {
  if (!templateHasLogoOverlays(template)) return [];

  // Get logo overlays from unified structure (text_overlays.overlays with type='logo')
  const unifiedOverlays = template?.text_overlays?.overlays;
  if (unifiedOverlays && Array.isArray(unifiedOverlays)) {
    return unifiedOverlays.filter((overlay) => overlay.type === "logo");
  }

  return [];
};

/**
 * Populate logo placeholder with actual logo data
 * @param {string} placeholder - Logo placeholder (e.g., "{{logo}}")
 * @param {Object} documentData - Document data containing logo information
 * @returns {Object|null} Logo data or null if not found
 */
export const populateLogoPlaceholder = (placeholder, documentData) => {
  try {
    // Handle {{logo}} placeholder
    if (placeholder === "{{logo}}" && documentData?.logo) {
      return documentData.logo;
    }

    // Handle specific logo placeholders like {{company_logo}}
    const logoKey = placeholder.replace(/[{}]/g, "");
    if (documentData?.logos?.[logoKey]) {
      return documentData.logos[logoKey];
    }

    // Handle direct logo data
    if (documentData?.logo) {
      return documentData.logo;
    }

    return null;
  } catch (error) {
    prodLogger.error("❌ Error populating logo placeholder:", error);
    return null;
  }
};

/**
 * Render logo overlay on canvas
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {Object} logoData - Logo data with image buffer
 * @param {Object} position - Position configuration
 * @param {Object} styling - Styling configuration
 * @returns {Promise<void>}
 */
export const renderLogoOverlay = async (
  ctx,
  logoData,
  position,
  styling = {}
) => {
  try {
    if (!logoData?.data) {
      prodLogger.warn("⚠️ No logo data provided for rendering");
      return;
    }

    const { x, y, width, height } = position;
    const { opacity = 1.0, borderRadius = 0 } = styling;

    // Create image from logo data
    const blob = new Blob([logoData.data], {
      type: logoData.fileType || "image/png",
    });
    const imageUrl = URL.createObjectURL(blob);

    const img = new Image();
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageUrl;
    });

    // Save current context state
    ctx.save();

    // Apply opacity
    if (opacity < 1.0) {
      ctx.globalAlpha = opacity;
    }

    // Apply border radius if specified
    if (borderRadius > 0) {
      ctx.beginPath();
      ctx.roundRect(x, y, width, height, borderRadius);
      ctx.clip();
    }

    // Draw the logo
    ctx.drawImage(img, x, y, width, height);

    // Restore context state
    ctx.restore();

    // Clean up object URL
    URL.revokeObjectURL(imageUrl);
  } catch (error) {
    prodLogger.error("❌ Error rendering logo overlay:", error);
  }
};

/**
 * Apply logo customizations to overlay (mirrors text overlay pattern)
 * @param {Object} overlay - Original logo overlay
 * @param {Object} customizations - User customizations
 * @returns {Object} Merged overlay with customizations
 */
export const applyLogoCustomizations = (overlay, customizations) => {
  if (!customizations || Object.keys(customizations).length === 0) {
    return overlay;
  }

  return {
    ...overlay,
    position: {
      ...overlay.position,
      ...customizations.position,
    },
    styling: {
      ...overlay.styling,
      ...customizations.styling,
    },
  };
};

/**
 * Apply size constraints to logo dimensions
 * @param {Object} sizeChanges - Proposed size changes {width, height}
 * @param {Object} constraints - Template-defined constraints
 * @returns {Object} Constrained size {width, height}
 */
export const applyLogoSizeConstraints = (sizeChanges, constraints = {}) => {
  const {
    min_width = 20,
    max_width = 500,
    min_height = 20,
    max_height = 500,
    maintain_aspect_ratio = false,
  } = constraints;

  // Apply basic min/max constraints
  let constrainedWidth = sizeChanges.width || 100;
  let constrainedHeight = sizeChanges.height || 60;

  // Clamp to min/max values
  constrainedWidth = Math.max(min_width, Math.min(max_width, constrainedWidth));
  constrainedHeight = Math.max(
    min_height,
    Math.min(max_height, constrainedHeight)
  );

  return {
    width: constrainedWidth,
    height: constrainedHeight,
  };
};

/**
 * Validate logo size against template constraints
 * @param {Object} size - Logo size {width, height}
 * @param {Object} constraints - Template-defined constraints
 * @returns {Object} Validation result {isValid, errors}
 */
export const validateLogoSize = (size, constraints = {}) => {
  const {
    min_width = 20,
    max_width = 500,
    min_height = 20,
    max_height = 500,
  } = constraints;

  const errors = [];

  if (size.width < min_width) {
    errors.push(`Width must be at least ${min_width}px`);
  }
  if (size.width > max_width) {
    errors.push(`Width cannot exceed ${max_width}px`);
  }
  if (size.height < min_height) {
    errors.push(`Height must be at least ${min_height}px`);
  }
  if (size.height > max_height) {
    errors.push(`Height cannot exceed ${max_height}px`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Get logo data for template rendering
 * @param {string} userId - User ID
 * @param {Object} template - Template object
 * @param {Object} logoCustomizations - Logo customizations per overlay
 * @returns {Promise<Object>} Logo data for rendering
 */
export const getLogoDataForTemplate = async (
  userId,
  template,
  logoCustomizations = {}
) => {
  try {
    prodLogger.debug("🔍 getLogoDataForTemplate called", {
      userId,
      templateId: template?.id,
      hasCustomizations: Object.keys(logoCustomizations).length > 0,
      customizations: logoCustomizations,
      templateStructure: {
        hasTextOverlays: !!template?.text_overlays?.overlays,
        textOverlaysCount: template?.text_overlays?.overlays?.length || 0,
        unifiedLogoCount:
          template?.text_overlays?.overlays?.filter((o) => o.type === "logo")
            .length || 0,
      },
    });

    // Handle missing userId gracefully
    if (!userId) {
      prodLogger.debug(
        "ℹ️ getLogoDataForTemplate: No userId provided, skipping logo data retrieval (likely thumbnail generation)"
      );
      return {};
    }

    if (!templateHasLogoOverlays(template)) {
      prodLogger.debug("ℹ️ Template has no logo overlays");
      return {};
    }

    const logoOverlays = getLogoOverlays(template);
    prodLogger.debug("📋 Found logo overlays", {
      count: logoOverlays.length,
      overlayIds: logoOverlays.map((o) => o.id),
      overlayTypes: logoOverlays.map((o) => o.type),
    });

    const logoData = {};

    for (const overlay of logoOverlays) {
      prodLogger.debug(`🔍 Processing overlay "${overlay.id}"`);
      const customization = logoCustomizations[overlay.id];

      // Check if user has selected a specific logo for this overlay
      if (customization?.selectedLogoId) {
        prodLogger.debug(
          `  Using selected logo: ${customization.selectedLogoId}`
        );
        const { data: logoRecord, error } = await supabase
          .from("user_logos")
          .select("*")
          .eq("id", customization.selectedLogoId)
          .eq("user_id", userId)
          .eq("is_active", true)
          .single();

        if (error) {
          prodLogger.error(`  ❌ Error fetching selected logo:`, error);
        } else if (!logoRecord) {
          prodLogger.warn(
            `  ⚠️ Selected logo not found: ${customization.selectedLogoId}`
          );
        } else {
          prodLogger.debug(`  ✅ Logo record found: ${logoRecord.name}`);
          // Download logo data from storage
          const { data: logoBuffer, error: downloadError } =
            await supabase.storage
              .from("user-logos")
              .download(logoRecord.storage_path);

          if (downloadError) {
            prodLogger.error(`  ❌ Error downloading logo:`, downloadError);
          } else {
            const arrayBuffer = await logoBuffer.arrayBuffer();
            logoData[overlay.id] = {
              id: logoRecord.id,
              name: logoRecord.name,
              data: arrayBuffer,
              fileType: logoRecord.file_type,
              originalWidth: logoRecord.width,
              originalHeight: logoRecord.height,
            };
          }
        }
      } else {
        // Try to use user's default logo
        const { data: userProfile, error: profileError } = await supabase
          .from("user_profiles")
          .select("default_logo_id")
          .eq("id", userId)
          .single();

        if (profileError) {
          prodLogger.error(`  ❌ Error fetching user profile:`, profileError);
        } else if (!userProfile?.default_logo_id) {
          prodLogger.warn(`  ⚠️ No default logo set for user ${userId}`);
        } else {
          prodLogger.debug(`  Default logo ID: ${userProfile.default_logo_id}`);
          const { data: logoRecord, error } = await supabase
            .from("user_logos")
            .select("*")
            .eq("id", userProfile.default_logo_id)
            .eq("user_id", userId)
            .eq("is_active", true)
            .single();

          if (error) {
            prodLogger.error(`  ❌ Error fetching default logo:`, error);
          } else if (!logoRecord) {
            prodLogger.warn(
              `  ⚠️ Default logo not found: ${userProfile.default_logo_id}`
            );
          } else {
            const { data: logoBuffer, error: downloadError } =
              await supabase.storage
                .from("user-logos")
                .download(logoRecord.storage_path);

            if (downloadError) {
              prodLogger.error(
                `  ❌ Error downloading default logo:`,
                downloadError
              );
            } else {
              const arrayBuffer = await logoBuffer.arrayBuffer();
              logoData[overlay.id] = {
                id: logoRecord.id,
                name: logoRecord.name,
                data: arrayBuffer,
                fileType: logoRecord.file_type,
                originalWidth: logoRecord.width,
                originalHeight: logoRecord.height,
              };
              prodLogger.debug(
                `  ✅ Default logo data prepared for overlay "${overlay.id}"`
              );
            }
          }
        }
      }
    }

    prodLogger.debug("🎯 getLogoDataForTemplate result", {
      templateId: template.id,
      overlayCount: logoOverlays.length,
      logoDataKeys: Object.keys(logoData),
      success: Object.keys(logoData).length > 0,
    });

    return logoData;
  } catch (error) {
    prodLogger.error("❌ Error getting logo data for template:", error);
    return {};
  }
};

/**
 * Check if logo overlay exists at canvas position (mirrors text overlay pattern)
 * @param {Array} logoOverlays - Array of logo overlays
 * @param {Object} logoCustomizations - Logo customizations
 * @param {number} x - X coordinate
 * @param {number} y - Y coordinate
 * @returns {string|null} Logo overlay ID or null
 */
export const getLogoOverlayAtPosition = (
  logoOverlays,
  logoCustomizations,
  x,
  y
) => {
  if (!logoOverlays || logoOverlays.length === 0) return null;

  // Check overlays from top to bottom (reverse order for proper layering)
  for (let i = logoOverlays.length - 1; i >= 0; i--) {
    const overlay = logoOverlays[i];
    const customization = logoCustomizations[overlay.id] || {};
    const position = customization.position || overlay.position;

    if (!position) continue;

    const overlayX = position.x;
    const overlayY = position.y;
    const overlayWidth = position.width;
    const overlayHeight = position.height;

    if (
      x >= overlayX &&
      x <= overlayX + overlayWidth &&
      y >= overlayY &&
      y <= overlayY + overlayHeight
    ) {
      return overlay.id;
    }
  }

  return null;
};

/**
 * Get default logo overlay configurations for template creation
 * @param {string} category - Template category
 * @returns {Object} Default logo overlay configuration
 */
export const getDefaultLogoOverlayForCategory = (category) => {
  const defaults = {
    Business: {
      id: "company_logo",
      type: "logo",
      placeholder: "{{logo}}",
      position: { x: 450, y: 50, width: 100, height: 60 },
      styling: { opacity: 1.0, borderRadius: 0 },
    },
    Academic: {
      id: "institution_logo",
      type: "logo",
      placeholder: "{{logo}}",
      position: { x: 250, y: 30, width: 80, height: 50 },
      styling: { opacity: 0.8, borderRadius: 0 },
    },
    Creative: {
      id: "brand_logo",
      type: "logo",
      placeholder: "{{logo}}",
      position: { x: 50, y: 50, width: 120, height: 80 },
      styling: { opacity: 1.0, borderRadius: 8 },
    },
  };

  return defaults[category] || defaults.Business;
};
