/**
 * Image Overlay Template Service for DocForge AI
 * Renders text overlays on background images for cover template generation
 *
 * Features:
 * - Canvas-based text rendering with precise positioning
 * - Dynamic text styling (font, size, color, alignment)
 * - Text overflow handling (ellipsis, word wrapping)
 * - High-quality image export for PDF generation
 * - Real-time preview generation
 * - Dynamic font loading for custom fonts
 */

import errorMonitor, { ErrorSeverity } from "../utils/errorMonitor.js";
import fontLoader from "../utils/fontLoader.js";
import {
  templateHasLogoOverlays,
  getLogoOverlays,
  renderLogoOverlay,
  applyLogoCustomizations,
  getLogoDataForTemplate,
} from "./logoOverlayService.js";
import ProfileDataService from "./profileDataService.js";

import { prodLogger } from "../utils/prodLogger.js";
import { detectTextOnlyChange } from "../utils/changeDetection.js";
/**
 * Image Overlay Service Class
 * Handles all image overlay template rendering operations
 */
class ImageOverlayService {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.imageCache = new Map();
    // Track rendered overlays to prevent duplication
    this.renderedOverlays = new Set();
    // Cache for background cleaning results
    this.backgroundCleaningCache = new Map();
    // Track last customizations for change detection
    this.lastCustomizations = {};
  }

  /**
   * Initialize canvas context
   * @param {number} width - Canvas width
   * @param {number} height - Canvas height
   * @returns {Object} Canvas and context
   */
  initializeCanvas(width, height) {
    try {
      // Create canvas element
      this.canvas = document.createElement("canvas");
      this.canvas.width = width;
      this.canvas.height = height;

      // Get 2D context with high quality settings
      this.ctx = this.canvas.getContext("2d", {
        alpha: true,
        desynchronized: false,
      });

      if (!this.ctx) {
        throw new Error("Failed to get 2D context from canvas");
      }

      // Enable high-quality text rendering
      this.ctx.textBaseline = "top";
      this.ctx.imageSmoothingEnabled = true;

      // Set image smoothing quality if supported
      if ("imageSmoothingQuality" in this.ctx) {
        this.ctx.imageSmoothingQuality = "high";
      }
      return { canvas: this.canvas, ctx: this.ctx };
    } catch (error) {
      prodLogger.error("Failed to initialize canvas:", error);
      throw new Error(`Canvas initialization failed: ${error.message}`);
    }
  }

  /**
   * Load and cache background image
   * @param {string} imageUrl - URL of the background image
   * @returns {Promise<HTMLImageElement>} Loaded image element
   */
  async loadBackgroundImage(imageUrl) {
    try {
      // Check cache first
      if (this.imageCache.has(imageUrl)) {
        return this.imageCache.get(imageUrl);
      }

      return new Promise((resolve, reject) => {
        const img = new Image();

        // Only set crossOrigin for external URLs, not for placeholder URLs
        if (imageUrl.includes("supabase") || imageUrl.includes("storage")) {
          img.crossOrigin = "anonymous";
        }

        img.onload = () => {
          // Cache the loaded image
          this.imageCache.set(imageUrl, img);
          resolve(img);
        };

        img.onerror = (error) => {
          prodLogger.error(`❌ Failed to load image: ${imageUrl}`, error);

          // Create a fallback colored rectangle
          const fallbackCanvas = document.createElement("canvas");
          fallbackCanvas.width = 600;
          fallbackCanvas.height = 800;
          const fallbackCtx = fallbackCanvas.getContext("2d");

          // Create a gradient background as fallback
          const gradient = fallbackCtx.createLinearGradient(0, 0, 0, 800);
          gradient.addColorStop(0, "#3498db");
          gradient.addColorStop(1, "#2c3e50");
          fallbackCtx.fillStyle = gradient;
          fallbackCtx.fillRect(0, 0, 600, 800);

          // Convert canvas to image
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            this.imageCache.set(imageUrl, fallbackImg);
            prodLogger.debug(`✅ Using fallback image for: ${imageUrl}`);
            resolve(fallbackImg);
          };
          fallbackImg.src = fallbackCanvas.toDataURL();
        };

        img.src = imageUrl;
      });
    } catch (error) {
      prodLogger.error("Error loading background image:", error);
      throw error;
    }
  }

  /**
   * Apply text styling to canvas context with font loading
   * @param {Object} styling - Text styling configuration
   * @returns {Promise<void>} Promise that resolves when styling is applied
   */
  async applyTextStyling(styling) {
    const {
      fontSize = 16,
      fontFamily = "Arial",
      fontWeight = "normal",
      fontStyle = "normal",
      color = "#000000",
      textAlign = "left",
      letterSpacing = 0,
    } = styling;

    // Ensure font is loaded before applying
    const loadedFontFamily = await fontLoader.ensureFontLoaded(fontFamily, {
      fallback: "Arial",
      loadGoogleFonts: true,
    });

    // Get font with proper fallbacks
    const fontWithFallbacks = fontLoader.getFontWithFallbacks(loadedFontFamily);

    // Set font with style and weight
    this.ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontWithFallbacks}`;

    // Set color
    this.ctx.fillStyle = color;

    // Set text alignment
    this.ctx.textAlign = textAlign;

    // Set letter spacing (if supported)
    if ("letterSpacing" in this.ctx) {
      this.ctx.letterSpacing = `${letterSpacing}px`;
    }
  }

  /**
   * Measure text dimensions with word wrapping
   * @param {string} text - Text to measure
   * @param {number} maxWidth - Maximum width for wrapping
   * @param {Object} styling - Text styling
   * @returns {Object} Text metrics and wrapped lines
   */
  measureText(text, maxWidth, styling) {
    this.applyTextStyling(styling);

    const words = text.split(" ");
    const lines = [];
    let currentLine = "";

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const metrics = this.ctx.measureText(testLine);

      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    const lineHeight = styling.lineHeight || 1.2;
    const totalHeight = lines.length * styling.fontSize * lineHeight;

    return {
      lines,
      totalHeight,
      lineHeight: styling.fontSize * lineHeight,
      maxWidth: Math.max(
        ...lines.map((line) => this.ctx.measureText(line).width)
      ),
    };
  }

  /**
   * Render text overlay with positioning and styling
   * @param {string} text - Text content to render
   * @param {Object} position - Position configuration
   * @param {Object} styling - Text styling configuration
   * @returns {Promise<void>} Promise that resolves when rendering is complete
   */
  async renderTextOverlay(text, position, styling) {
    try {
      const { x, y, width, height } = position;
      const { textTransform = "none" } = styling;

      // Apply text transformation
      const transformedText = this.applyTextTransform(text, textTransform);

      // Apply styling (now async for font loading)
      await this.applyTextStyling(styling);

      // Measure text and handle wrapping (using transformed text)
      const textMetrics = this.measureText(transformedText, width, styling);
      let { lines } = textMetrics;

      // No max lines constraint - allow unlimited text lines

      // Calculate vertical positioning
      const lineHeight = textMetrics.lineHeight;
      const totalTextHeight = lines.length * lineHeight;
      let startY = y;

      // Vertical alignment within the bounds
      if (styling.verticalAlign === "center") {
        startY = y + (height - totalTextHeight) / 2;
      } else if (styling.verticalAlign === "bottom") {
        startY = y + height - totalTextHeight;
      }

      // Render each line
      lines.forEach((line, index) => {
        const lineY = startY + index * lineHeight;

        // Calculate horizontal position based on alignment
        let lineX = x;
        if (styling.textAlign === "center") {
          lineX = x + width / 2;
        } else if (styling.textAlign === "right") {
          lineX = x + width;
        }

        // Render the text
        this.ctx.fillText(line, lineX, lineY);

        // Handle text decorations
        if (styling.textDecoration && styling.textDecoration !== "none") {
          const textWidth = this.ctx.measureText(line).width;
          let decorationX = lineX;

          // Adjust decoration position based on text alignment
          if (styling.textAlign === "center") {
            decorationX = lineX - textWidth / 2;
          } else if (styling.textAlign === "right") {
            decorationX = lineX - textWidth;
          }

          // Draw underline
          if (styling.textDecoration.includes("underline")) {
            const underlineY = lineY + styling.fontSize * 0.1;
            this.ctx.beginPath();
            this.ctx.moveTo(decorationX, underlineY);
            this.ctx.lineTo(decorationX + textWidth, underlineY);
            this.ctx.strokeStyle = styling.color;
            this.ctx.lineWidth = Math.max(1, styling.fontSize * 0.05);
            this.ctx.stroke();
          }

          // Draw strikethrough
          if (styling.textDecoration.includes("line-through")) {
            const strikeY = lineY - styling.fontSize * 0.3;
            this.ctx.beginPath();
            this.ctx.moveTo(decorationX, strikeY);
            this.ctx.lineTo(decorationX + textWidth, strikeY);
            this.ctx.strokeStyle = styling.color;
            this.ctx.lineWidth = Math.max(1, styling.fontSize * 0.05);
            this.ctx.stroke();
          }
        }
      });
    } catch (error) {
      prodLogger.error("Error rendering text overlay:", error);
      throw error;
    }
  }

  /**
   * Clear canvas completely and force re-initialization
   * This prevents text-over-text issues from previous renders
   * @param {number} width - Canvas width
   * @param {number} height - Canvas height
   */
  forceCanvasReset(width, height) {
    try {
      // Initialize canvas
      this.initializeCanvas(width, height);
      
      // Clear with white background to ensure clean slate
      this.ctx.clearRect(0, 0, width, height);
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.fillRect(0, 0, width, height);
      
      // Reset all canvas properties to defaults
      this.ctx.globalAlpha = 1.0;
      this.ctx.globalCompositeOperation = 'source-over';
      this.ctx.textBaseline = 'top';
      this.ctx.textAlign = 'left';
      this.ctx.font = '16px Arial';
      this.ctx.fillStyle = '#000000';
      this.ctx.strokeStyle = '#000000';
      this.ctx.lineWidth = 1;
      this.ctx.setLineDash([]);
      
      prodLogger.debug(
        "🔄 Canvas force reset completed",
        { width, height }
      );
    } catch (error) {
      prodLogger.error("Error during canvas force reset:", error);
      throw error;
    }
  }

  /**
   * Conditional background cleaning - only clean when necessary
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document data
   * @param {Object} customizations - User customizations
   * @param {HTMLCanvasElement} backgroundImage - Loaded background image
   */
  async conditionalBackgroundCleaning(template, documentData, customizations, backgroundImage) {
    try {
      const cacheKey = `${template.id}-${template.background_image_url}`;
      const isTextOnlyChange = detectTextOnlyChange(this.lastCustomizations, customizations);

      if (isTextOnlyChange && this.backgroundCleaningCache.has(cacheKey)) {
        // Use cached cleaned background for text-only changes
        prodLogger.debug('🚀 Using cached background cleaning result');
        const cachedCanvas = this.backgroundCleaningCache.get(cacheKey);
        this.ctx.drawImage(cachedCanvas, 0, 0);
      } else {
        // Perform full background cleaning and cache result
        prodLogger.debug('🔧 Performing background cleaning and caching result');
        await this.enhancedTextConflictResolution(template, documentData, customizations, backgroundImage);

        // Cache the cleaned background
        const cleanedCanvas = document.createElement('canvas');
        cleanedCanvas.width = this.canvas.width;
        cleanedCanvas.height = this.canvas.height;
        const cleanedCtx = cleanedCanvas.getContext('2d');
        cleanedCtx.drawImage(this.canvas, 0, 0);
        this.backgroundCleaningCache.set(cacheKey, cleanedCanvas);
      }

      // Update last customizations for next comparison
      this.lastCustomizations = { ...customizations };
    } catch (error) {
      prodLogger.error('Error in conditional background cleaning:', error);
      // Fallback to regular background cleaning
      await this.enhancedTextConflictResolution(template, documentData, customizations, backgroundImage);
    }
  }

  /**
   * Enhanced background text removal that targets specific text content
   * This method removes both background embedded text AND prevents document data fallback
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document data
   * @param {Object} customizations - User customizations
   * @param {HTMLCanvasElement} backgroundImage - Loaded background image
   */
  async enhancedTextConflictResolution(template, documentData, customizations, backgroundImage) {
    try {
      if (!template?.text_overlays?.overlays) {
        return;
      }

      prodLogger.debug(
        "🔧 Enhanced text conflict resolution",
        {
          templateId: template.id,
          overlayCount: template.text_overlays.overlays.length,
          hasCustomizations: Object.keys(customizations).length > 0,
        }
      );

      // Process each text overlay to resolve conflicts
      template.text_overlays.overlays.forEach((overlay) => {
        if (overlay.type === "logo") {
          return; // Skip logo overlays
        }

        const overlayCustomizations = customizations[overlay.id] || {};

        // Use customized position if available, otherwise use original position
        const position = overlayCustomizations.position || overlay.position;
        const { x, y, width, height } = position;

        // Determine what text would be rendered from document data
        const documentBasedText = this.populateWithDocumentDataOnly(
          overlay.placeholder,
          documentData
        );

        // Determine what text will actually be rendered (custom or document)
        const actualText = overlayCustomizations.content !== undefined
          ? overlayCustomizations.content
          : documentBasedText;

        // Skip cleaning if overlay is hidden
        if (overlayCustomizations.hidden) {
          prodLogger.debug(`⏭️ Skipping conflict resolution for hidden overlay: ${overlay.id}`);
          return;
        }

        prodLogger.debug(
          `🔧 Cleaning overlay area: ${overlay.id}`,
          {
            overlayId: overlay.id,
            hasCustomContent: overlayCustomizations.content !== undefined,
            position: { x, y, width, height },
          }
        );

        // ALWAYS mask the overlay area to remove any background text
        // Use a padding that's larger to ensure complete coverage
        const maskPadding = 10;
        const maskX = Math.max(0, x - maskPadding);
        const maskY = Math.max(0, y - maskPadding);
        const maskWidth = Math.min(
          this.canvas.width - maskX,
          width + (maskPadding * 2)
        );
        const maskHeight = Math.min(
          this.canvas.height - maskY,
          height + (maskPadding * 2)
        );

        // Sample background color for intelligent masking
        const backgroundColor = this.getDominantBackgroundColor([
          { x: Math.max(0, x - 30), y: Math.max(0, y - 30) },
          { x: Math.min(this.canvas.width - 1, x + width + 30), y: Math.max(0, y - 30) },
          { x: Math.max(0, x - 30), y: Math.min(this.canvas.height - 1, y + height + 30) },
          { x: Math.min(this.canvas.width - 1, x + width + 30), y: Math.min(this.canvas.height - 1, y + height + 30) },
        ]);

        // Create intelligent background fill
        const gradient = this.ctx.createLinearGradient(maskX, maskY, maskX + maskWidth, maskY + maskHeight);
        gradient.addColorStop(0, backgroundColor);
        gradient.addColorStop(0.5, this.adjustBrightness(backgroundColor, 0.98));
        gradient.addColorStop(1, this.adjustBrightness(backgroundColor, 0.96));
        
        // Apply the mask
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(maskX, maskY, maskWidth, maskHeight);

        prodLogger.debug(
          `✅ Masked overlay area: ${overlay.id}`,
          { 
            maskArea: { x: maskX, y: maskY, width: maskWidth, height: maskHeight },
            backgroundColor,
            removedPotentialText: documentBasedText,
          }
        );
      });

      prodLogger.debug(
        "✅ Enhanced text conflict resolution completed",
        { templateId: template.id }
      );
    } catch (error) {
      prodLogger.error("Error during enhanced text conflict resolution:", error);
      // Fallback to simple masking
      await this.maskBackgroundTextAreas(template, backgroundImage);
    }
  }

  /**
   * Populate placeholder with document data only (no customizations)
   * Used to determine what text would be rendered from document data
   * @param {string} placeholder - Placeholder text with {{variables}}
   * @param {Object} documentData - Document metadata
   * @returns {string} Populated text from document data only
   */
  populateWithDocumentDataOnly(placeholder, documentData) {
    if (!placeholder || typeof placeholder !== "string") {
      return "";
    }

    let populatedText = placeholder;

    // Replace common placeholders
    const now = new Date();
    const replacements = {
      "{{title}}": documentData.title || "Untitled Document",
      "{{author}}": documentData.author || "Unknown Author",
      "{{description}}": documentData.description || "",
      "{{date}}": now.toLocaleDateString(),
      "{{year}}": now.getFullYear().toString(),
      "{{month}}": now.toLocaleDateString("en-US", { month: "long" }),
      "{{day}}": now.getDate().toString(),
      // New overlay type placeholders
      "{{desc}}": this.resolveDescPlaceholder(documentData),
      "{{for}}": this.resolveForPlaceholder(documentData),
    };

    Object.entries(replacements).forEach(([placeholder, value]) => {
      populatedText = populatedText.replace(placeholder, value);
    });

    return populatedText;
  }

  /**
   * Resolve {{desc}} placeholder with appropriate document data
   * @param {Object} documentData - Document metadata
   * @returns {string} Resolved description text
   */
  resolveDescPlaceholder(documentData) {
    // Priority order for description resolution:
    // 1. Explicit description field
    // 2. Document purpose target outcome
    // 3. Generated description from questionnaire data (topic, audience, type)
    // 4. Default fallback

    if (documentData.description && documentData.description.trim()) {
      return documentData.description.trim();
    }

    if (documentData.documentPurpose?.targetOutcome && documentData.documentPurpose.targetOutcome.trim()) {
      return documentData.documentPurpose.targetOutcome.trim();
    }

    // Generate description from questionnaire data (same logic as DocumentPublish.jsx)
    const topic = documentData?.topicAndNiche?.mainTopic;
    const audience = documentData?.audienceAnalysis?.primaryAudience;
    const type = documentData?.documentPurpose?.primaryType;

    if (topic && audience && type) {
      return `A comprehensive ${type} about ${topic} for ${audience}.`;
    }
    if (topic && type) {
      return `A ${type} covering ${topic}.`;
    }
    if (topic) {
      return `A guide covering ${topic}.`;
    }

    return "Document Description";
  }

  /**
   * Resolve {{for}} placeholder with appropriate audience data
   * @param {Object} documentData - Document metadata
   * @returns {string} Resolved audience/target text
   */
  resolveForPlaceholder(documentData) {
    // Priority order for "for" resolution:
    // 1. Primary audience from audience analysis
    // 2. Document purpose use case
    // 3. Document type-based default
    // 4. Generic fallback

    if (documentData.audienceAnalysis?.primaryAudience && documentData.audienceAnalysis.primaryAudience.trim()) {
      return documentData.audienceAnalysis.primaryAudience.trim();
    }

    if (documentData.documentPurpose?.useCase && documentData.documentPurpose.useCase.trim()) {
      return documentData.documentPurpose.useCase.trim();
    }

    // Provide type-based defaults
    const docType = documentData.documentPurpose?.primaryType;
    const typeDefaults = {
      'academic': 'Students and Researchers',
      'business': 'Business Professionals',
      'ebook': 'General Readers',
      'guide': 'Practitioners and Learners'
    };

    if (docType && typeDefaults[docType]) {
      return typeDefaults[docType];
    }

    return "Target Audience";
  }

  /**
   * Advanced background text replacement system
   * Analyzes overlay areas and intelligently replaces background text with clean background
   * @param {Object} template - Template configuration
   * @param {HTMLCanvasElement} backgroundImage - Loaded background image
   */
  async intelligentBackgroundCleaning(template, backgroundImage) {
    try {
      if (!template?.text_overlays?.overlays) {
        return;
      }

      prodLogger.debug(
        "🧼 Performing intelligent background cleaning",
        {
          templateId: template.id,
          overlayCount: template.text_overlays.overlays.length,
        }
      );

      // For each text overlay, create a smart background replacement
      template.text_overlays.overlays.forEach((overlay) => {
        if (overlay.type === "logo") {
          return; // Skip logo overlays
        }

        const { x, y, width, height } = overlay.position;
        
        // Sample background colors around the text area to create intelligent fill
        const samplePoints = [
          { x: Math.max(0, x - 20), y: Math.max(0, y - 20) }, // Top-left
          { x: Math.min(this.canvas.width - 1, x + width + 20), y: Math.max(0, y - 20) }, // Top-right
          { x: Math.max(0, x - 20), y: Math.min(this.canvas.height - 1, y + height + 20) }, // Bottom-left
          { x: Math.min(this.canvas.width - 1, x + width + 20), y: Math.min(this.canvas.height - 1, y + height + 20) }, // Bottom-right
        ];

        // Get the dominant background color from sample points
        const backgroundColor = this.getDominantBackgroundColor(samplePoints);
        
        // Create a gradient or solid fill that matches the surrounding background
        const gradient = this.ctx.createLinearGradient(x, y, x + width, y + height);
        gradient.addColorStop(0, backgroundColor);
        gradient.addColorStop(1, this.adjustBrightness(backgroundColor, 0.95));
        
        // Apply the intelligent background replacement
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(x, y, width, height);

        prodLogger.debug(
          `🧼 Intelligently cleaned overlay area: ${overlay.id}`,
          { x, y, width, height, backgroundColor }
        );
      });

      prodLogger.debug(
        "✅ Intelligent background cleaning completed",
        { templateId: template.id }
      );
    } catch (error) {
      prodLogger.error("Error during intelligent background cleaning:", error);
      // Fallback to simple masking
      await this.maskBackgroundTextAreas(template, backgroundImage);
    }
  }

  /**
   * Get dominant background color from sample points
   * @param {Array} samplePoints - Array of {x, y} coordinates to sample
   * @returns {string} Dominant color in hex format
   */
  getDominantBackgroundColor(samplePoints) {
    try {
      const colors = [];
      
      samplePoints.forEach(point => {
        const imageData = this.ctx.getImageData(point.x, point.y, 1, 1);
        const [r, g, b] = imageData.data;
        colors.push(`rgb(${r}, ${g}, ${b})`);
      });
      
      // For simplicity, return the first sampled color
      // In a more advanced implementation, this could analyze frequency
      return colors[0] || '#FFFFFF';
    } catch (error) {
      prodLogger.warn("Could not sample background color, using white:", error);
      return '#FFFFFF';
    }
  }

  /**
   * Adjust brightness of a color
   * @param {string} color - Color in rgb() format
   * @param {number} factor - Brightness factor (0.0 to 2.0)
   * @returns {string} Adjusted color
   */
  adjustBrightness(color, factor) {
    try {
      const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
      if (!rgbMatch) return color;
      
      const [, r, g, b] = rgbMatch.map(Number);
      const adjustedR = Math.min(255, Math.floor(r * factor));
      const adjustedG = Math.min(255, Math.floor(g * factor));
      const adjustedB = Math.min(255, Math.floor(b * factor));
      
      return `rgb(${adjustedR}, ${adjustedG}, ${adjustedB})`;
    } catch (error) {
      return color;
    }
  }

  /**
   * Mask/cover background text areas with white rectangles before applying overlays
   * This prevents text-over-text issues when background images contain embedded text
   * @param {Object} template - Template configuration
   * @param {HTMLCanvasElement} backgroundImage - Loaded background image
   */
  async maskBackgroundTextAreas(template, backgroundImage) {
    try {
      if (!template?.text_overlays?.overlays) {
        return;
      }

      prodLogger.debug(
        "🎭 Masking background text areas to prevent conflicts",
        {
          templateId: template.id,
          overlayCount: template.text_overlays.overlays.length,
        }
      );

      // Mask each text overlay area with a white rectangle
      template.text_overlays.overlays.forEach((overlay) => {
        if (overlay.type === "logo") {
          return; // Skip logo overlays
        }

        const { x, y, width, height } = overlay.position;
        
        // Create a slightly larger mask to ensure complete coverage
        const maskPadding = 5;
        const maskX = Math.max(0, x - maskPadding);
        const maskY = Math.max(0, y - maskPadding);
        const maskWidth = Math.min(
          this.canvas.width - maskX,
          width + (maskPadding * 2)
        );
        const maskHeight = Math.min(
          this.canvas.height - maskY,
          height + (maskPadding * 2)
        );

        // Fill the area with white to mask any existing text
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.fillRect(maskX, maskY, maskWidth, maskHeight);

        prodLogger.debug(
          `🎭 Masked overlay area: ${overlay.id}`,
          { x: maskX, y: maskY, width: maskWidth, height: maskHeight }
        );
      });

      prodLogger.debug(
        "✅ Background text masking completed",
        { templateId: template.id }
      );
    } catch (error) {
      prodLogger.error("Error masking background text areas:", error);
      // Don't throw - this is a visual enhancement, not critical
    }
  }

  /**
   * Check if background image potentially contains text overlays already burned in
   * This helps detect templates where text was mistakenly saved into the background
   * @param {string} backgroundImageUrl - URL of the background image
   * @param {Object} template - Template configuration  
   * @returns {Promise<Object>} Analysis result
   */
  async analyzeBackgroundImage(backgroundImageUrl, template) {
    try {
      // For now, we'll detect this by checking the naming pattern and template structure
      // Future enhancement could use OCR or image analysis
      
      const hasTextOverlays = template?.text_overlays?.overlays?.length > 0;
      const backgroundUrlLower = backgroundImageUrl.toLowerCase();
      
      // Detect if background URL suggests it contains text
      const suspiciousPatterns = [
        'with-text',
        'filled',
        'sample',
        'preview',
        'rendered'
      ];
      
      const containsSuspiciousPattern = suspiciousPatterns.some(pattern => 
        backgroundUrlLower.includes(pattern)
      );
      
      const analysis = {
        likelyContainsText: containsSuspiciousPattern,
        hasOverlaySystem: hasTextOverlays,
        riskLevel: containsSuspiciousPattern && hasTextOverlays ? 'HIGH' : 'LOW',
        suggestion: containsSuspiciousPattern && hasTextOverlays 
          ? 'Background image may contain text that will conflict with overlays'
          : 'Background image appears clean for overlay system'
      };
      
      if (analysis.riskLevel === 'HIGH') {
        prodLogger.warn(
          "⚠️ Potential text-over-text issue detected",
          {
            templateId: template.id,
            backgroundUrl: backgroundImageUrl,
            analysis
          }
        );
      }
      
      return analysis;
    } catch (error) {
      prodLogger.error("Error analyzing background image:", error);
      return {
        likelyContainsText: false,
        hasOverlaySystem: false,
        riskLevel: 'UNKNOWN',
        suggestion: 'Could not analyze background image'
      };
    }
  }

  /**
   * Apply text transformation based on textTransform property
   * @param {string} text - Text to transform
   * @param {string} textTransform - Transform type (none, uppercase, lowercase, capitalize, capitalize-first)
   * @returns {string} Transformed text
   */
  applyTextTransform(text, textTransform) {
    if (!text || !textTransform || textTransform === "none") {
      return text;
    }

    switch (textTransform) {
      case "uppercase":
        return text.toUpperCase();

      case "lowercase":
        return text.toLowerCase();

      case "capitalize":
        // Capitalize each word
        return text.replace(/\b\w/g, (char) => char.toUpperCase());

      case "capitalize-first":
        // Capitalize only the first letter of the entire text
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();

      default:
        return text;
    }
  }

  /**
   * Populate placeholder text with document data
   * @param {string} placeholder - Placeholder text with {{variables}}
   * @param {Object} documentData - Document metadata
   * @param {Object} overlayCustomizations - Optional customizations for this overlay
   * @returns {string} Populated text
   */
  populatePlaceholder(placeholder, documentData, overlayCustomizations = {}) {
    if (!placeholder || typeof placeholder !== "string") {
      return "";
    }

    // Priority 1: Check for custom content override - SHOULD PREVENT DUAL TEXT
    // FIXED: More explicit check for custom content
    if (overlayCustomizations.content !== undefined && overlayCustomizations.content !== null) {
      return overlayCustomizations.content;
    }

    let populatedText = placeholder;

    // Replace common placeholders first
    const now = new Date();
    const replacements = {
      "{{title}}": documentData.title || "Untitled Document",
      "{{author}}": documentData.author || "Unknown Author",
      "{{description}}": documentData.description || "",
      "{{date}}": now.toLocaleDateString(),
      "{{year}}": now.getFullYear().toString(),
      "{{month}}": now.toLocaleDateString("en-US", { month: "long" }),
      "{{day}}": now.getDate().toString(),
      // New overlay type placeholders
      "{{desc}}": this.resolveDescPlaceholder(documentData),
      "{{for}}": this.resolveForPlaceholder(documentData),
    };

    Object.entries(replacements).forEach(([placeholder, value]) => {
      populatedText = populatedText.replace(placeholder, value);
    });

    // Use ProfileDataService for profile-specific placeholders
    if (documentData.userId && populatedText.includes("{{")) {
      // Extract all remaining placeholders
      const profilePlaceholders = populatedText.match(/\{\{([^}]+)\}\}/g);


      if (profilePlaceholders) {
        profilePlaceholders.forEach((fullPlaceholder) => {
          const fieldName = fullPlaceholder.replace(/[{}]/g, "");
          const profileValue = ProfileDataService.getProfileValue(
            fieldName,
            documentData
          );

          // Log failed placeholder replacements for monitoring
          if (!profileValue) {
            prodLogger.debug("Placeholder not replaced", {
              placeholder: fullPlaceholder,
              fieldName
            });
          }

          if (profileValue) {
            // Simple string replacement instead of regex to avoid escaping issues
            populatedText = populatedText.replace(
              fullPlaceholder,
              profileValue
            );
          }
        });
      }
    }
    return populatedText;
  }

  /**
   * Get the current text content for an overlay
   * This returns what would actually be rendered on the canvas
   * @param {Object} overlay - The overlay configuration
   * @param {Object} documentData - Document/profile data
   * @param {Object} overlayCustomizations - Current customizations for this overlay
   * @returns {string} The current rendered text content
   */
  getCurrentTextContent(overlay, documentData, overlayCustomizations = {}) {
    return this.populatePlaceholder(
      overlay.placeholder,
      documentData,
      overlayCustomizations
    );
  }


  /**
   * Render complete template with background image and text overlays
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} options - Rendering options
   * @param {Object} customizations - User customizations for overlays
   * @returns {Promise<HTMLCanvasElement>} Rendered canvas
   */
  async renderTemplate(
    template,
    documentData,
    options = {},
    customizations = {}
  ) {
    try {
      const {
        background_image_url,
        background_image_width,
        background_image_height,
        text_overlays,
      } = template;

      // Extract options
      const { 
        skipLogoOverlays = false,
        enableBackgroundCleaning = true 
      } = options;

      // Register template fonts (custom fonts + preloaded Google Fonts)
      await this.registerTemplateFonts(template);

      // Preload fonts for better performance
      await this.preloadTemplateFonts(template);

      // Initialize canvas
      this.initializeCanvas(background_image_width, background_image_height);
      
      // Clear the entire canvas to prevent overlapping renders
      this.ctx.clearRect(0, 0, background_image_width, background_image_height);

      // Load background image
      const backgroundImage = await this.loadBackgroundImage(
        background_image_url
      );

      // Draw background image
      this.ctx.drawImage(
        backgroundImage,
        0,
        0,
        background_image_width,
        background_image_height
      );

      // Clean background text areas if enabled
      if (enableBackgroundCleaning) {
        await this.intelligentBackgroundCleaning(template, backgroundImage);
      }

      // Text overlay rendering is handled in the unified customization section below

      // Render logo overlays if template supports them and not skipped
      if (templateHasLogoOverlays(template) && !skipLogoOverlays) {
        const logoOverlays = getLogoOverlays(template);
        const logoData = await getLogoDataForTemplate(
          documentData.userId,
          template
        );

        for (const overlay of logoOverlays) {
          const logoForOverlay = logoData[overlay.id];
          if (logoForOverlay) {
            await renderLogoOverlay(
              this.ctx,
              logoForOverlay,
              overlay.position,
              overlay.styling
            );
          }
        }
      }

      return this.canvas;
    } catch (error) {
      prodLogger.error("Error rendering template:", error);
      errorMonitor.captureError(
        error,
        { template: template?.id },
        ErrorSeverity.MEDIUM
      );
      throw error;
    }
  }

  /**
   * Render template with customizations applied
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} customizations - User customizations for overlays
   * @param {Object} options - Rendering options
   * @returns {Promise<HTMLCanvasElement>} Rendered canvas
   */
  async renderTemplateWithCustomizations(
    template,
    documentData,
    customizations = {},
    options = {}
  ) {
    try {
      const {
        background_image_url,
        background_image_width,
        background_image_height,
        text_overlays,
      } = template;

      // Extract options
      const { enableBackgroundCleaning = true } = options;

      // Clear any cached background image for this template to prevent conflicts
      this.clearTemplateCache(template.id, background_image_url);
      
      // Register template fonts (custom fonts + preloaded Google Fonts)
      await this.registerTemplateFonts(template, customizations);

      // Preload fonts for better performance (including customized fonts)
      await this.preloadTemplateFonts(template);
      await this.preloadCustomizationFonts(customizations);

      // Force complete canvas reset to prevent text-over-text issues
      this.forceCanvasReset(background_image_width, background_image_height);

      // Reset rendered overlay tracking for this render cycle
      this.renderedOverlays.clear();
      prodLogger.debug("🔄 Reset overlay tracking for new render cycle", { templateId: template.id });
      
      // Analyze background image for potential text conflicts
      const backgroundAnalysis = await this.analyzeBackgroundImage(
        background_image_url,
        template
      );

      // Load background image
      const backgroundImage = await this.loadBackgroundImage(
        background_image_url
      );

      // Draw background image
      this.ctx.drawImage(
        backgroundImage,
        0,
        0,
        background_image_width,
        background_image_height
      );

      // Enhanced text conflict resolution to prevent dual text rendering
      if (enableBackgroundCleaning) {
        await this.conditionalBackgroundCleaning(template, documentData, customizations, backgroundImage);
      }

      // Unified overlay rendering: Handle all overlays (text and logo) with customizations
      if (text_overlays && text_overlays.overlays) {
        // Get logo data once for all logo overlays
        const logoData = documentData.userId
          ? await this.getLogoDataForOverlays(
              documentData.userId,
              text_overlays.overlays,
              customizations
            )
          : {};

        prodLogger.debug(
          "🎨 Starting unified overlay rendering",
          {
            templateId: template.id,
            totalOverlays: text_overlays.overlays.length,
            hasCustomizations: Object.keys(customizations).length > 0,
          }
        );

        for (const overlay of text_overlays.overlays) {
          // Skip hidden overlays
          const overlayCustomizations = customizations[overlay.id] || {};
          if (overlayCustomizations.hidden) {
            prodLogger.debug(`⏭️ Skipping hidden overlay: ${overlay.id}`);
            continue;
          }

          // Apply customizations to overlay
          const customizedOverlay = this.applyCustomizations(
            overlay,
            overlayCustomizations
          );

          if (overlay.type === "logo") {
            // Handle logo overlay
            const logoForOverlay = logoData[overlay.id];

            if (logoForOverlay) {
              prodLogger.debug(`🖼️ Rendering logo overlay: ${overlay.id}`);
              await this.renderLogoOverlay(
                logoForOverlay,
                customizedOverlay.position,
                customizedOverlay.styling
              );
            } else {
              prodLogger.debug(`⏭️ No logo data for overlay: ${overlay.id}`);
            }
          } else {
            // Handle text overlay with duplication prevention

            // Prevent duplicate rendering of the same overlay
            if (this.renderedOverlays.has(overlay.id)) {
              prodLogger.warn("Prevented duplicate text overlay rendering", {
                overlayId: overlay.id,
                templateId: template.id,
              });
              continue;
            }

            const populatedText = this.populatePlaceholder(
              overlay.placeholder,
              documentData,
              overlayCustomizations
            );

            prodLogger.debug("Rendering text overlay", {
              overlayId: overlay.id,
              hasCustomContent: overlayCustomizations.content !== undefined,
              textLength: populatedText.length,
            });

            if (populatedText.trim()) {
              // Mark this overlay as rendered to prevent duplication
              this.renderedOverlays.add(overlay.id);

              await this.renderTextOverlay(
                populatedText,
                customizedOverlay.position,
                customizedOverlay.styling
              );


            } else {
              prodLogger.debug(`⏭️ Skipping empty text overlay: ${overlay.id}`);
            }
          }
        }

        prodLogger.debug("Completed unified overlay rendering", { templateId: template.id });
      }

      return this.canvas;
    } catch (error) {
      prodLogger.error("Error rendering template with customizations:", error);
      errorMonitor.captureError(
        error,
        { template: template?.id, customizations },
        ErrorSeverity.MEDIUM
      );
      throw error;
    }
  }

  /**
   * Apply user customizations to an overlay configuration
   * @param {Object} originalOverlay - Original overlay configuration
   * @param {Object} customizations - User customizations
   * @returns {Object} Customized overlay configuration
   */
  applyCustomizations(originalOverlay, customizations) {
    return {
      ...originalOverlay,
      position: {
        ...originalOverlay.position,
        ...customizations.position,
      },
      styling: {
        ...originalOverlay.styling,
        ...customizations.styling,
      },
    };
  }

  /**
   * Get logo data for logo overlays in the overlays array
   * @param {string} userId - User ID
   * @param {Array} overlays - Array of all overlays (text and logo)
   * @param {Object} customizations - User customizations
   * @returns {Promise<Object>} Logo data for logo overlays
   */
  async getLogoDataForOverlays(userId, overlays, customizations = {}) {
    try {

      // Handle missing userId gracefully
      if (!userId) {
        return {};
      }

      // Filter to only logo overlays
      const logoOverlays = overlays.filter(
        (overlay) => overlay.type === "logo"
      );

      if (logoOverlays.length === 0) {
        return {};
      }

      // Use the existing logo overlay service to get logo data
      // Create mock template with unified structure (text_overlays.overlays)
      const mockTemplate = {
        text_overlays: {
          overlays: logoOverlays,
        },
      };
      const result = await getLogoDataForTemplate(
        userId,
        mockTemplate,
        customizations
      );

      return result;
    } catch (error) {
      prodLogger.error("Error getting logo data for overlays:", error);
      return {};
    }
  }

  /**
   * Render logo overlay on canvas
   * @param {Object} logoData - Logo data with image buffer
   * @param {Object} position - Position configuration
   * @param {Object} styling - Styling configuration
   * @returns {Promise<void>}
   */
  async renderLogoOverlay(logoData, position, styling = {}) {
    try {
      if (!logoData || !logoData.data) {
        prodLogger.warn("No logo data provided for logo overlay");
        return;
      }

      // Use the existing logo overlay service to render
      await renderLogoOverlay(this.ctx, logoData, position, styling);
    } catch (error) {
      prodLogger.error("Error rendering logo overlay:", error);
      // Don't throw - continue with other overlays
    }
  }

  /**
   * Preload fonts used in template for better performance
   * @param {Object} template - Template configuration
   * @returns {Promise<void>} Promise that resolves when all fonts are loaded
   */
  async preloadTemplateFonts(template) {
    try {
      if (!template?.text_overlays?.overlays) {
        return;
      }

      const fontFamilies = new Set();

      // Collect all unique font families from overlays
      template.text_overlays.overlays.forEach((overlay) => {
        if (overlay.styling?.fontFamily) {
          fontFamilies.add(overlay.styling.fontFamily);
        }
      });

      // Load all fonts in parallel
      const loadPromises = Array.from(fontFamilies).map((fontFamily) =>
        fontLoader
          .ensureFontLoaded(fontFamily, {
            fallback: "Arial",
            loadGoogleFonts: true,
          })
          .catch((error) => {
            prodLogger.warn(`Failed to preload font ${fontFamily}:`, error);
            // Don't fail the entire operation if one font fails
          })
      );

      await Promise.all(loadPromises);
      prodLogger.debug(
        `Preloaded ${fontFamilies.size} fonts for template ${template.id}`
      );
    } catch (error) {
      prodLogger.warn("Error preloading template fonts:", error);
      // Don't throw - this is an optimization, not critical
    }
  }

  /**
   * Register template fonts (custom fonts + preloaded Google Fonts)
   * @param {Object} template - Template configuration
   * @param {Object} customizations - User customizations
   * @returns {Promise<void>} Promise that resolves when fonts are registered
   */
  async registerTemplateFonts(template, customizations = {}) {
    try {
      // Import preloaded Google Fonts utilities
      const {
        extractPreloadedGoogleFontsFromTemplate,
        extractPreloadedGoogleFontsFromCustomizations,
        logPreloadedGoogleFontsUsage,
      } = await import("../utils/preloadedGoogleFonts.js");

      // Register custom fonts with URLs
      let customFontsRegistered = 0;
      if (template?.custom_fonts?.fonts) {
        const customFonts = template.custom_fonts.fonts;

        const registrationPromises = customFonts.map(async (font) => {
          try {
            if (font.customUrl && font.name) {
              fontLoader.registerCustomFont(font.name, font.customUrl);
              prodLogger.debug(`Registered template custom font: ${font.name}`);
              customFontsRegistered++;
            }
          } catch (error) {
            prodLogger.warn(
              `Failed to register template custom font ${font.name}:`,
              error
            );
          }
        });

        await Promise.all(registrationPromises);
      }

      // Extract and load preloaded Google Fonts from template and customizations
      const templatePreloadedFonts =
        extractPreloadedGoogleFontsFromTemplate(template);
      const customizationPreloadedFonts =
        extractPreloadedGoogleFontsFromCustomizations(customizations);

      const allPreloadedFonts = [
        ...new Set([...templatePreloadedFonts, ...customizationPreloadedFonts]),
      ];
      logPreloadedGoogleFontsUsage(
        allPreloadedFonts,
        "template+customizations"
      );

      // Load preloaded Google Fonts
      let preloadedFontsLoaded = 0;
      if (allPreloadedFonts.length > 0) {
        const preloadedPromises = allPreloadedFonts.map(async (fontName) => {
          try {
            await fontLoader.loadGoogleFont(fontName);
            prodLogger.debug(`Loaded preloaded Google Font: ${fontName}`);
            preloadedFontsLoaded++;
          } catch (error) {
            prodLogger.warn(
              `Failed to load preloaded Google Font ${fontName}:`,
              error
            );
          }
        });

        await Promise.all(preloadedPromises);
      }

      if (customFontsRegistered > 0 || preloadedFontsLoaded > 0) {
        prodLogger.debug(
          `✅ Image Overlay Service: Registered ${customFontsRegistered} custom fonts and loaded ${preloadedFontsLoaded} preloaded Google Fonts for template ${template.id}`
        );
      }
    } catch (error) {
      prodLogger.warn("Error registering template fonts:", error);
      // Don't throw - this is an enhancement, not critical
    }
  }

  /**
   * Preload fonts used in customizations for better performance
   * @param {Object} customizations - User customizations
   * @returns {Promise<void>} Promise that resolves when all fonts are loaded
   */
  async preloadCustomizationFonts(customizations) {
    try {
      if (!customizations || typeof customizations !== "object") {
        return;
      }

      const fontFamilies = new Set();

      // Collect all unique font families from customizations
      Object.values(customizations).forEach((customization) => {
        if (customization?.styling?.fontFamily) {
          fontFamilies.add(customization.styling.fontFamily);
        }
      });

      if (fontFamilies.size === 0) {
        return;
      }

      // Load all fonts in parallel
      const loadPromises = Array.from(fontFamilies).map((fontFamily) =>
        fontLoader
          .ensureFontLoaded(fontFamily, {
            fallback: "Arial",
            loadGoogleFonts: true,
          })
          .catch((error) => {
            prodLogger.warn(
              `Failed to preload customization font ${fontFamily}:`,
              error
            );
            // Don't fail the entire operation if one font fails
          })
      );

      await Promise.all(loadPromises);
      prodLogger.debug(`Preloaded ${fontFamilies.size} customization fonts`);
    } catch (error) {
      prodLogger.warn("Error preloading customization fonts:", error);
      // Don't throw - this is an optimization, not critical
    }
  }

  /**
   * Export rendered template as image data
   * @param {HTMLCanvasElement} canvas - Rendered canvas
   * @param {string} format - Export format ('png', 'jpeg', 'webp')
   * @param {number} quality - Export quality (0-1)
   * @returns {string} Base64 image data URL
   */
  exportAsImage(canvas = this.canvas, format = "png", quality = 0.9) {
    try {
      if (!canvas) {
        throw new Error("No canvas available for export");
      }

      const mimeType = `image/${format}`;
      return canvas.toDataURL(mimeType, quality);
    } catch (error) {
      prodLogger.error("Error exporting image:", error);
      throw error;
    }
  }

  /**
   * Generate preview with sample data
   * @param {Object} template - Template configuration
   * @param {Object} sampleData - Sample document data for preview
   * @returns {Promise<string>} Base64 image data URL
   */
  async generatePreview(template, sampleData = null) {
    try {
      const defaultSampleData = {
        title: "Sample Document Title",
        author: "John Doe",
        description:
          "This is a sample description to show how the template will look with your content.",
      };

      const previewData = sampleData || defaultSampleData;
      const canvas = await this.renderTemplate(template, previewData, {}, {});

      return this.exportAsImage(canvas, "png", 0.8);
    } catch (error) {
      prodLogger.error("Error generating preview:", error);
      throw error;
    }
  }

  /**
   * Clear cached image for a specific template
   * Use this when switching templates to prevent cached background conflicts
   * @param {string} templateId - Template ID
   * @param {string} backgroundImageUrl - Background image URL to clear
   */
  clearTemplateCache(templateId, backgroundImageUrl) {
    try {
      if (backgroundImageUrl && this.imageCache.has(backgroundImageUrl)) {
        this.imageCache.delete(backgroundImageUrl);
        prodLogger.debug(
          "🗑️ Cleared cached background image for template",
          { templateId, backgroundImageUrl: backgroundImageUrl.substring(0, 100) + '...' }
        );
      }
    } catch (error) {
      prodLogger.error("Error clearing template cache:", error);
    }
  }

  /**
   * Force immediate background cleaning and re-render
   * Use this when user edits text to immediately show the effect
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document data
   * @param {Object} customizations - Current customizations
   * @returns {Promise<HTMLCanvasElement>} Updated canvas
   */
  async forceCleanRender(template, documentData, customizations = {}) {
    try {
      prodLogger.debug(
        "🔄 Force clean render triggered",
        {
          templateId: template.id,
          customizationCount: Object.keys(customizations).length,
        }
      );

      // Clear template cache to ensure fresh background
      this.clearTemplateCache(template.id, template.background_image_url);
      
      // Render with background cleaning enabled
      const canvas = await this.renderTemplateWithCustomizations(
        template,
        documentData,
        customizations,
        { enableBackgroundCleaning: true }
      );

      prodLogger.debug(
        "✅ Force clean render completed",
        { templateId: template.id }
      );

      return canvas;
    } catch (error) {
      prodLogger.error("Error during force clean render:", error);
      throw error;
    }
  }

  /**
   * Render only text overlays on an existing canvas (for fast text-only updates)
   * @param {Object} template - Template configuration
   * @param {Object} documentData - Document metadata
   * @param {Object} customizations - User customizations for overlays
   * @param {HTMLCanvasElement} targetCanvas - Canvas to render text overlays onto
   * @returns {Promise<void>}
   */
  async renderTextOverlaysOnly(template, documentData, customizations = {}, targetCanvas) {
    try {
      if (!template?.text_overlays?.overlays || !targetCanvas) {
        return;
      }

      const ctx = targetCanvas.getContext('2d');
      const { text_overlays } = template;

      prodLogger.debug('⚡ Rendering text overlays only', {
        templateId: template.id,
        overlayCount: text_overlays.overlays.length,
        customizationCount: Object.keys(customizations).length
      });

      // Process each text overlay
      for (const overlay of text_overlays.overlays) {
        if (overlay.type === 'logo') {
          continue; // Skip logo overlays in text-only render
        }

        const overlayCustomizations = customizations[overlay.id] || {};

        // Skip hidden overlays
        if (overlayCustomizations.hidden) {
          continue;
        }

        // Apply customizations to overlay
        const customizedOverlay = this.applyCustomizations(overlay, overlayCustomizations);

        // Get the populated text content
        const populatedText = this.populatePlaceholder(
          overlay.placeholder,
          documentData,
          overlayCustomizations
        );

        // Only render if there's text content
        if (populatedText.trim()) {
          // Render the text overlay with correct parameters
          await this.renderTextOverlay(
            populatedText,
            customizedOverlay.position,
            customizedOverlay.styling
          );
        }
      }

      prodLogger.debug('⚡ Text-only rendering completed');
    } catch (error) {
      prodLogger.error('Error rendering text overlays only:', error);
      throw error;
    }
  }

  /**
   * Clear background cleaning cache for a specific template
   * @param {string} templateId - Template ID
   * @param {string} backgroundImageUrl - Background image URL
   */
  clearBackgroundCleaningCache(templateId, backgroundImageUrl) {
    const cacheKey = `${templateId}-${backgroundImageUrl}`;
    this.backgroundCleaningCache.delete(cacheKey);
    prodLogger.debug('🗑️ Cleared background cleaning cache', { templateId, cacheKey });
  }

  /**
   * Clear all caches
   */
  clearCache() {
    this.imageCache.clear();
    this.backgroundCleaningCache.clear();
    this.lastCustomizations = {};
    prodLogger.debug('🗑️ Cleared all caches');
  }
}

// Create singleton instance
const imageOverlayService = new ImageOverlayService();

export default imageOverlayService;

// Export individual methods for convenience
export const { renderTemplate, generatePreview, exportAsImage, clearCache, forceCleanRender } =
  imageOverlayService;
