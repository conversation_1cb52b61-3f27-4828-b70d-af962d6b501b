/**
 * Document Statistics Service
 * Provides comprehensive document statistics calculation for all content types
 */
import { prodLogger } from "../utils/prodLogger.js";

/**
 * Calculate comprehensive document statistics from various content sources
 * @param {Object} generatedContent - Generated content structure
 * @param {Object} editorContent - Raw editor content (HTML)
 * @param {Object} fallbackOptions - Options for fallback calculations
 * @returns {Object} Comprehensive statistics object
 */
export const calculateComprehensiveStatistics = (
  generatedContent,
  editorContent = null,
  fallbackOptions = {}
) => {
  try {
    prodLogger.debug("📊 Calculating comprehensive document statistics", {
      hasGeneratedContent: !!generatedContent,
      hasEditorContent: !!editorContent,
      generatedContentType: typeof generatedContent,
      editorContentType: typeof editorContent,
    });

    // Priority 1: Use existing structured content if available
    if (
      generatedContent &&
      generatedContent.chapters &&
      generatedContent.chapters.length > 0
    ) {
      return calculateFromStructuredContent(generatedContent);
    }

    // Priority 2: Calculate from editor HTML content
    if (editorContent && editorContent.trim()) {
      return calculateFromEditorContent(editorContent, generatedContent);
    }

    // Priority 3: Extract from raw generatedContent text fields
    if (generatedContent && hasTextContent(generatedContent)) {
      return calculateFromRawContent(generatedContent);
    }

    // Priority 4: Return zero stats for completely blank documents
    return getZeroStats(fallbackOptions);
  } catch (error) {
    prodLogger.error("Error calculating comprehensive statistics:", error);
    return getZeroStats(fallbackOptions);
  }
};

/**
 * Calculate statistics from structured content (chapters, introduction, conclusion)
 */
const calculateFromStructuredContent = (generatedContent) => {
  let totalWords = 0;
  let totalCharacters = 0;
  let chapters = 0;
  let sections = 0;

  // Introduction
  if (generatedContent.introduction?.content) {
    const introStats = analyzeTextContent(
      generatedContent.introduction.content
    );
    totalWords += introStats.words;
    totalCharacters += introStats.characters;
    sections++;
  }

  // Chapters
  if (generatedContent.chapters && generatedContent.chapters.length > 0) {
    chapters = generatedContent.chapters.length;
    generatedContent.chapters.forEach((chapter) => {
      if (chapter.wordCount) {
        totalWords += chapter.wordCount;
      } else if (chapter.content) {
        const chapterStats = analyzeTextContent(chapter.content);
        totalWords += chapterStats.words;
        totalCharacters += chapterStats.characters;
      }
      sections++;
    });
  }

  // Conclusion
  if (generatedContent.conclusion?.content) {
    const conclusionStats = analyzeTextContent(
      generatedContent.conclusion.content
    );
    totalWords += conclusionStats.words;
    totalCharacters += conclusionStats.characters;
    sections++;
  }

  return {
    words: totalWords,
    characters: totalCharacters,
    chapters: chapters,
    sections: sections,
    pages: Math.ceil(totalWords / 250),
    readTime: Math.ceil(totalWords / 200),
    source: "structured",
  };
};

/**
 * Calculate statistics from editor HTML content
 */
const calculateFromEditorContent = (editorHTML, generatedContent = null) => {
  // Strip HTML tags and get plain text
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = editorHTML;
  const plainText = tempDiv.textContent || tempDiv.innerText || "";

  const stats = analyzeTextContent(plainText);

  // Count headings as chapters/sections
  const headingMatches = editorHTML.match(/<h[1-6][^>]*>/gi) || [];
  const h1Count = (editorHTML.match(/<h1[^>]*>/gi) || []).length;
  const totalHeadings = headingMatches.length;

  return {
    words: stats.words,
    characters: stats.characters,
    chapters: Math.max(h1Count, 1), // At least 1 chapter
    sections: Math.max(totalHeadings, 1), // At least 1 section
    pages: Math.ceil(stats.words / 250),
    readTime: Math.ceil(stats.words / 200),
    source: "editor",
    title: generatedContent?.title || extractTitleFromHTML(editorHTML),
  };
};

/**
 * Calculate statistics from raw content fields
 */
const calculateFromRawContent = (generatedContent) => {
  let totalWords = 0;
  let totalCharacters = 0;
  let contentSources = 0;

  // Check various content fields
  const contentFields = [
    generatedContent.title,
    generatedContent.introduction,
    generatedContent.conclusion,
    generatedContent.content,
    generatedContent.description,
  ];

  contentFields.forEach((field) => {
    if (field && typeof field === "string" && field.trim()) {
      const stats = analyzeTextContent(field);
      totalWords += stats.words;
      totalCharacters += stats.characters;
      contentSources++;
    }
  });

  return {
    words: totalWords,
    characters: totalCharacters,
    chapters: Math.max(contentSources, 1),
    sections: contentSources,
    pages: Math.ceil(totalWords / 250),
    readTime: Math.ceil(totalWords / 200),
    source: "raw",
  };
};

/**
 * Analyze text content for word and character counts
 */
const analyzeTextContent = (text) => {
  if (!text || typeof text !== "string") {
    return { words: 0, characters: 0, paragraphs: 0, sentences: 0 };
  }

  const cleanText = text.trim();
  const words = cleanText.split(/\s+/).filter((word) => word.length > 0).length;
  const characters = cleanText.length;
  const paragraphs = cleanText
    .split(/\n\s*\n/)
    .filter((p) => p.trim().length > 0).length;
  const sentences = cleanText
    .split(/[.!?]+/)
    .filter((s) => s.trim().length > 0).length;

  return { words, characters, paragraphs, sentences };
};

/**
 * Check if generatedContent has any text content
 */
const hasTextContent = (generatedContent) => {
  if (!generatedContent) return false;

  const textFields = [
    generatedContent.title,
    generatedContent.introduction,
    generatedContent.conclusion,
    generatedContent.content,
    generatedContent.description,
  ];

  return textFields.some(
    (field) => field && typeof field === "string" && field.trim().length > 0
  );
};

/**
 * Extract title from HTML content
 */
const extractTitleFromHTML = (html) => {
  const h1Match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
  if (h1Match) {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = h1Match[1];
    return tempDiv.textContent || tempDiv.innerText || "Untitled Document";
  }
  return "Untitled Document";
};

/**
 * Return zero statistics with proper structure
 */
const getZeroStats = (options = {}) => {
  return {
    words: 0,
    characters: 0,
    chapters: 0,
    sections: 0,
    pages: 1,
    readTime: 0,
    source: "blank",
    title: options.title || "Untitled Document",
  };
};

/**
 * Real-time statistics calculation for live editing
 * @param {HTMLElement|string} editorElement - Editor element or HTML string
 * @returns {Object} Real-time statistics
 */
export const calculateRealTimeStats = (editorElement) => {
  try {
    let content = "";

    if (typeof editorElement === "string") {
      content = editorElement;
    } else if (editorElement && editorElement.innerHTML) {
      content = editorElement.innerHTML;
    } else if (editorElement && typeof editorElement.getHTML === "function") {
      content = editorElement.getHTML();
    }

    return calculateFromEditorContent(content);
  } catch (error) {
    prodLogger.error("Error calculating real-time stats:", error);
    return getZeroStats();
  }
};

/**
 * Enhanced version of the original getDocumentStatistics for backward compatibility
 */
export const getEnhancedDocumentStatistics = (
  generatedContent,
  editorContent = null
) => {
  const stats = calculateComprehensiveStatistics(
    generatedContent,
    editorContent
  );

  // Return in the format expected by existing components
  return {
    pages: stats.pages,
    chapters: stats.chapters,
    words: stats.words,
    readTime: `${stats.readTime}m`,
    characters: stats.characters,
    sections: stats.sections,
    source: stats.source,
  };
};
