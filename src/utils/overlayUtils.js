/**
 * Utility functions for overlay handling
 */

/**
 * Format overlay name for display by replacing underscores with spaces and capitalizing
 * @param {string} overlayId - The overlay ID to format
 * @returns {string} - Formatted display name
 */
export function formatOverlayDisplayName(overlayId) {
  if (!overlayId) return "";

  return overlayId
    .replace(/_/g, " ") // Replace underscores with spaces
    .split(" ") // Split into words
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
    .join(" "); // Join back with spaces
}

/**
 * Format overlay type for display
 * @param {string} type - The overlay type
 * @returns {string} - Formatted display type
 */
export function formatOverlayType(type) {
  if (!type) return "Text";

  switch (type) {
    case "logo":
      return "Logo";
    case "company":
      return "Company";
    case "website":
      return "Website";
    case "email":
      return "Email";
    case "phone":
      return "Phone";
    case "text":
    default:
      return "Text";
  }
}

/**
 * Get user-friendly label for overlay with type indicator
 * @param {Object} overlay - The overlay object
 * @returns {string} - Formatted label with type
 */
export function getOverlayDisplayLabel(overlay) {
  if (!overlay) return "";

  const displayName = formatOverlayDisplayName(overlay.id);
  const type = formatOverlayType(overlay.type);

  if (overlay.type === "logo") {
    return `${displayName} (Logo)`;
  }

  return displayName;
}
