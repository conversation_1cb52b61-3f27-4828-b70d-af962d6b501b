import { prodLogger } from './prodLogger.js';

/**
 * Simple utility to test image URL extraction from HTML
 */

// Helper function to log with clear visual separation
const logSection = (title) => {
  prodLogger.debug(`\n----- ${title} -----`);
};

/**
 * Extract image URLs from HTML string
 * @param {string} html - HTML string to analyze
 * @returns {Array<string>} Array of image URLs found
 */
export const findImagesInHTML = (html) => {
  if (!html || typeof html !== "string") {
    prodLogger.error("Input must be a non-empty string");
    return [];
  }

  logSection("HTML Analysis");
  prodLogger.debug(`Input length: ${html.length} characters`);
  prodLogger.debug(`Sample: ${html.substring(0, 100)}...`);

  const results = [];
  const containsImgTag = html.includes("<img");
  const containsSrcAttribute = html.includes("src=");

  prodLogger.debug(`Contains <img> tag: ${containsImgTag}`);
  prodLogger.debug(`Contains src= attribute: ${containsSrcAttribute}`);

  if (!containsImgTag && !containsSrcAttribute) {
    prodLogger.debug("No image tags or src attributes found");
    return [];
  }

  // Find all occurrences of <img
  logSection("Image Tag Analysis");
  let startIndex = 0;
  const imgPositions = [];

  while (startIndex < html.length) {
    const foundIndex = html.indexOf("<img", startIndex);
    if (foundIndex === -1) break;

    imgPositions.push(foundIndex);
    startIndex = foundIndex + 4;
  }

  prodLogger.debug(`Found ${imgPositions.length} occurrences of <img tag`);

  // Extract src attributes from each position
  imgPositions.forEach((pos, index) => {
    // Find the closing > of this tag
    const endTagPos = html.indexOf(">", pos);
    if (endTagPos === -1) return;

    // Extract the whole tag
    const imgTag = html.substring(pos, endTagPos + 1);
    prodLogger.debug(`\nImage tag #${index + 1}: ${imgTag}`);

    // Find src attribute
    const srcPos = imgTag.indexOf("src=");
    if (srcPos === -1) {
      prodLogger.debug(`  No src attribute in this tag`);
      return;
    }

    // Find the quote character used (single or double)
    const afterSrc = imgTag.substring(srcPos + 4);
    const quoteChar = afterSrc[0];

    if (quoteChar !== '"' && quoteChar !== "'") {
      prodLogger.debug(`  Invalid src format - no quote after src=`);
      return;
    }

    // Find the closing quote
    const closeQuotePos = afterSrc.indexOf(quoteChar, 1);
    if (closeQuotePos === -1) {
      prodLogger.debug(`  Cannot find closing quote for src attribute`);
      return;
    }

    // Extract the URL
    const imageUrl = afterSrc.substring(1, closeQuotePos);
    prodLogger.debug(`  Found URL: ${imageUrl}`);
    results.push(imageUrl);
  });

  logSection("Results");
  prodLogger.debug(`Found ${results.length} image URLs`);
  results.forEach((url, i) => prodLogger.debug(`${i + 1}. ${url}`));

  return results;
};



export default findImagesInHTML;
