/**
 * Change Detection Utilities
 * Optimized functions for detecting different types of changes in overlay customizations
 */

import { prodLogger } from './prodLogger.js';

/**
 * Detect if only text content changed (not styling or position)
 * @param {Object} prevCustomizations - Previous customizations
 * @param {Object} currentCustomizations - Current customizations
 * @returns {boolean} True if only text content changed
 */
export function detectTextOnlyChange(prevCustomizations = {}, currentCustomizations = {}) {
  try {
    const prevKeys = Object.keys(prevCustomizations);
    const currentKeys = Object.keys(currentCustomizations);

    prodLogger.debug('🔍 Change detection analysis:', {
      prevOverlayCount: prevKeys.length,
      currentOverlayCount: currentKeys.length
    });

    // If different number of overlays, not text-only
    if (prevKeys.length !== currentKeys.length) {
      prodLogger.debug('❌ Different number of overlays');
      return false;
    }

    let hasContentChanges = false;

    // Check each overlay for changes
    for (const overlayId of currentKeys) {
      const prevCustomization = prevCustomizations[overlayId] || {};
      const currentCustomization = currentCustomizations[overlayId] || {};

      // Extract content and non-content properties
      const { content: prevContent, ...prevRest } = prevCustomization;
      const { content: currentContent, ...currentRest } = currentCustomization;

      // If non-content properties changed, not text-only
      if (JSON.stringify(prevRest) !== JSON.stringify(currentRest)) {
        prodLogger.debug(`❌ Non-content properties changed for ${overlayId}`, {
          prevRest,
          currentRest
        });
        return false;
      }

      // Track if any content changed
      if (prevContent !== currentContent) {
        hasContentChanges = true;
        prodLogger.debug(`✅ Content changed for ${overlayId}: "${prevContent}" → "${currentContent}"`);
      }
    }

    const result = hasContentChanges;
    prodLogger.debug(`🎯 FINAL RESULT: ${result ? 'TEXT-ONLY CHANGE' : 'NO CHANGES'}`, {
      hasContentChanges
    });

    // Only return true if there were content changes and no styling/position changes
    return result;
  } catch (error) {
    prodLogger.warn('Error detecting text-only change, defaulting to full render:', error);
    return false;
  }
}

/**
 * Fast hash function for object comparison
 * @param {Object} obj - Object to hash
 * @returns {number} Hash value
 */
export function hashObject(obj) {
  if (!obj || typeof obj !== 'object') {
    return 0;
  }
  
  try {
    const str = JSON.stringify(obj);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash;
  } catch (error) {
    prodLogger.warn('Error hashing object:', error);
    return 0;
  }
}

/**
 * Create efficient change signature for template rendering
 * @param {Object} template - Template object
 * @param {Object} documentData - Document data
 * @param {Object} customizations - Customizations
 * @param {Object} logoCustomizations - Logo customizations
 * @param {number} templateWidth - Template width
 * @param {number} templateHeight - Template height
 * @returns {string} Change signature
 */
export function createChangeSignature(
  template, 
  documentData, 
  customizations, 
  logoCustomizations, 
  templateWidth, 
  templateHeight
) {
  try {
    return `${template?.id || 'no-template'}-${hashObject(documentData)}-${hashObject(customizations)}-${hashObject(logoCustomizations)}-${templateWidth}x${templateHeight}`;
  } catch (error) {
    prodLogger.warn('Error creating change signature:', error);
    return `fallback-${Date.now()}`;
  }
}

/**
 * Detect if only specific overlay content changed
 * @param {Object} prevCustomizations - Previous customizations
 * @param {Object} currentCustomizations - Current customizations
 * @param {string} overlayId - Specific overlay ID to check
 * @returns {boolean} True if only this overlay's content changed
 */
export function detectSingleOverlayContentChange(prevCustomizations = {}, currentCustomizations = {}, overlayId) {
  try {
    // Check if only the specified overlay changed
    const prevKeys = Object.keys(prevCustomizations);
    const currentKeys = Object.keys(currentCustomizations);
    
    // If different overlays are present, not a single overlay change
    if (JSON.stringify(prevKeys.sort()) !== JSON.stringify(currentKeys.sort())) {
      return false;
    }
    
    // Check each overlay
    for (const id of currentKeys) {
      const prevCustomization = prevCustomizations[id] || {};
      const currentCustomization = currentCustomizations[id] || {};
      
      if (id === overlayId) {
        // For the target overlay, only content should change
        const { content: prevContent, ...prevRest } = prevCustomization;
        const { content: currentContent, ...currentRest } = currentCustomization;
        
        // Non-content properties should be the same
        if (JSON.stringify(prevRest) !== JSON.stringify(currentRest)) {
          return false;
        }
        
        // Content should be different
        if (prevContent === currentContent) {
          return false;
        }
      } else {
        // Other overlays should be unchanged
        if (JSON.stringify(prevCustomization) !== JSON.stringify(currentCustomization)) {
          return false;
        }
      }
    }
    
    return true;
  } catch (error) {
    prodLogger.warn('Error detecting single overlay content change:', error);
    return false;
  }
}

/**
 * Get list of overlays with content changes
 * @param {Object} prevCustomizations - Previous customizations
 * @param {Object} currentCustomizations - Current customizations
 * @returns {Array} Array of overlay IDs with content changes
 */
export function getOverlaysWithContentChanges(prevCustomizations = {}, currentCustomizations = {}) {
  try {
    const changedOverlays = [];
    const allOverlayIds = new Set([
      ...Object.keys(prevCustomizations),
      ...Object.keys(currentCustomizations)
    ]);
    
    for (const overlayId of allOverlayIds) {
      const prevCustomization = prevCustomizations[overlayId] || {};
      const currentCustomization = currentCustomizations[overlayId] || {};
      
      const prevContent = prevCustomization.content;
      const currentContent = currentCustomization.content;
      
      if (prevContent !== currentContent) {
        changedOverlays.push(overlayId);
      }
    }
    
    return changedOverlays;
  } catch (error) {
    prodLogger.warn('Error getting overlays with content changes:', error);
    return [];
  }
}
