/**
 * Unit tests for chapter heading detection utility
 * Tests the hasExistingChapterHeading function with various content formats
 */

import { hasExistingChapterHeading } from '../contentProcessing.js';

describe('hasExistingChapterHeading', () => {
  describe('HTML content detection', () => {
    test('should detect h1 chapter heading', () => {
      const content = '<h1>Chapter 1: Introduction</h1><p>Content here</p>';
      expect(hasExistingChapterHeading(content, 1, 'Introduction')).toBe(true);
    });

    test('should detect h2 chapter heading', () => {
      const content = '<h2>Chapter 2: Getting Started</h2><p>Content here</p>';
      expect(hasExistingChapterHeading(content, 2, 'Getting Started')).toBe(true);
    });

    test('should detect chapter heading with class attributes', () => {
      const content = '<h2 class="chapter-title">Chapter 3: Advanced Topics</h2>';
      expect(hasExistingChapterHeading(content, 3, 'Advanced Topics')).toBe(true);
    });

    test('should detect chapter heading with colon separator', () => {
      const content = '<h1>Chapter 1: The Beginning</h1>';
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });

    test('should detect chapter heading without colon', () => {
      const content = '<h2>Chapter 2 Overview</h2>';
      expect(hasExistingChapterHeading(content, 2)).toBe(true);
    });

    test('should not detect wrong chapter number', () => {
      const content = '<h1>Chapter 2: Introduction</h1>';
      expect(hasExistingChapterHeading(content, 1)).toBe(false);
    });

    test('should handle case insensitive matching', () => {
      const content = '<h1>chapter 1: introduction</h1>';
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });
  });

  describe('Markdown content detection', () => {
    test('should detect markdown h1 chapter heading', () => {
      const content = '# Chapter 1: Introduction\n\nContent here';
      expect(hasExistingChapterHeading(content, 1, 'Introduction')).toBe(true);
    });

    test('should detect markdown h2 chapter heading', () => {
      const content = '## Chapter 2: Getting Started\n\nContent here';
      expect(hasExistingChapterHeading(content, 2, 'Getting Started')).toBe(true);
    });

    test('should detect markdown h3 chapter heading', () => {
      const content = '### Chapter 3: Advanced Topics\n\nContent here';
      expect(hasExistingChapterHeading(content, 3, 'Advanced Topics')).toBe(true);
    });

    test('should detect chapter heading at start of line', () => {
      const content = 'Some intro text\n\n## Chapter 1: Main Content\n\nMore content';
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });

    test('should not detect chapter heading in middle of line', () => {
      const content = 'This is not ## Chapter 1: Main Content in the middle';
      expect(hasExistingChapterHeading(content, 1)).toBe(false);
    });
  });

  describe('Plain text content detection', () => {
    test('should detect plain text chapter heading', () => {
      const content = 'Chapter 1: Introduction\n\nThis is the content of the first chapter.';
      expect(hasExistingChapterHeading(content, 1, 'Introduction')).toBe(true);
    });

    test('should detect plain text chapter heading without colon', () => {
      const content = 'Chapter 2 Overview\n\nThis chapter covers the basics.';
      expect(hasExistingChapterHeading(content, 2)).toBe(true);
    });

    test('should detect chapter heading at start of content', () => {
      const content = 'Chapter 3: Advanced Features\nDetailed explanation...';
      expect(hasExistingChapterHeading(content, 3)).toBe(true);
    });
  });

  describe('Title-based detection', () => {
    test('should detect by title when provided', () => {
      const content = '<h1>Chapter 1: The Great Adventure</h1>';
      expect(hasExistingChapterHeading(content, 1, 'The Great Adventure')).toBe(true);
    });

    test('should detect title without chapter prefix', () => {
      const content = '<h1>The Great Adventure</h1>';
      expect(hasExistingChapterHeading(content, 1, 'The Great Adventure')).toBe(true);
    });

    test('should handle special characters in title', () => {
      const content = '<h1>Chapter 1: The "Special" Adventure (Part 1)</h1>';
      expect(hasExistingChapterHeading(content, 1, 'The "Special" Adventure (Part 1)')).toBe(true);
    });
  });

  describe('New format detection (title only)', () => {
    test('should detect HTML heading with title only', () => {
      const content = '<h1>Introduction</h1><p>Content here</p>';
      expect(hasExistingChapterHeading(content, 1, 'Introduction')).toBe(true);
    });

    test('should detect markdown heading with title only', () => {
      const content = '# Getting Started\n\nContent here';
      expect(hasExistingChapterHeading(content, 2, 'Getting Started')).toBe(true);
    });

    test('should detect plain text title only', () => {
      const content = 'Advanced Topics\n\nThis is the content of the chapter.';
      expect(hasExistingChapterHeading(content, 3, 'Advanced Topics')).toBe(true);
    });

    test('should not detect wrong title', () => {
      const content = '<h1>Different Title</h1>';
      expect(hasExistingChapterHeading(content, 1, 'Introduction')).toBe(false);
    });

    test('should handle special characters in new format', () => {
      const content = '<h1>The "Special" Adventure (Part 1)</h1>';
      expect(hasExistingChapterHeading(content, 1, 'The "Special" Adventure (Part 1)')).toBe(true);
    });
  });

  describe('Edge cases', () => {
    test('should return false for null content', () => {
      expect(hasExistingChapterHeading(null, 1)).toBe(false);
    });

    test('should return false for undefined content', () => {
      expect(hasExistingChapterHeading(undefined, 1)).toBe(false);
    });

    test('should return false for empty string', () => {
      expect(hasExistingChapterHeading('', 1)).toBe(false);
    });

    test('should return false for non-string content', () => {
      expect(hasExistingChapterHeading(123, 1)).toBe(false);
    });

    test('should return false when no chapter heading found', () => {
      const content = '<p>This is just regular content without any chapter headings.</p>';
      expect(hasExistingChapterHeading(content, 1)).toBe(false);
    });

    test('should handle whitespace variations', () => {
      const content = '   <h1>  Chapter   1  :   Introduction  </h1>   ';
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });

    test('should handle multiline content', () => {
      const content = `
        <div>
          <h1>Chapter 1: Introduction</h1>
          <p>This is the first chapter.</p>
        </div>
      `;
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });
  });

  describe('Complex content scenarios', () => {
    test('should detect heading in mixed HTML/text content', () => {
      const content = `
        <div class="intro">
          <p>Welcome to our document</p>
        </div>
        <h2>Chapter 1: Getting Started</h2>
        <p>This chapter will help you begin...</p>
      `;
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });

    test('should not be confused by similar text', () => {
      const content = `
        <p>In the previous chapter 1, we discussed...</p>
        <p>This chapter will cover new topics.</p>
      `;
      expect(hasExistingChapterHeading(content, 1)).toBe(false);
    });

    test('should handle nested HTML elements', () => {
      const content = `
        <div>
          <header>
            <h1><strong>Chapter 1:</strong> <em>Introduction</em></h1>
          </header>
        </div>
      `;
      expect(hasExistingChapterHeading(content, 1)).toBe(true);
    });
  });
});
