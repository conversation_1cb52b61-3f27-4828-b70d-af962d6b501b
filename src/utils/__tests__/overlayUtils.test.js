import {
  formatOverlayDisplayName,
  getOverlayDisplayLabel,
  formatOverlayType,
} from "../overlayUtils";

describe("overlayUtils", () => {
  describe("formatOverlayDisplayName", () => {
    test("should format overlay names by replacing underscores with spaces and capitalizing", () => {
      expect(formatOverlayDisplayName("company_name")).toBe("Company Name");
      expect(formatOverlayDisplayName("email_address")).toBe("Email Address");
      expect(formatOverlayDisplayName("phone_number")).toBe("Phone Number");
      expect(formatOverlayDisplayName("website")).toBe("Website");
      expect(formatOverlayDisplayName("title")).toBe("Title");
      expect(formatOverlayDisplayName("author")).toBe("Author");
    });

    test("should handle empty or null input", () => {
      expect(formatOverlayDisplayName("")).toBe("");
      expect(formatOverlayDisplayName(null)).toBe("");
      expect(formatOverlayDisplayName(undefined)).toBe("");
    });

    test("should handle names with multiple underscores", () => {
      expect(formatOverlayDisplayName("logo_overlay_main")).toBe(
        "Logo Overlay Main"
      );
      expect(formatOverlayDisplayName("user_profile_image")).toBe(
        "User Profile Image"
      );
    });

    test("should handle names already in proper format", () => {
      expect(formatOverlayDisplayName("Title")).toBe("Title");
      expect(formatOverlayDisplayName("Company Name")).toBe("Company Name");
    });
  });

  describe("formatOverlayType", () => {
    test("should format overlay types correctly", () => {
      expect(formatOverlayType("logo")).toBe("Logo");
      expect(formatOverlayType("company")).toBe("Company");
      expect(formatOverlayType("website")).toBe("Website");
      expect(formatOverlayType("email")).toBe("Email");
      expect(formatOverlayType("phone")).toBe("Phone");
      expect(formatOverlayType("text")).toBe("Text");
    });

    test("should default to Text for unknown types", () => {
      expect(formatOverlayType("unknown")).toBe("Text");
      expect(formatOverlayType("")).toBe("Text");
      expect(formatOverlayType(null)).toBe("Text");
      expect(formatOverlayType(undefined)).toBe("Text");
    });
  });

  describe("getOverlayDisplayLabel", () => {
    test("should format overlay labels with logo indicator", () => {
      const logoOverlay = { id: "company_logo", type: "logo" };
      expect(getOverlayDisplayLabel(logoOverlay)).toBe("Company Logo (Logo)");
    });

    test("should format overlay labels without type indicator for non-logo types", () => {
      const textOverlay = { id: "company_name", type: "company" };
      expect(getOverlayDisplayLabel(textOverlay)).toBe("Company Name");

      const emailOverlay = { id: "email_address", type: "email" };
      expect(getOverlayDisplayLabel(emailOverlay)).toBe("Email Address");
    });

    test("should handle empty overlay", () => {
      expect(getOverlayDisplayLabel(null)).toBe("");
      expect(getOverlayDisplayLabel({})).toBe("");
    });
  });
});
