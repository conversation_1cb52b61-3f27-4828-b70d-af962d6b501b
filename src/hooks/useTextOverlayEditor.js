import { useState, useCallback, useEffect, useRef } from "react";
import { prodLogger } from "../utils/prodLogger.js";
import { hashObject } from "../utils/changeDetection.js";

/**
 * Text Overlay Editor Hook
 * Manages state for text overlay customizations with debounced updates,
 * undo functionality, and sessionStorage persistence
 */
const useTextOverlayEditor = (template, documentData, options = {}) => {
  const {
    debounceMs = 100, // Reduced from 300ms to 100ms for better responsiveness
    enablePersistence = false,
    onPreviewUpdate = null,
  } = options;

  // State management
  const [customizations, setCustomizations] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [undoStack, setUndoStack] = useState([]);
  const [isPreviewUpdating, setIsPreviewUpdating] = useState(false);
  const [error, setError] = useState(null);

  // Refs for debouncing
  const debounceTimeoutRef = useRef(null);
  const lastCustomizationsRef = useRef({});

  // Load customizations from localStorage on template change
  useEffect(() => {
    if (!enablePersistence || !template?.id) return;

    const loadCustomizations = () => {
      try {
        // Use hash for more efficient storage key generation
        const documentHash = documentData ? hashObject({ title: documentData.title, author: documentData.author }) : 'default';
        const storageKey = `text-overlay-customizations-${template.id}-${documentHash}`;
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          const saved = JSON.parse(stored);
          setCustomizations(saved);
          lastCustomizationsRef.current = saved;

          prodLogger.debug("📂 Text overlay customizations loaded from localStorage", {
            templateId: template.id,
            customizationCount: Object.keys(saved).length,
          });
        }
      } catch (error) {
        prodLogger.warn("Failed to load text overlay customizations from localStorage:", error);
        setError("Failed to load saved customizations");
      }
    };

    loadCustomizations();
  }, [template?.id, enablePersistence]);

  // Save customizations to localStorage
  const saveCustomizations = useCallback(
    (newCustomizations) => {
      if (!enablePersistence || !template?.id) return;

      try {
        // Use hash for more efficient storage key generation
        const documentHash = documentData ? hashObject({ title: documentData.title, author: documentData.author }) : 'default';
        const storageKey = `text-overlay-customizations-${template.id}-${documentHash}`;
        localStorage.setItem(storageKey, JSON.stringify(newCustomizations));
        prodLogger.debug("💾 Saved text overlay customizations to localStorage", {
          templateId: template.id,
          customizationCount: Object.keys(newCustomizations).length,
        });
      } catch (error) {
        prodLogger.warn("⚠️ Failed to save text overlay customizations to localStorage:", error);
      }
    },
    [enablePersistence, template?.id]
  );
  // Debounced preview update
  const triggerPreviewUpdate = useCallback(
    (newCustomizations) => {
      if (!onPreviewUpdate) return;

      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set new timeout
      debounceTimeoutRef.current = setTimeout(async () => {
        setIsPreviewUpdating(true);
        setError(null);

        try {
          await onPreviewUpdate(template, documentData, newCustomizations);
        } catch (err) {
          prodLogger.error("Preview update failed:", err);
          setError(err.message || "Failed to update preview");
        } finally {
          setIsPreviewUpdating(false);
        }
      }, debounceMs);
    },
    [onPreviewUpdate, template, documentData, debounceMs]
  );

  // Handle customization changes
  const handleCustomizationChange = useCallback(
    (newCustomizations) => {
      // Add to undo stack (limit to 10 entries)
      setUndoStack((prev) => {
        const newStack = [lastCustomizationsRef.current, ...prev].slice(0, 10);
        return newStack;
      });

      // Update state
      setCustomizations(newCustomizations);
      lastCustomizationsRef.current = newCustomizations;

      // Save with sessionStorage
      saveCustomizations(newCustomizations);

      // Trigger preview update
      triggerPreviewUpdate(newCustomizations);
    },
    [saveCustomizations, triggerPreviewUpdate]
  );

  // Undo last change
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;

    const [previousState, ...restStack] = undoStack;
    setUndoStack(restStack);
    setCustomizations(previousState);
    lastCustomizationsRef.current = previousState;

    // Save with hybrid persistence
    saveCustomizations(previousState);

    // Trigger preview update
    triggerPreviewUpdate(previousState);
  }, [undoStack, saveCustomizations, triggerPreviewUpdate]);

  // Reset all customizations
  const handleReset = useCallback(() => {
    // Add current state to undo stack
    setUndoStack((prev) => [customizations, ...prev].slice(0, 10));

    const emptyCustomizations = {};
    setCustomizations(emptyCustomizations);
    lastCustomizationsRef.current = emptyCustomizations;

    // Clear localStorage
    if (enablePersistence && template?.id) {
      const documentHash = documentData ? hashObject({ title: documentData.title, author: documentData.author }) : 'default';
      const storageKey = `text-overlay-customizations-${template.id}-${documentHash}`;
      localStorage.removeItem(storageKey);
    }

    // Trigger preview update
    triggerPreviewUpdate(emptyCustomizations);
  }, [customizations, enablePersistence, template?.id, triggerPreviewUpdate]);

  // Reset specific overlay
  const handleResetOverlay = useCallback(
    (overlayId) => {
      if (!overlayId || !customizations[overlayId]) return;

      // Add current state to undo stack
      setUndoStack((prev) => [customizations, ...prev].slice(0, 10));

      const newCustomizations = { ...customizations };
      delete newCustomizations[overlayId];

      setCustomizations(newCustomizations);
      lastCustomizationsRef.current = newCustomizations;

      // Save with localStorage
      saveCustomizations(newCustomizations);

      // Trigger preview update
      triggerPreviewUpdate(newCustomizations);
    },
    [customizations, saveCustomizations, triggerPreviewUpdate]
  );

  // Delete overlay (hide it from template)
  const handleDeleteOverlay = useCallback(
    (overlayId) => {
      if (!overlayId) return;

      // Add current state to undo stack
      setUndoStack((prev) => [customizations, ...prev].slice(0, 10));

      const newCustomizations = { ...customizations };

      // Set the overlay as hidden instead of removing it completely
      // This allows the overlay to be restored with undo
      newCustomizations[overlayId] = {
        ...newCustomizations[overlayId],
        hidden: true,
      };

      setCustomizations(newCustomizations);
      lastCustomizationsRef.current = newCustomizations;

      // Save with localStorage
      saveCustomizations(newCustomizations);

      // Trigger preview update
      triggerPreviewUpdate(newCustomizations);

      prodLogger.debug("🗑️ Overlay hidden:", { overlayId });
    },
    [customizations, saveCustomizations, triggerPreviewUpdate]
  );

  // Toggle editing mode
  const handleToggleEditing = useCallback(() => {
    setIsEditing((prev) => !prev);
  }, []);

  // Get merged overlay configuration (original + customizations)
  const getMergedOverlay = useCallback(
    (overlayId) => {
      if (!template?.text_overlays?.overlays) return null;

      const originalOverlay = template.text_overlays.overlays.find(
        (overlay) => overlay.id === overlayId
      );

      if (!originalOverlay) return null;

      const overlayCustomizations = customizations[overlayId] || {};

      // If overlay is marked as hidden, return null to exclude it from rendering
      if (overlayCustomizations.hidden) return null;

      return {
        ...originalOverlay,
        styling: {
          ...originalOverlay.styling,
          ...overlayCustomizations.styling,
        },
        position: {
          ...originalOverlay.position,
          ...overlayCustomizations.position,
        },
      };
    },
    [template, customizations]
  );

  // Get all merged overlays
  const getMergedOverlays = useCallback(() => {
    if (!template?.text_overlays?.overlays) return [];

    return template.text_overlays.overlays
      .map((overlay) => getMergedOverlay(overlay.id))
      .filter(Boolean); // This will filter out hidden overlays (null values)
  }, [template, getMergedOverlay]);

  // Check if any customizations exist
  const hasCustomizations = Object.keys(customizations).length > 0;

  // Check if specific overlay has customizations
  const hasOverlayCustomizations = useCallback(
    (overlayId) => {
      return Boolean(customizations[overlayId]);
    },
    [customizations]
  );

  // Get customization summary
  const getCustomizationSummary = useCallback(() => {
    const overlayIds = Object.keys(customizations);
    const totalCustomizations = overlayIds.reduce((count, overlayId) => {
      const overlay = customizations[overlayId];
      return (
        count +
        Object.keys(overlay.styling || {}).length +
        Object.keys(overlay.position || {}).length
      );
    }, 0);

    return {
      overlayCount: overlayIds.length,
      totalCustomizations,
      overlayIds,
    };
  }, [customizations]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    customizations,
    isEditing,
    isPreviewUpdating,
    error,
    hasCustomizations,
    undoStack,

    // Actions
    handleCustomizationChange,
    handleToggleEditing,
    handleUndo,
    handleReset,
    handleResetOverlay,
    handleDeleteOverlay,

    // Getters
    getMergedOverlay,
    getMergedOverlays,
    hasOverlayCustomizations,
    getCustomizationSummary,

    // Utils
    canUndo: undoStack.length > 0,
  };
};

export default useTextOverlayEditor;
