import { useState, useCallback, useEffect, useRef } from "react";
import {
  templateHasLogoOverlays,
  getLogoOverlays,
  applyLogoSizeConstraints,
} from "../services/logoOverlayService.js";
import { prodLogger } from "../utils/prodLogger.js";

/**
 * Logo Overlay Editor Hook
 * Manages state for logo overlay customizations with sessionStorage persistence
 * Handles simplified logo operations: replace/change and delete/remove only
 */
const useLogoOverlayEditor = (template, documentData, options = {}) => {
  const {
    debounceMs = 300,
    enablePersistence = false,
    onPreviewUpdate = null,
  } = options;

  // State management
  const [logoCustomizations, setLogoCustomizations] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [isPreviewUpdating, setIsPreviewUpdating] = useState(false);
  const [error, setError] = useState(null);
  const [undoStack, setUndoStack] = useState([]);

  // Refs for debouncing
  const debounceTimeoutRef = useRef(null);
  const templateIdRef = useRef(null);

  // Check if template supports logo overlays
  const hasLogoOverlays = templateHasLogoOverlays(template);
  const logoOverlays = hasLogoOverlays ? getLogoOverlays(template) : [];

  // Load customizations from localStorage on template change
  useEffect(() => {
    if (!enablePersistence || !template?.id) return;

    const loadCustomizations = () => {
      try {
        const storageKey = `logo-overlay-customizations-${template.id}-${
          documentData
            ? btoa(
                JSON.stringify({
                  title: documentData.title,
                  author: documentData.author,
                })
              ).substring(0, 8)
            : "default"
        }`;
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          const saved = JSON.parse(stored);
          setLogoCustomizations(saved);
          prodLogger.debug(
            "📂 Logo overlay customizations loaded from localStorage",
            {
              templateId: template.id,
              customizationCount: Object.keys(saved).length,
            }
          );
        }
      } catch (error) {
        prodLogger.warn(
          "Failed to load logo overlay customizations from localStorage:",
          error
        );
        setError("Failed to load saved customizations");
      }
    };

    loadCustomizations();
  }, [template?.id, enablePersistence]);

  // Save customizations to localStorage
  const saveCustomizations = useCallback(
    (newCustomizations) => {
      if (!enablePersistence || !template?.id) return;

      try {
        const storageKey = `logo-overlay-customizations-${template.id}-${
          documentData
            ? btoa(
                JSON.stringify({
                  title: documentData.title,
                  author: documentData.author,
                })
              ).substring(0, 8)
            : "default"
        }`;
        localStorage.setItem(storageKey, JSON.stringify(newCustomizations));
        prodLogger.debug(
          "💾 Saved logo overlay customizations to localStorage",
          {
            templateId: template.id,
            customizationCount: Object.keys(newCustomizations).length,
          }
        );
      } catch (error) {
        prodLogger.warn(
          "⚠️ Failed to save logo overlay customizations to localStorage:",
          error
        );
      }
    },
    [enablePersistence, template?.id]
  );

  // Debounced preview update
  const debouncedPreviewUpdate = useCallback(
    (customizations) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      setIsPreviewUpdating(true);

      debounceTimeoutRef.current = setTimeout(() => {
        try {
          // Pass template, documentData, and customizations in the correct order
          onPreviewUpdate?.(template, documentData, customizations);
          setError(null);
        } catch (err) {
          setError("Failed to update preview");
          prodLogger.error("❌ Error updating logo preview:", err);
        } finally {
          setIsPreviewUpdating(false);
        }
      }, debounceMs);
    },
    [debounceMs, onPreviewUpdate, template, documentData]
  );

  // Handle logo change (replace/upload new logo)
  const handleLogoChange = useCallback(
    (overlayId, logoData) => {
      if (!hasLogoOverlays) return;

      try {
        setError(null);

        // Add current state to undo stack
        setUndoStack((prev) => [...prev, logoCustomizations]);

        const newCustomizations = {
          ...logoCustomizations,
          [overlayId]: {
            ...logoCustomizations[overlayId],
            selectedLogoId: logoData.id,
            logoData: logoData,
          },
        };

        setLogoCustomizations(newCustomizations);
        saveCustomizations(newCustomizations);
        debouncedPreviewUpdate(newCustomizations);

        prodLogger.debug("🎨 Logo changed for overlay", {
          overlayId,
          logoId: logoData.id,
          logoName: logoData.name,
        });
      } catch (error) {
        setError("Failed to change logo");
        prodLogger.error("❌ Error changing logo:", error);
      }
    },
    [
      hasLogoOverlays,
      logoCustomizations,
      saveCustomizations,
      debouncedPreviewUpdate,
    ]
  );

  // Handle logo deletion (remove logo from overlay)
  const handleLogoDelete = useCallback(
    (overlayId) => {
      if (!hasLogoOverlays) return;

      try {
        setError(null);

        // Add current state to undo stack
        setUndoStack((prev) => [...prev, logoCustomizations]);

        const newCustomizations = {
          ...logoCustomizations,
          [overlayId]: {
            ...logoCustomizations[overlayId],
            selectedLogoId: null,
            logoData: null,
          },
        };

        setLogoCustomizations(newCustomizations);
        saveCustomizations(newCustomizations);
        debouncedPreviewUpdate(newCustomizations);

        prodLogger.debug("🗑️ Logo deleted from overlay", { overlayId });
      } catch (error) {
        setError("Failed to delete logo");
        prodLogger.error("❌ Error deleting logo:", error);
      }
    },
    [
      hasLogoOverlays,
      logoCustomizations,
      saveCustomizations,
      debouncedPreviewUpdate,
    ]
  );

  // Handle logo resize (change width/height)
  const handleLogoResize = useCallback(
    (overlayId, sizeChanges) => {
      if (!hasLogoOverlays) return;

      try {
        setError(null);

        // Add current state to undo stack
        setUndoStack((prev) => [...prev, logoCustomizations]);

        const currentCustomization = logoCustomizations[overlayId] || {};
        const overlay = logoOverlays.find((o) => o.id === overlayId);
        const currentPosition =
          currentCustomization.position || overlay?.position || {};

        // Apply size constraints from template if they exist
        const constraints = overlay?.constraints || {};
        const constrainedSize = applyLogoSizeConstraints(
          sizeChanges,
          constraints
        );

        const newPosition = {
          ...currentPosition,
          ...constrainedSize,
        };

        const newCustomizations = {
          ...logoCustomizations,
          [overlayId]: {
            ...currentCustomization,
            position: newPosition,
          },
        };

        setLogoCustomizations(newCustomizations);
        saveCustomizations(newCustomizations);
        debouncedPreviewUpdate(newCustomizations);

        prodLogger.debug("📏 Logo resized for overlay", {
          overlayId,
          newSize: constrainedSize,
          constraints: constraints,
        });
      } catch (error) {
        setError("Failed to resize logo");
        prodLogger.error("❌ Error resizing logo:", error);
      }
    },
    [
      hasLogoOverlays,
      logoCustomizations,
      logoOverlays,
      saveCustomizations,
      debouncedPreviewUpdate,
    ]
  );

  // Handle aspect ratio toggle
  const handleAspectRatioToggle = useCallback(
    (overlayId, maintainAspectRatio) => {
      if (!hasLogoOverlays) return;

      try {
        setError(null);

        // Add current state to undo stack
        setUndoStack((prev) => [...prev, logoCustomizations]);

        const newCustomizations = {
          ...logoCustomizations,
          [overlayId]: {
            ...logoCustomizations[overlayId],
            maintainAspectRatio: maintainAspectRatio,
          },
        };

        setLogoCustomizations(newCustomizations);
        saveCustomizations(newCustomizations);

        prodLogger.debug("🔒 Aspect ratio toggle for overlay", {
          overlayId,
          maintainAspectRatio,
        });
      } catch (error) {
        setError("Failed to toggle aspect ratio");
        prodLogger.error("❌ Error toggling aspect ratio:", error);
      }
    },
    [hasLogoOverlays, logoCustomizations, saveCustomizations]
  );

  // Handle undo
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;

    try {
      const previousState = undoStack[undoStack.length - 1];
      const newUndoStack = undoStack.slice(0, -1);

      setLogoCustomizations(previousState);
      setUndoStack(newUndoStack);
      saveCustomizations(previousState);
      debouncedPreviewUpdate(previousState);

      prodLogger.debug("↶ Logo customizations undone");
    } catch (error) {
      setError("Failed to undo changes");
      prodLogger.error("❌ Error undoing logo changes:", error);
    }
  }, [undoStack, saveCustomizations, debouncedPreviewUpdate]);

  // Handle reset (clear all customizations)
  const handleReset = useCallback(() => {
    try {
      setError(null);

      // Add current state to undo stack
      setUndoStack((prev) => [...prev, logoCustomizations]);

      const emptyCustomizations = {};
      setLogoCustomizations(emptyCustomizations);

      // Clear localStorage
      if (enablePersistence && template?.id) {
        const storageKey = `logo-overlay-customizations-${template.id}-${
          documentData
            ? btoa(
                JSON.stringify({
                  title: documentData.title,
                  author: documentData.author,
                })
              ).substring(0, 8)
            : "default"
        }`;
        localStorage.removeItem(storageKey);
      }

      debouncedPreviewUpdate(emptyCustomizations);

      prodLogger.debug("🔄 Logo customizations reset");
    } catch (error) {
      setError("Failed to reset customizations");
      prodLogger.error("❌ Error resetting logo customizations:", error);
    }
  }, [
    logoCustomizations,
    enablePersistence,
    template?.id,
    debouncedPreviewUpdate,
  ]);

  // Handle reset for specific overlay
  const handleResetOverlay = useCallback(
    (overlayId) => {
      if (!hasLogoOverlays) return;

      try {
        setError(null);

        // Add current state to undo stack
        setUndoStack((prev) => [...prev, logoCustomizations]);

        const newCustomizations = {
          ...logoCustomizations,
        };
        delete newCustomizations[overlayId];

        setLogoCustomizations(newCustomizations);
        saveCustomizations(newCustomizations);
        debouncedPreviewUpdate(newCustomizations);

        prodLogger.debug("🔄 Logo overlay reset", { overlayId });
      } catch (error) {
        setError("Failed to reset overlay");
        prodLogger.error("❌ Error resetting logo overlay:", error);
      }
    },
    [
      hasLogoOverlays,
      logoCustomizations,
      saveCustomizations,
      debouncedPreviewUpdate,
    ]
  );

  // Check if overlay has customizations
  const hasOverlayCustomizations = useCallback(
    (overlayId) => {
      return (
        logoCustomizations[overlayId] &&
        (logoCustomizations[overlayId].selectedLogoId ||
          logoCustomizations[overlayId].logoData)
      );
    },
    [logoCustomizations]
  );

  // Check if any customizations exist
  const hasCustomizations = Object.keys(logoCustomizations).some((overlayId) =>
    hasOverlayCustomizations(overlayId)
  );

  // Get customization summary
  const getCustomizationSummary = useCallback(() => {
    const overlayCount = Object.keys(logoCustomizations).length;
    const logoCount = Object.values(logoCustomizations).filter(
      (c) => c.selectedLogoId
    ).length;

    return {
      totalOverlays: logoOverlays.length,
      customizedOverlays: overlayCount,
      logosSelected: logoCount,
      hasChanges: hasCustomizations,
    };
  }, [logoCustomizations, logoOverlays.length, hasCustomizations]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    logoCustomizations,
    isEditing,
    isPreviewUpdating,
    error,
    hasCustomizations,
    hasLogoOverlays,
    logoOverlays,
    undoStack,

    // Actions
    handleLogoChange,
    handleLogoDelete,
    handleLogoResize,
    handleAspectRatioToggle,
    handleUndo,
    handleReset,
    handleResetOverlay,

    // Getters
    hasOverlayCustomizations,
    getCustomizationSummary,

    // Utils
    canUndo: undoStack.length > 0,
  };
};

export default useLogoOverlayEditor;
