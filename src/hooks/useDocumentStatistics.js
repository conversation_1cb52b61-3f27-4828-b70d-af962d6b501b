import { useState, useEffect, useCallback, useRef } from "react";
import { calculateRealTimeStats } from "../services/documentStatisticsService";
import { prodLogger } from "../utils/prodLogger.js";

/**
 * Custom hook for real-time document statistics
 * @param {Object} editorInstance - Editor instance or content
 * @param {Object} generatedContent - Generated content structure
 * @param {Object} options - Configuration options
 * @returns {Object} Statistics and update functions
 */
export const useDocumentStatistics = (
  editorInstance = null,
  generatedContent = null,
  options = {}
) => {
  const [stats, setStats] = useState({
    words: 0,
    characters: 0,
    chapters: 0,
    sections: 0,
    pages: 1,
    readTime: 0,
    source: "blank",
  });

  const [isCalculating, setIsCalculating] = useState(false);
  const debounceTimeoutRef = useRef(null);
  const lastCalculationRef = useRef(0);

  const {
    debounceMs = 500,
    minCalculationInterval = 1000,
    enableRealTime = true,
  } = options;

  /**
   * Calculate statistics from current content
   */
  const calculateStats = useCallback(async () => {
    try {
      setIsCalculating(true);

      let newStats = { ...stats };

      // Priority 1: Use editor content if available
      if (editorInstance && enableRealTime) {
        let editorContent = "";

        if (typeof editorInstance.getHTML === "function") {
          editorContent = editorInstance.getHTML();
        } else if (typeof editorInstance === "string") {
          editorContent = editorInstance;
        } else if (editorInstance.innerHTML) {
          editorContent = editorInstance.innerHTML;
        }

        if (editorContent && editorContent.trim()) {
          newStats = calculateRealTimeStats(editorContent);
          prodLogger.debug(
            "📊 Real-time stats calculated from editor",
            newStats
          );
        }
      }

      // Priority 2: Use generated content
      else if (generatedContent) {
        // Import the comprehensive stats service
        const { calculateComprehensiveStatistics } = await import(
          "../services/documentStatisticsService"
        );
        newStats = calculateComprehensiveStatistics(generatedContent);
        prodLogger.debug(
          "📊 Stats calculated from generated content",
          newStats
        );
      }

      setStats(newStats);
      lastCalculationRef.current = Date.now();
    } catch (error) {
      prodLogger.error("Error calculating document statistics:", error);
    } finally {
      setIsCalculating(false);
    }
  }, [editorInstance, generatedContent, enableRealTime, stats]);

  /**
   * Debounced statistics calculation
   */
  const calculateStatsDebounced = useCallback(() => {
    // Prevent too frequent calculations
    const now = Date.now();
    if (now - lastCalculationRef.current < minCalculationInterval) {
      return;
    }

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      calculateStats();
    }, debounceMs);
  }, [calculateStats, debounceMs, minCalculationInterval]);

  /**
   * Force immediate statistics calculation
   */
  const forceCalculate = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    calculateStats();
  }, [calculateStats]);

  /**
   * Update statistics when content changes
   */
  useEffect(() => {
    if (enableRealTime && (editorInstance || generatedContent)) {
      calculateStatsDebounced();
    }
  }, [
    editorInstance,
    generatedContent,
    enableRealTime,
    calculateStatsDebounced,
  ]);

  /**
   * Set up editor event listeners for real-time updates
   */
  useEffect(() => {
    if (!editorInstance || !enableRealTime) return;

    let cleanup = () => {};

    try {
      // For TipTap editor instances
      if (editorInstance.on && typeof editorInstance.on === "function") {
        const updateHandler = () => calculateStatsDebounced();

        editorInstance.on("update", updateHandler);
        editorInstance.on("transaction", updateHandler);

        cleanup = () => {
          editorInstance.off("update", updateHandler);
          editorInstance.off("transaction", updateHandler);
        };
      }
      // For DOM elements
      else if (editorInstance.addEventListener) {
        const inputHandler = () => calculateStatsDebounced();

        editorInstance.addEventListener("input", inputHandler);
        editorInstance.addEventListener("keyup", inputHandler);
        editorInstance.addEventListener("paste", inputHandler);

        cleanup = () => {
          editorInstance.removeEventListener("input", inputHandler);
          editorInstance.removeEventListener("keyup", inputHandler);
          editorInstance.removeEventListener("paste", inputHandler);
        };
      }
    } catch (error) {
      prodLogger.warn("Could not set up editor event listeners:", error);
    }

    return cleanup;
  }, [editorInstance, enableRealTime, calculateStatsDebounced]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    stats,
    isCalculating,
    calculateStats: forceCalculate,
    updateStats: calculateStatsDebounced,
  };
};

export default useDocumentStatistics;
