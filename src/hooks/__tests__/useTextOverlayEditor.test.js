import { renderHook, act } from '@testing-library/react';
import useTextOverlayEditor from '../useTextOverlayEditor';

// Mock sessionStorage
const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('useTextOverlayEditor', () => {
  const mockTemplate = {
    id: 'test-template',
    text_overlays: {
      overlays: [
        {
          id: 'title',
          placeholder: '{{title}}',
          position: { x: 50, y: 100, width: 400, height: 60 },
          styling: {
            fontSize: 32,
            fontFamily: 'Arial',
            fontWeight: 'bold',
            color: '#000000',
            textAlign: 'center'
          }
        },
        {
          id: 'author',
          placeholder: 'by {{author}}',
          position: { x: 50, y: 200, width: 400, height: 30 },
          styling: {
            fontSize: 18,
            fontFamily: 'Arial',
            fontWeight: 'normal',
            color: '#666666',
            textAlign: 'center'
          }
        }
      ]
    }
  };

  const mockDocumentData = {
    title: 'Test Document',
    author: 'Test Author'
  };

  const mockOnPreviewUpdate = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockSessionStorage.getItem.mockReturnValue(null);
  });

  test('initializes with empty customizations', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    expect(result.current.customizations).toEqual({});
    expect(result.current.hasCustomizations).toBe(false);
    expect(result.current.isEditing).toBe(false);
    expect(result.current.canUndo).toBe(false);
  });

  test('handles customization changes', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const newCustomizations = {
      title: {
        styling: {
          fontSize: 48,
          color: '#FF0000'
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(newCustomizations);
    });

    expect(result.current.customizations).toEqual(newCustomizations);
    expect(result.current.hasCustomizations).toBe(true);
  });

  test('saves customizations to session storage', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        enablePersistence: true,
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const newCustomizations = {
      title: {
        styling: {
          fontSize: 48
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(newCustomizations);
    });

    expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
      'text-overlay-customizations-test-template',
      JSON.stringify(newCustomizations)
    );
  });

  test('loads customizations from session storage', () => {
    const storedCustomizations = {
      title: {
        styling: {
          fontSize: 40,
          color: '#0000FF'
        }
      }
    };

    mockSessionStorage.getItem.mockReturnValue(JSON.stringify(storedCustomizations));

    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        enablePersistence: true,
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    expect(result.current.customizations).toEqual(storedCustomizations);
    expect(result.current.hasCustomizations).toBe(true);
  });

  test('handles undo functionality', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const firstCustomizations = {
      title: {
        styling: {
          fontSize: 36
        }
      }
    };

    const secondCustomizations = {
      title: {
        styling: {
          fontSize: 48,
          color: '#FF0000'
        }
      }
    };

    // Make first change
    act(() => {
      result.current.handleCustomizationChange(firstCustomizations);
    });

    // Make second change
    act(() => {
      result.current.handleCustomizationChange(secondCustomizations);
    });

    expect(result.current.canUndo).toBe(true);

    // Undo
    act(() => {
      result.current.handleUndo();
    });

    expect(result.current.customizations).toEqual(firstCustomizations);
  });

  test('resets all customizations', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48
        }
      }
    };

    // Add customizations
    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    expect(result.current.hasCustomizations).toBe(true);

    // Reset
    act(() => {
      result.current.handleReset();
    });

    expect(result.current.customizations).toEqual({});
    expect(result.current.hasCustomizations).toBe(false);
  });

  test('resets specific overlay', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48
        }
      },
      author: {
        styling: {
          color: '#FF0000'
        }
      }
    };

    // Add customizations
    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    // Reset only title
    act(() => {
      result.current.handleResetOverlay('title');
    });

    expect(result.current.customizations).toEqual({
      author: {
        styling: {
          color: '#FF0000'
        }
      }
    });
  });

  test('gets merged overlay configuration', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48,
          color: '#FF0000'
        },
        position: {
          x: 100
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    const mergedOverlay = result.current.getMergedOverlay('title');

    expect(mergedOverlay).toEqual({
      id: 'title',
      placeholder: '{{title}}',
      position: { x: 100, y: 100, width: 400, height: 60 }, // x customized, others original
      styling: {
        fontSize: 48, // customized
        fontFamily: 'Arial', // original
        fontWeight: 'bold', // original
        color: '#FF0000', // customized
        textAlign: 'center' // original
      }
    });
  });

  test('checks if overlay has customizations', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    expect(result.current.hasOverlayCustomizations('title')).toBe(true);
    expect(result.current.hasOverlayCustomizations('author')).toBe(false);
  });

  test('gets customization summary', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48,
          color: '#FF0000'
        },
        position: {
          x: 100
        }
      },
      author: {
        styling: {
          color: '#0000FF'
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    const summary = result.current.getCustomizationSummary();

    expect(summary).toEqual({
      overlayCount: 2,
      totalCustomizations: 4, // 2 styling + 1 position for title, 1 styling for author
      overlayIds: ['title', 'author']
    });
  });

  test('toggles editing mode', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    expect(result.current.isEditing).toBe(false);

    act(() => {
      result.current.handleToggleEditing();
    });

    expect(result.current.isEditing).toBe(true);

    act(() => {
      result.current.handleToggleEditing();
    });

    expect(result.current.isEditing).toBe(false);
  });

  test('calls preview update with debouncing', async () => {
    jest.useFakeTimers();

    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        onPreviewUpdate: mockOnPreviewUpdate,
        debounceMs: 300
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    // Preview update should not be called immediately
    expect(mockOnPreviewUpdate).not.toHaveBeenCalled();

    // Fast forward time
    act(() => {
      jest.advanceTimersByTime(300);
    });

    // Now preview update should be called
    expect(mockOnPreviewUpdate).toHaveBeenCalledWith(
      mockTemplate,
      mockDocumentData,
      customizations
    );

    jest.useRealTimers();
  });

  test('handles session storage errors gracefully', () => {
    mockSessionStorage.getItem.mockImplementation(() => {
      throw new Error('Storage error');
    });

    // Should not throw error
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        enablePersistence: true,
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    expect(result.current.customizations).toEqual({});
  });

  test('disables session storage when option is false', () => {
    const { result } = renderHook(() =>
      useTextOverlayEditor(mockTemplate, mockDocumentData, {
        enablePersistence: false,
        onPreviewUpdate: mockOnPreviewUpdate
      })
    );

    const customizations = {
      title: {
        styling: {
          fontSize: 48
        }
      }
    };

    act(() => {
      result.current.handleCustomizationChange(customizations);
    });

    expect(mockSessionStorage.setItem).not.toHaveBeenCalled();
  });
});
