import React, { useState, useRef, useEffect, useCallback } from 'react';
import imageOverlayService from '../../services/imageOverlayService.js';
import { formatOverlayDisplayName } from '../../utils/overlayUtils';
import { detectTextOnlyChange, createChangeSignature } from '../../utils/changeDetection.js';

import { prodLogger } from '../../utils/prodLogger.js';
// Logo overlay functionality is now integrated into the unified overlay system
/**
 * Interactive Template Canvas Component
 * Provides Canva-style text editing with click-to-select and drag-to-move functionality
 * for user-facing template previews
 */
const InteractiveTemplateCanvas = ({
  template,
  documentData,
  customizations = {},
  logoCustomizations = {},
  onCustomizationChange,
  onLogoCustomizationChange,
  onTextSelect,
  onLogoSelect,
  selectedOverlayId,
  selectedLogoOverlayId,
  isInteractive = false,
  className = '',
  isLoading = false
}) => {
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [internalSelectedOverlayId, setInternalSelectedOverlayId] = useState(null);
  const [internalSelectedLogoOverlayId, setInternalSelectedLogoOverlayId] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [renderedCanvas, setRenderedCanvas] = useState(null);
  const [isRendering, setIsRendering] = useState(false);
  const [hoveredOverlayId, setHoveredOverlayId] = useState(null);
  const [hoveredLogoOverlayId, setHoveredLogoOverlayId] = useState(null);

  // Performance optimization: track if we need template re-render vs overlay redraw
  const lastRenderSignatureRef = useRef('');
  const lastTemplateIdRef = useRef(null); // Track template ID for cache clearing
  const lastCustomizationsRef = useRef({});
  const backgroundCanvasRef = useRef(null); // Cache for background + cleaned areas
  const offscreenCanvasRef = useRef(null); // Off-screen canvas for double-buffering

  // Use external selection if provided, otherwise use internal state
  const currentSelectedOverlayId = selectedOverlayId || internalSelectedOverlayId;
  const setCurrentSelectedOverlayId = selectedOverlayId !== null ?
    (id) => onTextSelect?.(id) : setInternalSelectedOverlayId;

  const currentSelectedLogoOverlayId = selectedLogoOverlayId || internalSelectedLogoOverlayId;
  const setCurrentSelectedLogoOverlayId = selectedLogoOverlayId !== null ?
    (id) => onLogoSelect?.(id) : setInternalSelectedLogoOverlayId;

  // Get overlays from template (unified approach)
  const allOverlays = template?.text_overlays?.overlays || [];
  const overlays = allOverlays.filter(overlay => overlay.type !== 'logo');
  const logoOverlays = allOverlays.filter(overlay => overlay.type === 'logo');

  // Get template dimensions
  const templateWidth = template?.background_image_width || 1200;
  const templateHeight = template?.background_image_height || 1600;

  // Render template to canvas - optimized for text-only changes
  const renderTemplate = useCallback(async () => {
    if (!template) return;

    // Create efficient change signature
    const currentSignature = createChangeSignature(
      template,
      documentData,
      customizations,
      logoCustomizations,
      templateWidth,
      templateHeight
    );

    // Skip re-render if content hasn't actually changed
    if (lastRenderSignatureRef.current === currentSignature) {
      prodLogger.debug('🚀 Skipping template re-render - content unchanged');
      return;
    }

    // Check if only text content changed for optimization
    const isTextOnlyChange = detectTextOnlyChange(lastCustomizationsRef.current, customizations);

    prodLogger.debug('🔍 RENDER PATH ANALYSIS:', {
      templateId: template.id,
      isTextOnlyChange,
      hasBackgroundCache: !!backgroundCanvasRef.current,
      prevCustomizations: lastCustomizationsRef.current,
      currentCustomizations: customizations,
      customizationCount: Object.keys(customizations).length
    });

    if (isTextOnlyChange && backgroundCanvasRef.current) {
      prodLogger.debug('⚡ FAST PATH: Text-only change with background cache');

      // Show optimistic update immediately, then render in background
      showOptimisticTextUpdate();
      await renderTextOnlyChanges();
      return;
    } else if (isTextOnlyChange && !backgroundCanvasRef.current) {
      prodLogger.debug('⚡ FAST PATH: Text-only change but no background cache - using optimistic update only');

      // Show optimistic update immediately for first text change
      // Full render will happen but user sees immediate feedback
      showOptimisticTextUpdate();
      // Continue to full render to create background cache
    } else {
      prodLogger.debug('🐌 FULL RENDER: Reason analysis', {
        isTextOnlyChange,
        hasBackgroundCache: !!backgroundCanvasRef.current,
        reason: !isTextOnlyChange
          ? 'Not text-only change (styling/position changed)'
          : 'No background cache (first render or cache cleared)'
      });
    }

    // Clear template cache when switching templates to prevent text-over-text issues
    if (lastTemplateIdRef.current && lastTemplateIdRef.current !== template.id) {
      prodLogger.debug('🔄 Template switched - clearing cache to prevent conflicts');
      imageOverlayService.clearTemplateCache(lastTemplateIdRef.current, template.background_image_url);
    }

    // Full render path
    lastRenderSignatureRef.current = currentSignature;
    lastTemplateIdRef.current = template.id;
    lastCustomizationsRef.current = customizations;
    setIsRendering(true);

    try {
      // Merge text customizations and logo customizations for rendering
      const mergedCustomizations = {
        ...customizations,
        ...logoCustomizations
      };

      // CRITICAL DEBUG: Log what customizations we're passing to the renderer
      prodLogger.debug('📝 INTERACTIVE CANVAS CUSTOMIZATIONS DEBUG:', {
        templateId: template.id,
        templateName: template.name,
        textCustomizations: customizations,
        logoCustomizations: logoCustomizations,
        mergedCustomizations,
        totalOverlays: allOverlays.length,
        textOverlays: overlays.length,
        logoOverlays: logoOverlays.length,
        localStorage_keys: Object.keys(localStorage).filter(k => k.includes(template.id)),
        localStorage_text_key: `text-overlay-customizations-${template.id}`,
        localStorage_text_data: localStorage.getItem(`text-overlay-customizations-${template.id}`),
      });

      // Log canvas rendering for consistency monitoring
      prodLogger.debug("Canvas: Rendering template", {
        templateId: template.id,
        hasProfileData: !!(documentData.full_name || documentData.email)
      });

      // Use enhanced rendering with background cleaning for better text replacement
      const canvas = await imageOverlayService.renderTemplateWithCustomizations(
        template,
        documentData,
        mergedCustomizations,
        { enableBackgroundCleaning: true } // Enable intelligent background cleaning
      );

      // Cache background canvas for fast text-only updates
      if (canvas) {
        // CRITICAL FIX: Properly clone canvas with its content
        const backgroundCanvas = document.createElement('canvas');
        backgroundCanvas.width = canvas.width;
        backgroundCanvas.height = canvas.height;
        const backgroundCtx = backgroundCanvas.getContext('2d');
        backgroundCtx.drawImage(canvas, 0, 0);
        backgroundCanvasRef.current = backgroundCanvas;

        prodLogger.debug('📦 Background canvas cached', {
          width: backgroundCanvas.width,
          height: backgroundCanvas.height
        });
      }

      // Seamlessly update display canvas (no blank flash)
      await updateDisplayCanvasSeamlessly(canvas);

      // CRITICAL: Only update renderedCanvas AFTER seamless update completes
      // This prevents the drawInteractiveOverlay useEffect from interfering
      // Also prevent unnecessary re-renders by checking if canvas actually changed
      setRenderedCanvas(prevCanvas => {
        // Only update if canvas is actually different to prevent unnecessary re-renders
        if (prevCanvas !== canvas) {
          prodLogger.debug('📱 Updating renderedCanvas state');
          return canvas;
        }
        return prevCanvas;
      });

    } catch (error) {
      prodLogger.error('Error rendering template:', error);
    } finally {
      setIsRendering(false);
    }
  }, [template, documentData, customizations, logoCustomizations, templateWidth, templateHeight]);

  // Seamless canvas update method (prevents blank flash)
  const updateDisplayCanvasSeamlessly = useCallback(async (newCanvas) => {
    if (!canvasRef.current || !newCanvas) return;

    const displayCanvas = canvasRef.current;
    const displayCtx = displayCanvas.getContext('2d');

    // Ensure canvas dimensions are correct
    if (displayCanvas.width !== templateWidth || displayCanvas.height !== templateHeight) {
      displayCanvas.width = templateWidth;
      displayCanvas.height = templateHeight;
    }

    // Use requestAnimationFrame for smooth visual updates
    return new Promise((resolve) => {
      requestAnimationFrame(() => {
        // Only clear and draw when new content is ready
        displayCtx.clearRect(0, 0, templateWidth, templateHeight);
        displayCtx.drawImage(newCanvas, 0, 0);
        resolve();
      });
    });
  }, [templateWidth, templateHeight]);

  // Optimistic UI update - show immediate text changes
  const showOptimisticTextUpdate = useCallback(() => {
    if (!canvasRef.current || !backgroundCanvasRef.current) return;

    try {
      const displayCanvas = canvasRef.current;
      const displayCtx = displayCanvas.getContext('2d');

      // Start with cached background
      displayCtx.clearRect(0, 0, templateWidth, templateHeight);
      displayCtx.drawImage(backgroundCanvasRef.current, 0, 0);

      // Render text overlays with current customizations immediately
      // This provides instant visual feedback while proper rendering happens in background
      const mergedCustomizations = { ...customizations, ...logoCustomizations };

      // Simple immediate text rendering (without full styling pipeline)
      if (template?.text_overlays?.overlays) {
        template.text_overlays.overlays.forEach(overlay => {
          if (overlay.type === 'logo') return;

          const overlayCustomizations = mergedCustomizations[overlay.id] || {};
          if (overlayCustomizations.hidden) return;

          // Get text content - use custom content or populate placeholder
          let text;
          if (overlayCustomizations.content !== undefined) {
            text = overlayCustomizations.content;
          } else {
            // Populate placeholder with document data for accurate preview
            try {
              text = imageOverlayService.populatePlaceholder(
                overlay.placeholder || '',
                documentData,
                {}
              );
            } catch (error) {
              text = overlay.placeholder || '';
            }
          }

          if (!text.trim()) return;

          // Apply basic styling for immediate feedback
          const position = overlayCustomizations.position || overlay.position;
          const styling = { ...overlay.styling, ...overlayCustomizations.styling };

          displayCtx.font = `${styling.fontSize || 16}px ${styling.fontFamily || 'Arial'}`;
          displayCtx.fillStyle = styling.color || '#000000';
          displayCtx.textAlign = styling.textAlign || 'left';

          // Simple text rendering for immediate feedback
          displayCtx.fillText(text, position.x, position.y + (styling.fontSize || 16));
        });
      }

      prodLogger.debug('⚡ Optimistic text update completed', {
        overlaysProcessed: template?.text_overlays?.overlays?.length || 0,
        customizationCount: Object.keys(mergedCustomizations).length
      });
    } catch (error) {
      prodLogger.error('Error in optimistic text update:', error);
      // Fail silently - proper render will happen anyway
    }
  }, [template, customizations, logoCustomizations, templateWidth, templateHeight]);

  // Fast text-only rendering function with seamless updates
  const renderTextOnlyChanges = useCallback(async () => {
    if (!backgroundCanvasRef.current || !canvasRef.current || !template) return;

    try {
      // Don't set isRendering for fast text-only updates to prevent loading flashes

      // Create off-screen canvas for seamless rendering
      if (!offscreenCanvasRef.current) {
        offscreenCanvasRef.current = document.createElement('canvas');
      }

      const offscreenCanvas = offscreenCanvasRef.current;
      offscreenCanvas.width = templateWidth;
      offscreenCanvas.height = templateHeight;
      const offscreenCtx = offscreenCanvas.getContext('2d');

      // Start with cached background on off-screen canvas
      offscreenCtx.clearRect(0, 0, templateWidth, templateHeight);
      offscreenCtx.drawImage(backgroundCanvasRef.current, 0, 0);

      // Only re-render text overlays with new content on off-screen canvas
      const mergedCustomizations = {
        ...customizations,
        ...logoCustomizations
      };

      // Use imageOverlayService to render only text overlays on off-screen canvas
      await imageOverlayService.renderTextOverlaysOnly(
        template,
        documentData,
        mergedCustomizations,
        offscreenCanvas
      );

      // Seamlessly update display canvas with completed render
      await updateDisplayCanvasSeamlessly(offscreenCanvas);

      // Update signature and customizations cache
      lastRenderSignatureRef.current = createChangeSignature(
        template,
        documentData,
        customizations,
        logoCustomizations,
        templateWidth,
        templateHeight
      );
      lastCustomizationsRef.current = customizations;

      prodLogger.debug('⚡ Fast text-only render completed');
    } catch (error) {
      prodLogger.error('Error in fast text-only render, falling back to full render:', error);
      // Fallback to full render
      await renderTemplate();
    } finally {
      // No setIsRendering(false) needed since we didn't set it to true for fast updates
    }
  }, [template, documentData, customizations, logoCustomizations, templateWidth, templateHeight, renderTemplate, updateDisplayCanvasSeamlessly]);

  // Render template only when content actually changes (not selection)
  useEffect(() => {
    renderTemplate();
  }, [renderTemplate]);

  // Draw interactive overlay separately from template rendering
  useEffect(() => {
    drawInteractiveOverlay();
  }, [renderedCanvas, currentSelectedOverlayId, currentSelectedLogoOverlayId, hoveredOverlayId, hoveredLogoOverlayId]);

  const drawInteractiveOverlay = () => {
    const canvas = canvasRef.current;
    if (!canvas || !renderedCanvas) {
      return;
    }

    const ctx = canvas.getContext('2d');

    // CRITICAL FIX: Only clear and redraw if we're in interactive mode
    // This prevents unnecessary canvas clearing during text edits
    if (isInteractive) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      // Draw the rendered template at full size
      ctx.drawImage(renderedCanvas, 0, 0);
    }

    // Get current overlay data dynamically from template
    const currentAllOverlays = template?.text_overlays?.overlays || [];
    const currentOverlays = currentAllOverlays.filter(overlay => overlay.type !== 'logo');
    const currentLogoOverlays = currentAllOverlays.filter(overlay => overlay.type === 'logo');

    // Draw interactive overlays if interactive mode is enabled
    if (isInteractive && currentOverlays.length > 0) {
      currentOverlays.forEach((overlay) => {
        const isSelected = currentSelectedOverlayId === overlay.id;
        const isHovered = hoveredOverlayId === overlay.id;
        const customization = customizations[overlay.id] || {};
        const position = customization.position || overlay.position;

        const x = position.x;
        const y = position.y;
        const width = position.width;
        const height = position.height;

        // Draw selection highlight
        if (isSelected) {
          // Draw selection background with subtle highlight
          ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
          ctx.fillRect(x - 2, y - 2, width + 4, height + 4);

          // Draw selection border
          ctx.strokeStyle = '#3B82F6';
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.strokeRect(x - 2, y - 2, width + 4, height + 4);

          // Draw selection handles with better visibility
          const handleSize = 10;
          ctx.fillStyle = '#3B82F6';
          ctx.strokeStyle = '#FFFFFF';
          ctx.lineWidth = 2;

          const handles = [
            [x - handleSize/2, y - handleSize/2], // Top-left
            [x + width - handleSize/2, y - handleSize/2], // Top-right
            [x - handleSize/2, y + height - handleSize/2], // Bottom-left
            [x + width - handleSize/2, y + height - handleSize/2], // Bottom-right
            [x + width/2 - handleSize/2, y - handleSize/2], // Top-center
            [x + width/2 - handleSize/2, y + height - handleSize/2], // Bottom-center
            [x - handleSize/2, y + height/2 - handleSize/2], // Left-center
            [x + width - handleSize/2, y + height/2 - handleSize/2] // Right-center
          ];

          handles.forEach(([hx, hy]) => {
            // Draw handle with shadow effect
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(hx + 1, hy + 1, handleSize, handleSize);

            ctx.fillStyle = '#3B82F6';
            ctx.fillRect(hx, hy, handleSize, handleSize);
            ctx.strokeRect(hx, hy, handleSize, handleSize);
          });

          // Draw overlay label
          ctx.fillStyle = '#3B82F6';
          ctx.font = 'bold 12px Arial';
          ctx.textAlign = 'left';
          const labelText = formatOverlayDisplayName(overlay.id) || 'Text Element';
          const labelY = y > 20 ? y - 8 : y + height + 16;
          ctx.fillText(labelText, x, labelY);
        } else if (isHovered) {
          // Draw hover highlight
          ctx.fillStyle = 'rgba(59, 130, 246, 0.05)';
          ctx.fillRect(x, y, width, height);

          ctx.strokeStyle = 'rgba(59, 130, 246, 0.6)';
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.strokeRect(x, y, width, height);

          // Show hover label
          ctx.fillStyle = 'rgba(59, 130, 246, 0.8)';
          ctx.font = '11px Arial';
          ctx.textAlign = 'left';
          const labelText = 'Click to select';
          const labelY = y > 20 ? y - 6 : y + height + 14;
          ctx.fillText(labelText, x, labelY);
        } else {
          // Draw subtle outline for non-selected, non-hovered overlays
          ctx.strokeStyle = 'rgba(59, 130, 246, 0.2)';
          ctx.lineWidth = 1;
          ctx.setLineDash([4, 4]);
          ctx.strokeRect(x, y, width, height);
        }
      });
    }

    // Draw interactive logo overlays if template supports them
    if (isInteractive && currentLogoOverlays.length > 0) {
      currentLogoOverlays.forEach((overlay) => {
        const isSelected = currentSelectedLogoOverlayId === overlay.id;
        const isHovered = hoveredLogoOverlayId === overlay.id;
        const customization = logoCustomizations[overlay.id] || {};
        const position = customization.position || overlay.position;

        const x = position.x;
        const y = position.y;
        const width = position.width;
        const height = position.height;

        // Draw selection highlight for logo overlays
        if (isSelected) {
          // Draw selection background
          ctx.fillStyle = 'rgba(34, 197, 94, 0.1)'; // Green tint for logos
          ctx.fillRect(x, y, width, height);

          // Draw selection border
          ctx.strokeStyle = '#22C55E'; // Green border for logos
          ctx.lineWidth = 3;
          ctx.setLineDash([]);
          ctx.strokeRect(x, y, width, height);

          // Draw selection corners
          const cornerSize = 8;
          ctx.fillStyle = '#22C55E';
          // Top-left corner
          ctx.fillRect(x - cornerSize/2, y - cornerSize/2, cornerSize, cornerSize);
          // Top-right corner
          ctx.fillRect(x + width - cornerSize/2, y - cornerSize/2, cornerSize, cornerSize);
          // Bottom-left corner
          ctx.fillRect(x - cornerSize/2, y + height - cornerSize/2, cornerSize, cornerSize);
          // Bottom-right corner
          ctx.fillRect(x + width - cornerSize/2, y + height - cornerSize/2, cornerSize, cornerSize);

          // Draw overlay label
          ctx.fillStyle = '#22C55E';
          ctx.font = 'bold 12px Arial';
          ctx.textAlign = 'left';
          const labelText = formatOverlayDisplayName(overlay.id) || 'Logo Element';
          const labelY = y > 20 ? y - 8 : y + height + 16;
          ctx.fillText(labelText, x, labelY);
        } else if (isHovered) {
          // Draw hover highlight
          ctx.fillStyle = 'rgba(34, 197, 94, 0.05)';
          ctx.fillRect(x, y, width, height);

          ctx.strokeStyle = 'rgba(34, 197, 94, 0.6)';
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.strokeRect(x, y, width, height);

          // Show hover label
          ctx.fillStyle = 'rgba(34, 197, 94, 0.8)';
          ctx.font = '11px Arial';
          ctx.textAlign = 'left';
          const labelText = 'Click to edit logo';
          const labelY = y > 20 ? y - 6 : y + height + 14;
          ctx.fillText(labelText, x, labelY);
        } else {
          // Draw subtle outline for non-selected, non-hovered logo overlays
          ctx.strokeStyle = 'rgba(34, 197, 94, 0.2)';
          ctx.lineWidth = 1;
          ctx.setLineDash([4, 4]);
          ctx.strokeRect(x, y, width, height);
        }
      });
    }
  };

  const getCanvasCoordinates = (e) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    return {
      x: (e.clientX - rect.left) * (canvas.width / rect.width),
      y: (e.clientY - rect.top) * (canvas.height / rect.height)
    };
  };

  const getOverlayAtPosition = (x, y) => {
    // Get current overlay data dynamically from template
    const currentAllOverlays = template?.text_overlays?.overlays || [];
    const currentOverlays = currentAllOverlays.filter(overlay => overlay.type !== 'logo');
    const currentLogoOverlays = currentAllOverlays.filter(overlay => overlay.type === 'logo');
    
    // Check all overlays (logo overlays have higher priority)
    const allOverlaysToCheck = [...currentLogoOverlays, ...currentOverlays];

    for (let i = allOverlaysToCheck.length - 1; i >= 0; i--) {
      const overlay = allOverlaysToCheck[i];
      const isLogo = overlay.type === 'logo';
      const customization = isLogo ?
        (logoCustomizations[overlay.id] || {}) :
        (customizations[overlay.id] || {});
      const position = customization.position || overlay.position;

      const overlayX = position.x;
      const overlayY = position.y;
      const overlayWidth = position.width;
      const overlayHeight = position.height;

      const isWithinBounds = x >= overlayX && x <= overlayX + overlayWidth &&
          y >= overlayY && y <= overlayY + overlayHeight;

      if (isWithinBounds) {
        return {
          type: isLogo ? 'logo' : 'text',
          id: overlay.id
        };
      }
    }

    return null;
  };

  const handleMouseMove = (e) => {
    if (!isInteractive) return;

    if (isDragging && currentSelectedOverlayId) {
      // Handle dragging
      e.preventDefault();
      const coords = getCanvasCoordinates(e);
      const deltaX = coords.x - dragStart.x;
      const deltaY = coords.y - dragStart.y;

      // Get current overlay data dynamically
      const currentAllOverlays = template?.text_overlays?.overlays || [];
      const currentOverlays = currentAllOverlays.filter(overlay => overlay.type !== 'logo');
      
      // Find the overlay being dragged
      const overlay = currentOverlays.find(o => o.id === currentSelectedOverlayId);
      if (!overlay) return;

      const currentCustomization = customizations[currentSelectedOverlayId] || {};
      const currentPosition = currentCustomization.position || overlay.position;

      // Calculate new position with bounds checking
      const newX = Math.max(0, Math.min(
        templateWidth - currentPosition.width,
        currentPosition.x + deltaX
      ));
      const newY = Math.max(0, Math.min(
        templateHeight - currentPosition.height,
        currentPosition.y + deltaY
      ));

      // Update customization
      onCustomizationChange?.(currentSelectedOverlayId, 'position', {
        ...currentPosition,
        x: newX,
        y: newY
      });

      setDragStart(coords);
    } else {
      // Handle hover detection
      const coords = getCanvasCoordinates(e);
      const overlayResult = getOverlayAtPosition(coords.x, coords.y);

      if (overlayResult?.type === 'logo') {
        setHoveredLogoOverlayId(overlayResult.id);
        setHoveredOverlayId(null);
      } else if (overlayResult?.type === 'text') {
        setHoveredOverlayId(overlayResult.id);
        setHoveredLogoOverlayId(null);
      } else {
        setHoveredOverlayId(null);
        setHoveredLogoOverlayId(null);
      }
    }
  };

  const handleMouseDown = (e) => {
    e.preventDefault();
    const coords = getCanvasCoordinates(e);
    const overlayResult = getOverlayAtPosition(coords.x, coords.y);

    if (overlayResult) {
      if (overlayResult.type === 'logo') {
        // Handle logo overlay selection
        setCurrentSelectedLogoOverlayId(overlayResult.id);
        setCurrentSelectedOverlayId(null);
        onLogoSelect?.(overlayResult.id);
        onTextSelect?.(null);
        // Note: Logo overlays don't support dragging in simplified version
      } else if (overlayResult.type === 'text') {
        // Handle text overlay selection (existing functionality)
        setCurrentSelectedOverlayId(overlayResult.id);
        setCurrentSelectedLogoOverlayId(null);
        setIsDragging(true);
        setDragStart(coords);
        onTextSelect?.(overlayResult.id);
        onLogoSelect?.(null);
      }
    } else {
      // Clear all selections
      setCurrentSelectedOverlayId(null);
      setCurrentSelectedLogoOverlayId(null);
      onTextSelect?.(null);
      onLogoSelect?.(null);
    }
  };

  const handleMouseUp = (e) => {
    if (isDragging) {
      e.preventDefault();
      setIsDragging(false);
    }
  };

  // Global mouse event listeners for drag operations
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e) => handleMouseMove(e);
      const handleGlobalMouseUp = (e) => handleMouseUp(e);

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, currentSelectedOverlayId, dragStart, customizations]);

  if (isLoading) {
    return (
      <div className={`interactive-template-canvas ${className}`} ref={containerRef}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Rendering template...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`interactive-template-canvas ${className}`} ref={containerRef}>
      <canvas
        ref={canvasRef}
        width={templateWidth}
        height={templateHeight}
        className={`${
          isInteractive
            ? isDragging
              ? 'cursor-grabbing'
              : hoveredOverlayId
                ? 'cursor-pointer'
                : 'cursor-default'
            : 'cursor-default'
        }`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setHoveredOverlayId(null)}
        style={{
          userSelect: 'none',
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        }}
      />
    </div>
  );
};

export default InteractiveTemplateCanvas;
