import React, { useState, useCallback, useEffect } from 'react';
import { Maximize2, Lock, Unlock } from 'lucide-react';

/**
 * Logo Size Editor Component
 * Provides slider and input controls for adjusting logo width and height
 * with aspect ratio lock and preset size options
 */
const LogoSizeEditor = ({
  width = 100,
  height = 60,
  originalWidth = 100,
  originalHeight = 60,
  constraints = {},
  maintainAspectRatio = true,
  onChange = null,
  onAspectRatioToggle = null,
  isMobile = false,
  className = ''
}) => {
  const [localWidth, setLocalWidth] = useState(width);
  const [localHeight, setLocalHeight] = useState(height);
  const [widthInput, setWidthInput] = useState(width.toString());
  const [heightInput, setHeightInput] = useState(height.toString());

  // Calculate aspect ratio from original dimensions
  const aspectRatio = originalWidth / originalHeight;

  // Apply constraints with sensible defaults
  const minWidth = constraints.min_width || 20;
  const maxWidth = constraints.max_width || 300;
  const minHeight = constraints.min_height || 20;
  const maxHeight = constraints.max_height || 300;

  // Update local state when props change
  useEffect(() => {
    setLocalWidth(width);
    setLocalHeight(height);
    setWidthInput(width.toString());
    setHeightInput(height.toString());
  }, [width, height]);

  // Handle width slider change
  const handleWidthSliderChange = useCallback((event) => {
    const newWidth = parseInt(event.target.value, 10);
    setLocalWidth(newWidth);
    setWidthInput(newWidth.toString());

    if (maintainAspectRatio) {
      const newHeight = Math.round(newWidth / aspectRatio);
      const constrainedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
      setLocalHeight(constrainedHeight);
      setHeightInput(constrainedHeight.toString());
      onChange?.({ width: newWidth, height: constrainedHeight });
    } else {
      onChange?.({ width: newWidth, height: localHeight });
    }
  }, [maintainAspectRatio, aspectRatio, localHeight, minHeight, maxHeight, onChange]);

  // Handle height slider change
  const handleHeightSliderChange = useCallback((event) => {
    const newHeight = parseInt(event.target.value, 10);
    setLocalHeight(newHeight);
    setHeightInput(newHeight.toString());

    if (maintainAspectRatio) {
      const newWidth = Math.round(newHeight * aspectRatio);
      const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
      setLocalWidth(constrainedWidth);
      setWidthInput(constrainedWidth.toString());
      onChange?.({ width: constrainedWidth, height: newHeight });
    } else {
      onChange?.({ width: localWidth, height: newHeight });
    }
  }, [maintainAspectRatio, aspectRatio, localWidth, minWidth, maxWidth, onChange]);

  // Handle width input change
  const handleWidthInputChange = useCallback((event) => {
    const newValue = event.target.value;
    setWidthInput(newValue);

    const numValue = parseInt(newValue, 10);
    if (!isNaN(numValue) && numValue >= minWidth) {
      setLocalWidth(numValue);
      if (maintainAspectRatio) {
        const newHeight = Math.round(numValue / aspectRatio);
        const constrainedHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
        setLocalHeight(constrainedHeight);
        setHeightInput(constrainedHeight.toString());
        onChange?.({ width: numValue, height: constrainedHeight });
      } else {
        onChange?.({ width: numValue, height: localHeight });
      }
    }
  }, [maintainAspectRatio, aspectRatio, localHeight, minWidth, minHeight, maxHeight, onChange]);

  // Handle height input change
  const handleHeightInputChange = useCallback((event) => {
    const newValue = event.target.value;
    setHeightInput(newValue);

    const numValue = parseInt(newValue, 10);
    if (!isNaN(numValue) && numValue >= minHeight) {
      setLocalHeight(numValue);
      if (maintainAspectRatio) {
        const newWidth = Math.round(numValue * aspectRatio);
        const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
        setLocalWidth(constrainedWidth);
        setWidthInput(constrainedWidth.toString());
        onChange?.({ width: constrainedWidth, height: numValue });
      } else {
        onChange?.({ width: localWidth, height: numValue });
      }
    }
  }, [maintainAspectRatio, aspectRatio, localWidth, minHeight, minWidth, maxWidth, onChange]);

  // Handle input blur for validation
  const handleWidthInputBlur = useCallback(() => {
    const numValue = parseInt(widthInput, 10);
    if (isNaN(numValue)) {
      setWidthInput(localWidth.toString());
      return;
    }
    const clampedValue = Math.max(minWidth, Math.min(maxWidth, numValue));
    setWidthInput(clampedValue.toString());
    if (clampedValue !== localWidth) {
      setLocalWidth(clampedValue);
      onChange?.({ width: clampedValue, height: localHeight });
    }
  }, [widthInput, localWidth, localHeight, minWidth, maxWidth, onChange]);

  const handleHeightInputBlur = useCallback(() => {
    const numValue = parseInt(heightInput, 10);
    if (isNaN(numValue)) {
      setHeightInput(localHeight.toString());
      return;
    }
    const clampedValue = Math.max(minHeight, Math.min(maxHeight, numValue));
    setHeightInput(clampedValue.toString());
    if (clampedValue !== localHeight) {
      setLocalHeight(clampedValue);
      onChange?.({ width: localWidth, height: clampedValue });
    }
  }, [heightInput, localHeight, localWidth, minHeight, maxHeight, onChange]);

  // Handle preset size selection
  const handlePresetSize = useCallback((preset) => {
    const presets = {
      small: { 
        width: Math.round(originalWidth * 0.7), 
        height: Math.round(originalHeight * 0.7) 
      },
      medium: { 
        width: originalWidth, 
        height: originalHeight 
      },
      large: { 
        width: Math.round(originalWidth * 1.3), 
        height: Math.round(originalHeight * 1.3) 
      }
    };

    const size = presets[preset];
    if (size) {
      // Apply constraints to preset sizes
      const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, size.width));
      const constrainedHeight = Math.max(minHeight, Math.min(maxHeight, size.height));
      
      setLocalWidth(constrainedWidth);
      setLocalHeight(constrainedHeight);
      setWidthInput(constrainedWidth.toString());
      setHeightInput(constrainedHeight.toString());
      onChange?.({ width: constrainedWidth, height: constrainedHeight });
    }
  }, [originalWidth, originalHeight, minWidth, maxWidth, minHeight, maxHeight, onChange]);

  return (
    <div className={`logo-size-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Maximize2 className="w-4 h-4 text-green-600" />
          <label className="block text-sm font-medium text-gray-700">
            Logo Size
          </label>
        </div>
        <span className="text-xs text-gray-500">
          {localWidth}×{localHeight}px
        </span>
      </div>

      {/* Width Controls */}
      <div className="mb-4">
        <label className="block text-xs text-gray-500 mb-1">Width</label>
        <input
          type="range"
          min={minWidth}
          max={maxWidth}
          value={localWidth}
          onChange={handleWidthSliderChange}
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider ${
            isMobile ? 'h-3' : 'h-2'
          }`}
          style={{
            background: `linear-gradient(to right, #10B981 0%, #10B981 ${
              ((localWidth - minWidth) / (maxWidth - minWidth)) * 100
            }%, #E5E7EB ${((localWidth - minWidth) / (maxWidth - minWidth)) * 100}%, #E5E7EB 100%)`
          }}
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{minWidth}px</span>
          <span>{maxWidth}px</span>
        </div>
        
        {/* Width Input */}
        <div className="relative mt-2">
          <input
            type="number"
            min={minWidth}
            max={maxWidth}
            value={widthInput}
            onChange={handleWidthInputChange}
            onBlur={handleWidthInputBlur}
            className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              isMobile ? 'text-base' : 'text-sm'
            }`}
            placeholder="Width"
          />
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
            px
          </span>
        </div>
      </div>

      {/* Height Controls */}
      <div className="mb-4">
        <label className="block text-xs text-gray-500 mb-1">Height</label>
        <input
          type="range"
          min={minHeight}
          max={maxHeight}
          value={localHeight}
          onChange={handleHeightSliderChange}
          disabled={maintainAspectRatio}
          className={`w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider ${
            isMobile ? 'h-3' : 'h-2'
          } ${maintainAspectRatio ? 'opacity-50 cursor-not-allowed' : ''}`}
          style={{
            background: maintainAspectRatio 
              ? '#E5E7EB'
              : `linear-gradient(to right, #10B981 0%, #10B981 ${
                  ((localHeight - minHeight) / (maxHeight - minHeight)) * 100
                }%, #E5E7EB ${((localHeight - minHeight) / (maxHeight - minHeight)) * 100}%, #E5E7EB 100%)`
          }}
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{minHeight}px</span>
          <span>{maxHeight}px</span>
        </div>
        
        {/* Height Input */}
        <div className="relative mt-2">
          <input
            type="number"
            min={minHeight}
            max={maxHeight}
            value={heightInput}
            onChange={handleHeightInputChange}
            onBlur={handleHeightInputBlur}
            disabled={maintainAspectRatio}
            className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              isMobile ? 'text-base' : 'text-sm'
            } ${maintainAspectRatio ? 'opacity-50 cursor-not-allowed bg-gray-50' : ''}`}
            placeholder="Height"
          />
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
            px
          </span>
        </div>
      </div>

      {/* Aspect Ratio Toggle - Compact */}
      <div className="mb-4 flex items-center justify-between">
        <label className="block text-xs text-gray-500">Aspect Ratio</label>
        <div className="flex items-center gap-2">
          {maintainAspectRatio && (
            <span className="text-xs text-gray-600">
              {aspectRatio >= 1
                ? `${aspectRatio.toFixed(1)}:1.0`
                : `1.0:${(1/aspectRatio).toFixed(1)}`
              }
            </span>
          )}
          <button
            onClick={() => onAspectRatioToggle?.(!maintainAspectRatio)}
            className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
              maintainAspectRatio
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'
            }`}
          >
            {maintainAspectRatio ? <Lock size={12} /> : <Unlock size={12} />}
            <span>{maintainAspectRatio ? 'Locked' : 'Free'}</span>
          </button>
        </div>
      </div>

      {/* Preset Sizes */}
      <div>
        <label className="block text-xs text-gray-500 mb-2">Quick Sizes</label>
        <div className="flex gap-2">
          {['small', 'medium', 'large'].map((preset) => (
            <button
              key={preset}
              onClick={() => handlePresetSize(preset)}
              className="flex-1 px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors text-center"
            >
              {preset.charAt(0).toUpperCase() + preset.slice(1)}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LogoSizeEditor;
