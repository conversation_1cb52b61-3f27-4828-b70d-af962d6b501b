import React, { useState, useCallback, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';
import imageOverlayService from '../../../services/imageOverlayService.js';
import { prodLogger } from '../../../utils/prodLogger.js';

/**
 * ContentEditor - Handles custom text content editing for overlays
 * Allows users to override placeholder-based text with custom content
 */
const ContentEditor = ({
  overlay,
  documentData = {},
  customizations = {},
  onContentChange,
  isMobile = false,
  className = '',
  // NEW: Direct pass of the current rendered text from canvas
  currentRenderedText = null
}) => {
  const [localContent, setLocalContent] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  // Get the current custom content
  const customContent = customizations.content;
  const isCustomContent = customContent !== undefined;
  
  // Use the passed current rendered text if available, otherwise calculate it
  // BUT: If we have custom content, we should show that instead
  const actualCurrentText = currentRenderedText ?? imageOverlayService.populatePlaceholder(
    overlay.placeholder || '',
    documentData,
    {} // Empty customizations to get the profile data fallback
  );
  
  // Determine what text to show in the editor:
  // 1. If we have custom content, show that
  // 2. Otherwise, show the actual current rendered text
  const textToShowInEditor = customContent ?? actualCurrentText;
  
  // Generate default text from placeholder (without custom content) for reference
  const defaultText = imageOverlayService.populatePlaceholder(
    overlay.placeholder || '',
    documentData,
    {} // Empty customizations to get the profile data fallback
  );

  // Initialize local content - FIXED: Use the correct text based on custom content state
  useEffect(() => {
    // Show custom content if it exists, otherwise show the actual current rendered content
    setLocalContent(textToShowInEditor);


  }, [textToShowInEditor]);

  // Handle content changes with immediate UI feedback (no debouncing)
  const handleContentChange = useCallback((newContent) => {
    setLocalContent(newContent); // Immediate UI update

    // Determine if content should be saved as custom or reset to default
    // Compare against actual current rendered text (not the custom content)
    const contentToSave = newContent === actualCurrentText ? undefined : newContent;

    if (onContentChange) {
      onContentChange(contentToSave); // Pass through immediately
    }

    prodLogger.debug('📝 Content updated (immediate):', {
      overlayId: overlay.id,
      isCustom: contentToSave !== undefined,
      contentLength: newContent.length,
      actualCurrentText,
      newContent
    });
  }, [actualCurrentText, onContentChange, overlay.id]);

  // Reset to profile/document data
  const handleReset = useCallback(() => {
    setLocalContent(actualCurrentText);
    if (onContentChange) {
      onContentChange(undefined);
    }
    
    prodLogger.debug('🔄 Content reset to actual current:', {
      overlayId: overlay.id,
      actualCurrentText
    });
  }, [actualCurrentText, onContentChange, overlay.id]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };
  
  // No cleanup needed since we removed debouncing

  return (
    <div className={`content-editor ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm font-medium text-gray-700">
          Text Content
        </label>
        <div className="flex items-center space-x-2">
          {/* Status indicator */}
          <span className={`text-xs px-2 py-1 rounded-full ${
            isCustomContent 
              ? 'bg-blue-100 text-blue-700' 
              : 'bg-gray-100 text-gray-600'
          }`}>
            {isCustomContent ? 'Custom' : 'From Profile'}
          </span>
          
          {/* Reset button */}
          {isCustomContent && (
            <button
              onClick={handleReset}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
              title="Reset to profile data"
            >
              <RefreshCw className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>

      {/* Mobile: Expandable textarea */}
      {isMobile ? (
        <div>
          <button
            onClick={toggleExpanded}
            className="w-full p-3 text-left border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <div className="text-sm text-gray-900 truncate">
              {localContent || 'Click to edit text content...'}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {isExpanded ? 'Tap to collapse' : 'Tap to expand editor'}
            </div>
          </button>
          
          {isExpanded && (
            <div className="mt-2">
              <textarea
                value={localContent}
                onChange={(e) => handleContentChange(e.target.value)}
                placeholder={actualCurrentText}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                rows={4}
                autoFocus
              />
            </div>
          )}
        </div>
      ) : (
        /* Desktop: Always visible textarea */
        <textarea
          value={localContent}
          onChange={(e) => handleContentChange(e.target.value)}
          placeholder={actualCurrentText}
          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          rows={3}
        />
      )}

      {/* Helper text */}
      {!isCustomContent && (
        <p className="text-xs text-gray-500 mt-1">
          Current: "{actualCurrentText}"
        </p>
      )}
      
      {/* Character count for longer content */}
      {localContent && localContent.length > 50 && (
        <p className="text-xs text-gray-400 mt-1 text-right">
          {localContent.length} characters
        </p>
      )}
    </div>
  );
};

export default ContentEditor;