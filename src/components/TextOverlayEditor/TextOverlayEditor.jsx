import React, { useState, useCallback, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, RotateCcw, X, ChevronUp, ChevronDown, Undo2, Trash2 } from 'lucide-react';
import FontSizeEditor from './PropertyEditors/FontSizeEditor';
import FontFamilyEditor from './PropertyEditors/FontFamilyEditor';
import ColorEditor from './PropertyEditors/ColorEditor';
import PositionEditor from './PropertyEditors/PositionEditor';
import AlignmentEditor from './PropertyEditors/AlignmentEditor';
import StyleEditor from './PropertyEditors/StyleEditor';
import ContentEditor from './PropertyEditors/ContentEditor';
import LogoSizeEditor from './PropertyEditors/LogoSizeEditor';
import { formatOverlayDisplayName, getOverlayDisplayLabel } from '../../utils/overlayUtils';
// Import for logo editing functionality
import Button from '../ui/Button';
import Icon from '../AppIcon';
import LogoUpload from '../ui/LogoUpload';
import ConfirmationModal from '../ui/ConfirmationModal';
import { useAuth } from '../../contexts/AuthContext';
import { getUserLogos, uploadLogo } from '../../services/logoService';
import imageOverlayService from '../../services/imageOverlayService.js';
import { prodLogger } from '../../utils/prodLogger.js';

/**
 * Text Overlay Editor Component
 * Provides a unified editing interface for both text and logo overlays
 * Automatically switches content based on selected overlay type
 */
const TextOverlayEditor = ({
  template,
  customizations = {},
  onCustomizationChange,
  onReset,
  onUndo,
  onDeleteOverlay,
  canUndo = false,
  isVisible = false,
  onToggleVisibility,
  selectedOverlayId = null,
  onOverlaySelect,
  // Logo overlay props
  selectedLogoOverlayId = null,
  logoCustomizations = {},
  onLogoChange,
  onLogoDelete,
  onLogoResize,
  onAspectRatioToggle,
  // Document data for content editing
  documentData = {},
  className = ''
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [internalSelectedOverlayId, setInternalSelectedOverlayId] = useState(null);


  // Logo editing state
  const { user } = useAuth();
  const [userLogos, setUserLogos] = useState([]);
  const [logoLoading, setLogoLoading] = useState(false);
  const [uploadMode, setUploadMode] = useState(false);
  const [logoError, setLogoError] = useState('');
  
  // Confirmation modal state
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    isLoading: false
  });

  // Use external selection if provided, otherwise use internal state
  const currentSelectedOverlayId = selectedOverlayId || internalSelectedOverlayId;
  const setSelectedOverlayId = onOverlaySelect || setInternalSelectedOverlayId;


  // Get overlays from template - INCLUDE ALL OVERLAYS FOR UNIFIED EDITOR
  const allOverlays = template?.text_overlays?.overlays || [];
  const overlays = allOverlays; // Include both text and logo overlays for unified selection
  const textOnlyOverlays = allOverlays.filter(overlay => overlay.type !== 'logo');
  const logoOverlays = allOverlays.filter(overlay => overlay.type === 'logo');

  // Select first overlay by default if no external selection
  const hasAttemptedAutoSelect = useRef(false);
  
  useEffect(() => {
    // Only auto-select once and if we have overlays, no current selection (text OR logo), and no external selection
    if (overlays.length > 0 && !currentSelectedOverlayId && !selectedOverlayId && !selectedLogoOverlayId && !hasAttemptedAutoSelect.current) {
      hasAttemptedAutoSelect.current = true;
      // Use the appropriate setter based on whether we have external control
      if (onOverlaySelect) {
        onOverlaySelect(overlays[0].id);
      } else {
        setInternalSelectedOverlayId(overlays[0].id);
      }
    }
  }, [overlays.length, currentSelectedOverlayId, selectedOverlayId, selectedLogoOverlayId, onOverlaySelect]);

  // Reset auto-select flag when template changes
  useEffect(() => {
    hasAttemptedAutoSelect.current = false;
  }, [template?.id]);

  // Force update when external selection changes
  useEffect(() => {

    if (selectedOverlayId && overlays.find(o => o.id === selectedOverlayId)) {
      // Selection is valid, no need to change internal state since we use external || internal
    } else if (selectedOverlayId === null) {
      // External selection was cleared
      setInternalSelectedOverlayId(null);
    }
  }, [selectedOverlayId, overlays]);

  // Logo editing functions - MOVED UP before useEffect that uses them
  const loadUserLogos = useCallback(async () => {
    try {
      setLogoLoading(true);
      setLogoError('');
      
      const result = await getUserLogos(user.id, { 
        activeOnly: true, 
        includeDefault: true 
      });
      
      if (result.success) {
        setUserLogos(result.logos);
      } else {
        setLogoError(result.error || 'Failed to load logos');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to load user logos:', err);
      setLogoError('Failed to load logos');
    } finally {
      setLogoLoading(false);
    }
  }, [user.id]);

  // Load user's logos when logo overlay is selected
  useEffect(() => {
    if (user?.id && isVisible && selectedLogoOverlayId) {
      loadUserLogos();
    }
  }, [user?.id, isVisible, selectedLogoOverlayId, loadUserLogos]);

  // Get current overlay data
  const selectedOverlay = overlays.find(overlay => overlay.id === currentSelectedOverlayId);
  const selectedLogoOverlay = logoOverlays.find(overlay => overlay.id === selectedLogoOverlayId);
  
  // Get customizations for current overlay (declare before using)
  const overlayCustomizations = customizations[currentSelectedOverlayId] || {};
  const logoOverlayCustomizations = logoCustomizations[selectedLogoOverlayId] || {};
  
  // Calculate the current rendered text for the selected overlay
  const currentRenderedText = selectedOverlay ? imageOverlayService.populatePlaceholder(
    selectedOverlay.placeholder,
    documentData,
    overlayCustomizations
  ) : '';


  
  // Determine if we're editing a logo overlay
  const isEditingLogo = !!selectedLogoOverlayId && !!selectedLogoOverlay;
  const isEditingText = !!currentSelectedOverlayId && !!selectedOverlay;



  // Merge original styling with customizations
  const currentStyling = selectedOverlay ? {
    ...selectedOverlay.styling,
    ...overlayCustomizations.styling
  } : {};

  const currentPosition = selectedOverlay ? {
    ...selectedOverlay.position,
    ...overlayCustomizations.position
  } : {};

  // Other logo editing functions
  const handleLogoSelect = useCallback((logoId) => {
    if (!selectedLogoOverlayId || !onLogoChange) return;

    const selectedLogo = userLogos.find(logo => logo.id === logoId);
    if (selectedLogo) {
      onLogoChange(selectedLogoOverlayId, selectedLogo);
      setUploadMode(false);
    }
  }, [selectedLogoOverlayId, onLogoChange, userLogos]);

  const handleLogoUpload = useCallback(async (file, validationResult) => {
    if (!validationResult.success) {
      setLogoError(validationResult.error);
      return;
    }
    
    try {
      setLogoLoading(true);
      setLogoError('');
      
      const uploadResult = await uploadLogo(file, user.id, {
        name: file.name.replace(/\.[^/.]+$/, ''),
        description: 'Logo uploaded from template editor'
      });
      
      if (uploadResult.success) {
        // Refresh logo list
        await loadUserLogos();
        
        // Auto-select the newly uploaded logo
        if (selectedLogoOverlayId && onLogoChange) {
          onLogoChange(selectedLogoOverlayId, uploadResult.logo);
        }
        
        // Show success message with additional info if set as first default
        if (uploadResult.setAsFirstDefault) {
          prodLogger.debug('✅ Logo uploaded and set as first default');
        }
        
        setUploadMode(false);
      } else {
        setLogoError(uploadResult.error || 'Failed to upload logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to upload logo:', err);
      setLogoError('Failed to upload logo');
    } finally {
      setLogoLoading(false);
    }
  }, [user.id, loadUserLogos, selectedLogoOverlayId, onLogoChange]);

  const handleDeleteLogo = useCallback(() => {
    if (!selectedLogoOverlayId || !onLogoDelete) return;
    
    setConfirmModal({
      isOpen: true,
      isLoading: false
    });
  }, [selectedLogoOverlayId, onLogoDelete]);

  const handleCloseConfirmModal = useCallback(() => {
    if (!confirmModal.isLoading) {
      setConfirmModal({ isOpen: false, isLoading: false });
    }
  }, [confirmModal.isLoading]);

  // Handle text overlay deletion
  const handleDeleteTextOverlay = useCallback(() => {
    if (!currentSelectedOverlayId || !onDeleteOverlay) return;
    
    setConfirmModal({
      isOpen: true,
      isLoading: false,
      type: 'text' // Mark this as text overlay deletion
    });
  }, [currentSelectedOverlayId, onDeleteOverlay]);

  const handleConfirmDeleteTextOverlay = useCallback(async () => {
    if (!currentSelectedOverlayId || !onDeleteOverlay) return;

    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      onDeleteOverlay(currentSelectedOverlayId);
      setConfirmModal({ isOpen: false, isLoading: false });
      
      // Clear selection after deletion
      if (onOverlaySelect) {
        onOverlaySelect(null);
      } else {
        setInternalSelectedOverlayId(null);
      }
    } catch (error) {
      prodLogger.error('Error deleting text overlay:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));
    }
  }, [currentSelectedOverlayId, onDeleteOverlay, onOverlaySelect]);

  // Unified confirm handler that routes to appropriate delete method
  const handleConfirmDelete = useCallback(async () => {
    if (confirmModal.type === 'text') {
      return handleConfirmDeleteTextOverlay();
    } else {
      // Default to logo deletion for backwards compatibility
      if (!selectedLogoOverlayId || !onLogoDelete) return;

      setConfirmModal(prev => ({ ...prev, isLoading: true }));

      try {
        onLogoDelete(selectedLogoOverlayId);
        setConfirmModal({ isOpen: false, isLoading: false });
      } catch (error) {
        prodLogger.error('Error removing logo:', error);
        setConfirmModal(prev => ({ ...prev, isLoading: false }));
      }
    }
  }, [confirmModal.type, handleConfirmDeleteTextOverlay, selectedLogoOverlayId, onLogoDelete]);



  // Unified overlay selection handler that determines overlay type
  const handleUnifiedOverlaySelect = useCallback((overlayId) => {
    // Find the overlay to determine its type
    const overlay = allOverlays.find(o => o.id === overlayId);
    if (!overlay) {
      return;
    }
    
    if (overlay.type === 'logo') {
      // We need to call the parent's logo selection handler
      // For now, just call the text handler but we should improve this
      if (onOverlaySelect) {
        onOverlaySelect(overlayId);
      }
    } else {
      // This is a text overlay - update text selection
      if (onOverlaySelect) {
        onOverlaySelect(overlayId);
      } else {
        setInternalSelectedOverlayId(overlayId);
      }
    }
  }, [allOverlays, onOverlaySelect]);

  // Handle property changes
  const handlePropertyChange = useCallback((property, value) => {
    if (!currentSelectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = {
      ...customizations,
      [currentSelectedOverlayId]: {
        ...customizations[currentSelectedOverlayId],
        [property]: {
          ...customizations[currentSelectedOverlayId]?.[property],
          ...value
        }
      }
    };

    onCustomizationChange(updatedCustomizations);
  }, [currentSelectedOverlayId, customizations, onCustomizationChange]);

  // Handle styling changes
  const handleStylingChange = useCallback((styleProperty, value) => {
    handlePropertyChange('styling', { [styleProperty]: value });
  }, [handlePropertyChange]);

  // Handle position changes
  const handlePositionChange = useCallback((positionProperty, value) => {
    handlePropertyChange('position', { [positionProperty]: value });
  }, [handlePropertyChange]);

  // Handle content changes
  const handleContentChange = useCallback((newContent) => {
    if (!currentSelectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = {
      ...customizations,
      [currentSelectedOverlayId]: {
        ...customizations[currentSelectedOverlayId],
        content: newContent
      }
    };

    onCustomizationChange(updatedCustomizations);
    
    prodLogger.debug('📝 Content customization updated:', {
      overlayId: currentSelectedOverlayId,
      hasCustomContent: newContent !== undefined,
      contentLength: newContent ? newContent.length : 0
    });
  }, [currentSelectedOverlayId, customizations, onCustomizationChange]);

  // Reset overlay to defaults
  const handleResetOverlay = useCallback(() => {
    if (!currentSelectedOverlayId || !onCustomizationChange) return;

    const updatedCustomizations = { ...customizations };
    delete updatedCustomizations[currentSelectedOverlayId];
    onCustomizationChange(updatedCustomizations);
  }, [currentSelectedOverlayId, customizations, onCustomizationChange]);

  // Toggle panel visibility - use the main toggle function for consistent behavior
  const handleToggleCollapse = useCallback(() => {
    // UX FIX: Use the main toggle visibility function instead of just collapsing
    // This ensures consistent layout shifts like the edit text button behavior
    // Single source of truth approach prevents layout inconsistencies
    if (onToggleVisibility) {
      onToggleVisibility(false);
    } else {
      // Fallback to local collapse state if no main toggle function
      setIsCollapsed(!isCollapsed);
    }
  }, [onToggleVisibility, isCollapsed]);

  if (!isVisible) return null;

  return (
    <div className={`text-overlay-editor ${className}`}>
      {/* Mobile: Adaptive bottom sheet overlay */}
      <div className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <MobileEditorContent
          overlays={overlays}
          selectedOverlayId={currentSelectedOverlayId}
          selectedLogoOverlayId={selectedLogoOverlayId}
          onSelectOverlay={handleUnifiedOverlaySelect}
          currentStyling={currentStyling}
          currentPosition={currentPosition}
          onStylingChange={handleStylingChange}
          onPositionChange={handlePositionChange}
          onContentChange={handleContentChange}
          onReset={handleResetOverlay}
          onResetAll={onReset}
          onUndo={onUndo}
          canUndo={canUndo}
          onClose={() => onToggleVisibility?.(false)}
          // Logo editing props
          isEditingLogo={isEditingLogo}
          isEditingText={isEditingText}
          selectedOverlay={selectedOverlay}
          selectedLogoOverlay={selectedLogoOverlay}
          userLogos={userLogos}
          logoLoading={logoLoading}
          logoError={logoError}
          uploadMode={uploadMode}
          onLogoSelect={handleLogoSelect}
          onLogoUpload={handleLogoUpload}
          onDeleteLogo={handleDeleteLogo}
          onDeleteOverlay={onDeleteOverlay}
          onDeleteTextOverlay={handleDeleteTextOverlay}
          onToggleUpload={() => setUploadMode(!uploadMode)}
          // Logo resize props
          logoCustomizations={logoCustomizations}
          onLogoResize={onLogoResize}
          onAspectRatioToggle={onAspectRatioToggle}
          // Additional props for content editing
          overlay={selectedOverlay}
          documentData={documentData}
          overlayCustomizations={overlayCustomizations}
          currentRenderedText={currentRenderedText}
        />
      </div>

      {/* Desktop: Side panel */}
      <div className="hidden lg:block">
        <DesktopEditorPanel
          isCollapsed={isCollapsed}
          onToggleCollapse={handleToggleCollapse}
          overlays={overlays}
          selectedOverlayId={currentSelectedOverlayId}
          selectedLogoOverlayId={selectedLogoOverlayId}
          onSelectOverlay={handleUnifiedOverlaySelect}
          currentStyling={currentStyling}
          currentPosition={currentPosition}
          onStylingChange={handleStylingChange}
          onPositionChange={handlePositionChange}
          onContentChange={handleContentChange}
          onReset={handleResetOverlay}
          onResetAll={onReset}
          onUndo={onUndo}
          canUndo={canUndo}
          // Logo editing props
          isEditingLogo={isEditingLogo}
          isEditingText={isEditingText}
          selectedOverlay={selectedOverlay}
          selectedLogoOverlay={selectedLogoOverlay}
          userLogos={userLogos}
          logoLoading={logoLoading}
          logoError={logoError}
          uploadMode={uploadMode}
          onLogoSelect={handleLogoSelect}
          onLogoUpload={handleLogoUpload}
          onDeleteLogo={handleDeleteLogo}
          onDeleteOverlay={onDeleteOverlay}
          onDeleteTextOverlay={handleDeleteTextOverlay}
          onToggleUpload={() => setUploadMode(!uploadMode)}
          // Logo resize props
          logoCustomizations={logoCustomizations}
          onLogoResize={onLogoResize}
          onAspectRatioToggle={onAspectRatioToggle}
          // Pass main toggle function for consistent behavior
          onToggleVisibility={onToggleVisibility}
          // Additional props for content editing
          overlay={selectedOverlay}
          documentData={documentData}
          overlayCustomizations={overlayCustomizations}
          currentRenderedText={currentRenderedText}
        />
      </div>
      
      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmModal.isOpen}
        onClose={handleCloseConfirmModal}
        onConfirm={handleConfirmDelete}
        title={confirmModal.type === 'text' ? "Delete Overlay" : "Remove Logo"}
        message={confirmModal.type === 'text' 
          ? "Are you sure you want to delete this text overlay? This action can be undone."
          : "Are you sure you want to remove this logo from the template?"
        }
        confirmText={confirmModal.type === 'text' ? "Delete" : "Remove"}
        cancelText="Cancel"
        type="warning"
        isLoading={confirmModal.isLoading}
      />
    </div>
  );
};

/**
 * Mobile Editor Content Component with Adaptive Bottom Sheet
 */
const MobileEditorContent = ({
  overlays,
  selectedOverlayId,
  selectedLogoOverlayId,
  onSelectOverlay,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onContentChange,
  onReset,
  onResetAll,
  onUndo,
  canUndo,
  onClose,
  // Logo editing props
  isEditingLogo,
  isEditingText,
  selectedOverlay,
  selectedLogoOverlay,
  userLogos,
  logoLoading,
  logoError,
  uploadMode,
  onLogoSelect,
  onLogoUpload,
  onDeleteLogo,
  onDeleteOverlay,
  onDeleteTextOverlay,
  onToggleUpload,
  onSetAsDefault,
  // Logo resize props
  logoCustomizations,
  onLogoResize,
  onAspectRatioToggle,
  // Additional props for content editing
  overlay,
  documentData,
  overlayCustomizations,
  currentRenderedText
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [currentHeight, setCurrentHeight] = useState(() => {
    // Responsive initial height based on screen size and orientation
    const isLandscape = window.innerWidth > window.innerHeight;
    const isSmallScreen = window.innerHeight < 600;

    if (isLandscape) return 50; // More height in landscape
    if (isSmallScreen) return 45; // Slightly more on small screens
    return 40; // Default
  });

  // Handle drag start
  const handleDragStart = useCallback((e) => {
    setIsDragging(true);
    setStartY(e.touches ? e.touches[0].clientY : e.clientY);
  }, []);

  // Handle drag move
  const handleDragMove = useCallback((e) => {
    if (!isDragging) return;

    const currentY = e.touches ? e.touches[0].clientY : e.clientY;
    const deltaY = startY - currentY;
    const viewportHeight = window.innerHeight;
    const deltaVh = (deltaY / viewportHeight) * 100;

    // Calculate new height (between 25vh and 90vh)
    const newHeight = Math.min(90, Math.max(25, currentHeight + deltaVh));
    setCurrentHeight(newHeight);

    // Auto-expand/collapse based on height
    setIsExpanded(newHeight > 60);
  }, [isDragging, startY, currentHeight]);

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    if (!isDragging) return;
    setIsDragging(false);

    // If dragged below 25vh, close the panel
    if (currentHeight < 25) {
      onClose();
      return;
    }

    // Snap to collapsed (40vh) or expanded (85vh) state
    if (currentHeight < 60) {
      setCurrentHeight(40);
      setIsExpanded(false);
    } else {
      setCurrentHeight(85);
      setIsExpanded(true);
    }
  }, [isDragging, currentHeight, onClose]);

  // Toggle expanded state with responsive heights
  const toggleExpanded = useCallback(() => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);

    // Calculate responsive heights
    const isLandscape = window.innerWidth > window.innerHeight;
    const isSmallScreen = window.innerHeight < 600;

    if (newExpanded) {
      // Expanded state
      if (isLandscape) {
        setCurrentHeight(90); // More height in landscape
      } else if (isSmallScreen) {
        setCurrentHeight(88); // Almost full screen on small devices
      } else {
        setCurrentHeight(85); // Default expanded
      }
    } else {
      // Collapsed state
      if (isLandscape) {
        setCurrentHeight(50);
      } else if (isSmallScreen) {
        setCurrentHeight(45);
      } else {
        setCurrentHeight(40);
      }
    }
  }, [isExpanded]);

  // Handle keyboard navigation for drag handle
  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toggleExpanded();
    } else if (e.key === 'Escape') {
      onClose();
    }
  }, [toggleExpanded, onClose]);

  // Add event listeners for drag and keyboard
  useEffect(() => {
    if (isDragging) {
      const handleMouseMove = (e) => handleDragMove(e);
      const handleMouseUp = () => handleDragEnd();
      const handleTouchMove = (e) => handleDragMove(e);
      const handleTouchEnd = () => handleDragEnd();

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isDragging, handleDragMove, handleDragEnd]);

  // Handle orientation and resize changes
  useEffect(() => {
    const handleResize = () => {
      // Update height based on new orientation/size
      const isLandscape = window.innerWidth > window.innerHeight;
      const isSmallScreen = window.innerHeight < 600;

      if (isExpanded) {
        if (isLandscape) {
          setCurrentHeight(90);
        } else if (isSmallScreen) {
          setCurrentHeight(88);
        } else {
          setCurrentHeight(85);
        }
      } else {
        if (isLandscape) {
          setCurrentHeight(50);
        } else if (isSmallScreen) {
          setCurrentHeight(45);
        } else {
          setCurrentHeight(40);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, [isExpanded]);

  return (
    <div
      className="mobile-bottom-sheet absolute bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-2xl"
      style={{
        height: `${currentHeight}vh`,
        transform: isDragging ? 'none' : undefined,
        transition: isDragging ? 'none' : 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    >
      <div className="flex flex-col h-full">
        {/* Drag Handle */}
        <div
          className="mobile-drag-handle flex items-center justify-center py-3 cursor-grab active:cursor-grabbing touch-pan-y focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
          onMouseDown={handleDragStart}
          onTouchStart={handleDragStart}
          onKeyDown={handleKeyDown}
          role="button"
          aria-label={`${isExpanded ? 'Collapse' : 'Expand'} text editor panel. Current height: ${Math.round(currentHeight)}% of screen`}
          aria-expanded={isExpanded}
          tabIndex={0}
        >
          <div className="w-12 h-1 bg-gray-300 rounded-full transition-colors"></div>
        </div>

        {/* Mobile Header */}
        <div className="mobile-editor-header flex items-center justify-between px-4 py-1 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold text-gray-900">
              {isEditingLogo ? 'Edit Logo' : 'Edit Text Style'}
            </h3>
            <button
              onClick={toggleExpanded}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors rounded-md"
              aria-label={isExpanded ? "Collapse panel" : "Expand panel"}
            >
              {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
            </button>
          </div>
          <div className="flex items-center space-x-1">
            {/* Delete Button */}
            {selectedOverlayId && onDeleteOverlay && !isEditingLogo && (
              <button
                onClick={onDeleteTextOverlay}
                className="p-2 text-red-500 hover:text-red-700 transition-colors rounded-lg hover:bg-red-50"
                aria-label="Delete this overlay"
                title="Delete this overlay"
              >
                <Trash2 className="w-5 h-5" />
              </button>
            )}
            {/* Undo Button */}
            {canUndo && (
              <button
                onClick={onUndo}
                className="p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-lg hover:bg-gray-100"
                aria-label="Undo last change"
                title="Undo last change"
              >
                <Undo2 className="w-5 h-5" />
              </button>
            )}
            {/* Reset All Button */}
            <button
              onClick={onResetAll}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-lg hover:bg-gray-100"
              aria-label="Reset all changes"
              title="Reset all changes"
            >
              <RotateCcw className="w-5 h-5" />
            </button>
            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors rounded-lg hover:bg-gray-100"
              aria-label="Close editor"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Compact Overlay Selection */}
        <div className="px-4 py-1 border-b border-gray-200 bg-gray-50">
          <CompactOverlaySelector
            overlays={overlays}
            selectedOverlayId={selectedOverlayId}
            selectedLogoOverlayId={selectedLogoOverlayId}
            onSelectOverlay={onSelectOverlay}
          />
        </div>

        {/* Scrollable Editor Content */}
        <div className="mobile-editor-content flex-1 overflow-y-auto">
          <div className="p-4">
            {isEditingLogo ? (
              <LogoEditorContent
                selectedOverlay={selectedLogoOverlay}
                userLogos={userLogos}
                loading={logoLoading}
                error={logoError}
                uploadMode={uploadMode}
                onLogoSelect={onLogoSelect}
                onLogoUpload={onLogoUpload}
                onDeleteLogo={onDeleteLogo}
                onToggleUpload={onToggleUpload}
                logoCustomizations={logoCustomizations}
                onLogoResize={onLogoResize}
                onAspectRatioToggle={onAspectRatioToggle}
              />
            ) : isEditingText ? (
              <EditorContent
                overlay={selectedOverlay}
                documentData={documentData}
                customizations={overlayCustomizations}
                currentStyling={currentStyling}
                currentPosition={currentPosition}
                onStylingChange={onStylingChange}
                onPositionChange={onPositionChange}
                onContentChange={onContentChange}
                onReset={onReset}
                isMobile={true}
                isExpanded={isExpanded}
                currentRenderedText={currentRenderedText}
              />
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>Select a text element or logo to start editing</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Compact Overlay Selector Component for Mobile
 */
const CompactOverlaySelector = ({ overlays, selectedOverlayId, selectedLogoOverlayId, onSelectOverlay }) => {
  if (overlays.length === 0) return null;

  if (overlays.length === 1) {
    // Single overlay - just show the name with better styling
    return (
      <div className="compact-overlay-selector">
        <div className="text-sm text-gray-600">
          Editing: <span className="font-semibold text-gray-900">{formatOverlayDisplayName(overlays[0].id)}</span>
        </div>
      </div>
    );
  }

  // Multiple overlays - show compact dropdown
  // Determine the currently selected overlay (could be text or logo)
  const currentSelectedId = selectedLogoOverlayId || selectedOverlayId || '';

  return (
    <div className="compact-overlay-selector">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600 whitespace-nowrap">Editing:</span>
        <select
          value={currentSelectedId}
          onChange={(e) => onSelectOverlay(e.target.value)}
          className="flex-1 text-sm bg-transparent border-none focus:ring-0 focus:outline-none font-semibold text-gray-900 cursor-pointer"
        >
          {overlays.map((overlay) => (
            <option key={overlay.id} value={overlay.id}>
              {formatOverlayDisplayName(overlay.id)}
            </option>
          ))}
        </select>
        <ChevronDown className="w-4 h-4 text-gray-400 pointer-events-none" />
      </div>
    </div>
  );
};

/**
 * Desktop Editor Panel Component
 */
const DesktopEditorPanel = ({
  isCollapsed,
  onToggleCollapse,
  overlays,
  selectedOverlayId,
  selectedLogoOverlayId,
  onSelectOverlay,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onContentChange,
  onReset,
  onResetAll,
  onUndo,
  canUndo,
  // Logo editing props
  isEditingLogo,
  isEditingText,
  selectedOverlay,
  selectedLogoOverlay,
  userLogos,
  logoLoading,
  logoError,
  uploadMode,
  onLogoSelect,
  onLogoUpload,
  onDeleteLogo,
  onDeleteOverlay,
  onDeleteTextOverlay,
  onToggleUpload,
  onSetAsDefault,
  // Logo resize props
  logoCustomizations,
  onLogoResize,
  onAspectRatioToggle,
  // Main toggle function for consistent behavior
  onToggleVisibility,
  // Additional props for content editing
  overlay,
  documentData,
  overlayCustomizations,
  currentRenderedText
}) => {
  const panelWidth = isCollapsed ? '48px' : '320px';

  return (
    <div
      className="fixed right-0 bg-white border-l border-gray-200 shadow-lg transition-all duration-300 z-40"
      style={{ 
        width: panelWidth,
        top: '4rem', // Start below the document workflow header (64px)
        height: 'calc(100vh - 4rem)' // Full height minus header height
      }}
    >
      {/* Collapse Toggle - Now uses main toggle for consistent layout behavior */}
      <button
        onClick={() => {
          // Use main toggle visibility for consistent layout shifts
          if (onToggleVisibility) {
            onToggleVisibility(false);
          } else {
            // Fallback to local collapse behavior
            onToggleCollapse();
          }
        }}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full bg-white border border-gray-200 rounded-l-lg p-2 shadow-md hover:bg-gray-50 transition-colors"
        title="Close editor panel (same as Edit Text button)"
      >
        {isCollapsed ? (
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        ) : (
          <ChevronRight className="w-4 h-4 text-gray-600" />
        )}
      </button>

      {!isCollapsed && (
        <div className="flex flex-col h-full">
          {/* Panel Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">
                {isEditingLogo ? 'Edit Logo' : 'Edit Text Style'}
              </h3>
              <div className="flex items-center space-x-1">
                {/* Delete Button */}
                {selectedOverlayId && onDeleteOverlay && !isEditingLogo && (
                  <button
                    onClick={onDeleteTextOverlay}
                    className="p-2 text-red-500 hover:text-red-700 transition-colors"
                    title="Delete this overlay"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
                {/* Undo Button */}
                {canUndo && (
                  <button
                    onClick={onUndo}
                    className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                    title="Undo last change"
                  >
                    <Undo2 className="w-4 h-4" />
                  </button>
                )}
                {/* Reset All Button */}
                <button
                  onClick={onResetAll}
                  className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                  title="Reset all changes"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Overlay Selection */}
            <OverlaySelector
              overlays={overlays}
              selectedOverlayId={selectedOverlayId}
              selectedLogoOverlayId={selectedLogoOverlayId}
              onSelectOverlay={onSelectOverlay}
              isMobile={false}
            />
          </div>

          {/* Editor Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {isEditingLogo ? (
              <LogoEditorContent
                selectedOverlay={selectedLogoOverlay}
                userLogos={userLogos}
                loading={logoLoading}
                error={logoError}
                uploadMode={uploadMode}
                onLogoSelect={onLogoSelect}
                onLogoUpload={onLogoUpload}
                onDeleteLogo={onDeleteLogo}
                onToggleUpload={onToggleUpload}
                logoCustomizations={logoCustomizations}
                onLogoResize={onLogoResize}
                onAspectRatioToggle={onAspectRatioToggle}
              />
            ) : isEditingText ? (
              <EditorContent
                overlay={selectedOverlay}
                documentData={documentData}
                customizations={overlayCustomizations}
                currentStyling={currentStyling}
                currentPosition={currentPosition}
                onStylingChange={onStylingChange}
                onPositionChange={onPositionChange}
                onContentChange={onContentChange}
                onReset={onReset}
                isMobile={false}
                currentRenderedText={currentRenderedText}
              />
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>Select a text element or logo to start editing</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Overlay Selector Component
 */
const OverlaySelector = ({ overlays, selectedOverlayId, selectedLogoOverlayId, onSelectOverlay, isMobile }) => {
  if (overlays.length === 0) return null;

  // Use dropdown layout for desktop to save space (consistent with mobile UX)
  const useDropdownLayout = !isMobile;
  
  // Use compact layout for desktop when there are many overlays (fallback)
  const useCompactLayout = !isMobile && overlays.length > 4;

  if (useDropdownLayout) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Text Overlay
        </label>
        <select
          value={selectedOverlayId || selectedLogoOverlayId || ''}
          onChange={(e) => {
            if (e.target.value) {
              onSelectOverlay(e.target.value);
            }
          }}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
        >
          <option value="" disabled>Choose overlay...</option>
          {overlays.map((overlay) => (
            <option key={overlay.id} value={overlay.id}>
              {getOverlayDisplayLabel(overlay)}
            </option>
          ))}
        </select>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Select Text Element
      </label>
      <div className={`grid gap-2 ${
        isMobile 
          ? 'grid-cols-1' 
          : useCompactLayout 
            ? 'grid-cols-2' 
            : 'grid-cols-1'
      }`}>
        {overlays.map((overlay) => (
          <button
            key={overlay.id}
            onClick={() => {
              onSelectOverlay(overlay.id);
            }}
            className={`${
              useCompactLayout ? 'p-2' : 'p-3'
            } text-left rounded-lg border transition-colors ${
              selectedOverlayId === overlay.id || selectedLogoOverlayId === overlay.id
                ? 'border-blue-500 bg-blue-50 text-blue-900'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
            }`}
            title={overlay.placeholder ? `Content: ${overlay.placeholder}` : undefined}
          >
            <div className={`font-medium flex items-center ${
              useCompactLayout ? 'text-sm' : ''
            }`}>
              {overlay.type === 'logo' && (
                <svg className="w-3 h-3 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              )}
              {formatOverlayDisplayName(overlay.id)}
            </div>
            {/* Placeholder text removed for space efficiency */}
          </button>
        ))}
      </div>
    </div>
  );
};

/**
 * Editor Content Component
 */
const EditorContent = ({
  overlay,
  documentData,
  customizations,
  currentStyling,
  currentPosition,
  onStylingChange,
  onPositionChange,
  onContentChange,
  onReset,
  isMobile,
  isExpanded = true,
  currentRenderedText = null
}) => {
  // Priority controls (always visible)
  const priorityControls = (
    <>
      {/* Content Editor - Most important for custom text */}
      {overlay && (
        <ContentEditor
          overlay={overlay}
          documentData={documentData}
          customizations={customizations}
          onContentChange={onContentChange}
          isMobile={isMobile}
          currentRenderedText={currentRenderedText}
        />
      )}

      {/* Font Size - Second most important for mobile */}
      <FontSizeEditor
        value={currentStyling.fontSize || 16}
        onChange={(value) => onStylingChange('fontSize', value)}
        isMobile={isMobile}
      />

      {/* Color - Third most important */}
      <ColorEditor
        value={currentStyling.color || '#000000'}
        onChange={(value) => onStylingChange('color', value)}
        isMobile={isMobile}
      />

      {/* Text Alignment - Fourth priority */}
      <AlignmentEditor
        value={currentStyling.textAlign || 'left'}
        onChange={(value) => onStylingChange('textAlign', value)}
        isMobile={isMobile}
      />
    </>
  );

  // Advanced controls (shown when expanded or on desktop)
  const advancedControls = (
    <>
      {/* Font Family */}
      <FontFamilyEditor
        value={currentStyling.fontFamily || 'Arial'}
        onChange={(value) => onStylingChange('fontFamily', value)}
        isMobile={isMobile}
      />

      {/* Font Style */}
      <StyleEditor
        fontWeight={currentStyling.fontWeight || 'normal'}
        fontStyle={currentStyling.fontStyle || 'normal'}
        onFontWeightChange={(value) => onStylingChange('fontWeight', value)}
        onFontStyleChange={(value) => onStylingChange('fontStyle', value)}
        isMobile={isMobile}
      />

      {/* Position */}
      <PositionEditor
        position={currentPosition}
        onChange={onPositionChange}
        isMobile={isMobile}
      />
    </>
  );

  return (
    <div className={`space-y-${isMobile ? '4' : '6'}`}>
      {/* Always show priority controls */}
      {priorityControls}

      {/* Show advanced controls based on expansion state */}
      {(!isMobile || isExpanded) && (
        <>
          {/* Separator for mobile when showing advanced controls */}
          {isMobile && (
            <div className="mobile-advanced-separator">
              <span>Advanced Options</span>
            </div>
          )}
          {advancedControls}
        </>
      )}

      {/* Show expand hint on mobile when collapsed */}
      {isMobile && !isExpanded && (
        <div className="text-center py-3">
          <div className="mobile-expand-hint text-xs text-gray-500 flex items-center justify-center space-x-1">
            <ChevronUp className="w-3 h-3" />
            <span>Pull up for more options</span>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Logo Editor Content Component
 * Embedded logo editing functionality for unified editor
 */
const LogoEditorContent = ({
  selectedOverlay,
  userLogos,
  loading,
  error,
  uploadMode,
  onLogoSelect,
  onLogoUpload,
  onDeleteLogo,
  onToggleUpload,
  // New props for logo resizing
  logoCustomizations = {},
  onLogoResize,
  onAspectRatioToggle
}) => {
  if (!selectedOverlay) {
    return (
      <div className="text-center py-8">
        <Icon name="Image" size={48} className="mx-auto mb-4 text-gray-300" />
        <h4 className="text-lg font-medium text-gray-900 mb-2">Select a Logo Area</h4>
        <p className="text-gray-500">Click on a logo area in the template to edit it.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          {error}
        </div>
      )}

      {/* Current Logo Area Info */}
      <div>
        <h4 className="font-medium text-gray-900 mb-2">Editing: {formatOverlayDisplayName(selectedOverlay.id)}</h4>
        <p className="text-sm text-gray-500">
          Position: {selectedOverlay.position.x}, {selectedOverlay.position.y} •
          Size: {selectedOverlay.position.width}×{selectedOverlay.position.height}
        </p>
      </div>

      {/* Logo Size Controls - show when a logo is selected OR when there's a default logo */}
      {(() => {
        const currentCustomization = logoCustomizations[selectedOverlay.id] || {};
        const hasCustomLogo = currentCustomization.selectedLogoId;

        // Check if there's a default logo available (similar to how text overlays work with placeholders)
        // A logo overlay should be editable if it has a placeholder (indicating it can render a default logo)
        const hasDefaultLogo = selectedOverlay.placeholder === '{{logo}}' || selectedOverlay.placeholder?.includes('logo');

        // Show controls if user has selected a custom logo OR if there's a default logo available
        const hasLogo = hasCustomLogo || hasDefaultLogo;
        const currentPosition = currentCustomization.position || selectedOverlay.position;

        return hasLogo && onLogoResize && (
          <LogoSizeEditor
            width={currentPosition.width}
            height={currentPosition.height}
            originalWidth={selectedOverlay.position.width}
            originalHeight={selectedOverlay.position.height}
            constraints={selectedOverlay.constraints}
            maintainAspectRatio={currentCustomization.maintainAspectRatio !== false}
            onChange={(sizeChanges) => onLogoResize(selectedOverlay.id, sizeChanges)}
            onAspectRatioToggle={(maintain) => onAspectRatioToggle?.(selectedOverlay.id, maintain)}
            isMobile={false}
          />
        );
      })()}

      {/* Upload Mode */}
      {uploadMode ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">Upload New Logo</h4>
            <Button
              variant="ghost"
              onClick={onToggleUpload}
              iconName="X"
              size="sm"
            />
          </div>
          
          <LogoUpload
            onFileSelect={onLogoUpload}
            disabled={loading}
            showPreview={true}
          />
        </div>
      ) : (
        <>
          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              variant="primary"
              onClick={onToggleUpload}
              iconName="Upload"
              iconPosition="left"
              disabled={loading}
              className="w-full"
            >
              Upload New Logo
            </Button>
            
            <Button
              variant="outline"
              onClick={onDeleteLogo}
              iconName="Trash2"
              iconPosition="left"
              disabled={loading}
              className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Remove Logo
            </Button>
          </div>

          {/* Logo Library */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Your Logo Library</h4>
            
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Icon name="Loader2" size={24} className="animate-spin text-gray-400" />
              </div>
            ) : userLogos.length > 0 ? (
              <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
                {userLogos.map((logo) => (
                  <div
                    key={logo.id}
                    className="border border-gray-200 rounded-lg p-2 hover:border-green-300 hover:bg-green-50 transition-colors"
                  >
                    <div
                      className="cursor-pointer"
                      onClick={() => onLogoSelect(logo.id)}
                    >
                      <img
                        src={logo.previewUrl || logo.public_url || logo.url}
                        alt={logo.name}
                        className="w-full h-16 object-contain bg-white rounded"
                        onError={(e) => {
                          console.error('🖼️ Logo image failed to load:', {
                            logoId: logo.id,
                            logoName: logo.name,
                            previewUrl: logo.previewUrl,
                            publicUrl: logo.public_url,
                            url: logo.url,
                            fullLogo: logo
                          });
                          e.target.style.display = 'none';
                        }}
                        onLoad={() => {
                          console.log('✅ Logo image loaded successfully:', {
                            logoId: logo.id,
                            logoName: logo.name,
                            src: logo.previewUrl || logo.public_url || logo.url
                          });
                        }}
                      />
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-gray-600 truncate flex-1">{logo.name}</p>
                        {logo.isDefault && (
                          <Icon name="Star" size={12} className="text-blue-500 ml-1" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Icon name="Image" size={32} className="mx-auto mb-2 text-gray-300" />
                <p className="text-sm text-gray-500">No logos in your library</p>
                <Button
                  variant="ghost"
                  onClick={onToggleUpload}
                  className="mt-2 text-sm"
                >
                  Upload your first logo
                </Button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default TextOverlayEditor;
