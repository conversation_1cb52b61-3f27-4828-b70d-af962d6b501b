import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

const ProtectedRoute = ({ children, requireProfile = false }) => {
  const authContext = useAuth()
  
  // Defensive check - ensure auth context is available
  if (!authContext) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Authentication Error</p>
          <p className="text-sm text-text-secondary">Please refresh the page</p>
        </div>
      </div>
    )
  }

  const { isAuthenticated, hasProfile, loading } = authContext
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading...</p>
          <p className="mt-2 text-xs text-text-muted">Please wait a moment...</p>
        </div>
      </div>
    )
  }

  // Redirect to auth page if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/auth" state={{ from: location }} replace />
  }

  // Redirect to profile setup if profile is required but doesn't exist
  if (requireProfile && !hasProfile) {
    return <Navigate to="/profile-setup" state={{ from: location }} replace />
  }

  return children
}

export default ProtectedRoute
