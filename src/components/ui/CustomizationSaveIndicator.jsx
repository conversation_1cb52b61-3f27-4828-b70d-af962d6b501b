/**
 * Template Customization Save Indicator
 * Shows visual feedback for save state of template customizations
 */
import React from 'react';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

const CustomizationSaveIndicator = ({ saveState, className = '' }) => {
  if (!saveState || saveState === 'saved') return null;

  const getIcon = () => {
    switch (saveState) {
      case 'saving':
        return <Loader2 className="w-3 h-3 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-3 h-3 text-red-500" />;
      default:
        return <CheckCircle className="w-3 h-3 text-green-500" />;
    }
  };

  const getText = () => {
    switch (saveState) {
      case 'saving':
        return 'Saving changes...';
      case 'error':
        return 'Save failed - retrying...';
      default:
        return 'Changes saved';
    }
  };

  return (
    <div className={`flex items-center gap-1 text-xs text-gray-500 ${className}`}>
      {getIcon()}
      <span>{getText()}</span>
    </div>
  );
};

export default CustomizationSaveIndicator;
