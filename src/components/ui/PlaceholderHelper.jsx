/**
 * Placeholder Helper Component
 * Provides a dropdown with available placeholders (document and profile) for easy insertion
 */

import React, { useState, useRef, useEffect } from 'react';
import ProfileDataService from '../../services/profileDataService.js';
import Icon from '../AppIcon.jsx';

const PlaceholderHelper = ({ 
  onInsertPlaceholder, 
  className = '',
  buttonText = 'Insert Placeholder',
  size = 'sm' 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Get available placeholders
  const documentPlaceholders = ProfileDataService.getAvailableDocumentPlaceholders();
  const profilePlaceholders = ProfileDataService.getAvailableProfilePlaceholders();

  // Filter placeholders based on search term
  const filteredDocumentPlaceholders = documentPlaceholders.filter(p =>
    p.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.placeholder.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredProfilePlaceholders = profilePlaceholders.filter(p =>
    p.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    p.placeholder.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInsertPlaceholder = (placeholder) => {
    onInsertPlaceholder(placeholder);
    setIsOpen(false);
    setSearchTerm('');
  };

  const renderPlaceholderGroup = (title, placeholders, bgColor = 'bg-gray-50') => {
    // Defensive programming: validate parameters
    if (!title || !placeholders || !Array.isArray(placeholders) || placeholders.length === 0) {
      return null;
    }

    return (
      <div className="mb-4">
        <div className={`px-3 py-2 text-xs font-medium text-gray-600 ${bgColor} border-b`}>
          {title}
        </div>
        <div className="max-h-32 overflow-y-auto">
          {placeholders.map((item) => (
            <button
              key={item.placeholder}
              onClick={() => handleInsertPlaceholder(item.placeholder)}
              className="w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
            >
              <div className="flex justify-between items-start gap-2">
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-gray-900 truncate">
                    {item.label}
                  </div>
                  <div className="text-xs text-blue-600 font-mono">
                    {item.placeholder}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {item.description}
                  </div>
                </div>
                <div className="text-xs text-gray-400 italic flex-shrink-0">
                  {item.example}
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    );
  };

  const buttonSizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <div className={`relative inline-block ${className}`}>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className={`
          ${buttonSizeClasses[size]}
          bg-blue-50 text-blue-700 border border-blue-200 rounded-md 
          hover:bg-blue-100 hover:border-blue-300 
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
          transition-colors duration-200
          flex items-center gap-2
        `}
        type="button"
      >
        <Icon name="Code" size={size === 'xs' ? 12 : size === 'sm' ? 14 : 16} />
        {buttonText}
        <Icon 
          name={isOpen ? "ChevronUp" : "ChevronDown"} 
          size={size === 'xs' ? 10 : size === 'sm' ? 12 : 14} 
        />
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-hidden"
          style={{ 
            left: '0',
            top: '100%'
          }}
        >
          {/* Search Input */}
          <div className="p-3 border-b border-gray-100">
            <div className="relative">
              <Icon 
                name="Search" 
                size={14} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
              />
              <input
                type="text"
                placeholder="Search placeholders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
              />
            </div>
          </div>

          {/* Placeholder Groups */}
          <div className="overflow-y-auto max-h-80">
            {searchTerm === '' || filteredProfilePlaceholders.length > 0 ? (
              renderPlaceholderGroup(
                "Profile Data", 
                filteredProfilePlaceholders, 
                "bg-green-50"
              )
            ) : null}

            {searchTerm === '' || filteredDocumentPlaceholders.length > 0 ? (
              renderPlaceholderGroup(
                "Document Data", 
                filteredDocumentPlaceholders, 
                "bg-blue-50"
              )
            ) : null}

            {/* No results message */}
            {searchTerm !== '' && 
             filteredDocumentPlaceholders.length === 0 && 
             filteredProfilePlaceholders.length === 0 && (
              <div className="p-4 text-center text-gray-500">
                <Icon name="Search" size={24} className="mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No placeholders found for "{searchTerm}"</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 bg-gray-50 border-t border-gray-100">
            <p className="text-xs text-gray-600 text-center">
              Click any placeholder to insert it into your text
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaceholderHelper;