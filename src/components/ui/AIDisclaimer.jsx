import React, { useState, useEffect } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

/**
 * AI Disclaimer Component
 * 
 * Shows a warning banner to users about AI-generated content accuracy
 * and the importance of proofreading. Appears in the document editor
 * during the edit phase.
 */
const AIDisclaimer = ({
  variant = 'banner', // 'banner' | 'modal' | 'toast'
  onDismiss = null,
  persistent = false, // If true, disclaimer will reappear even if dismissed
  className = '',
  showProofreadTips = false
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isDismissed, setIsDismissed] = useState(false);
  const [showTips, setShowTips] = useState(false);

  // Check if user has previously dismissed this disclaimer
  useEffect(() => {
    if (!persistent) {
      const dismissed = localStorage.getItem('docforge_ai_disclaimer_dismissed');
      if (dismissed === 'true') {
        setIsVisible(false);
        setIsDismissed(true);
      }
    }
  }, [persistent]);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    
    // Save dismissal state unless persistent
    if (!persistent) {
      localStorage.setItem('docforge_ai_disclaimer_dismissed', 'true');
    }
    
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleShowTips = () => {
    setShowTips(!showTips);
  };

  if (!isVisible) return null;

  const proofreadingTips = [
    "Check facts, figures, and statistics for accuracy",
    "Verify proper grammar, spelling, and punctuation",
    "Ensure the tone matches your intended audience",
    "Review for logical flow and coherence",
    "Confirm all claims are properly supported"
  ];

  // Banner variant (default)
  if (variant === 'banner') {
    return (
      <div className={`bg-amber-50 border border-amber-200 rounded-lg ${className}`}>
        <div className="p-4">
          <div className="flex items-start space-x-3">
            {/* Warning Icon */}
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center">
                <Icon name="AlertTriangle" size={18} color="var(--color-warning)" />
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-sm font-semibold text-amber-800 mb-1">
                    AI-Generated Content Notice
                  </h3>
                  <p className="text-sm text-amber-700 leading-relaxed">
                    This content was generated using AI and may contain errors or inaccuracies. 
                    <strong className="font-medium"> Please carefully proofread and verify all information</strong> before 
                    using or publishing this document.
                  </p>
                  
                  {showProofreadTips && (
                    <div className="mt-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleShowTips}
                        className="text-amber-700 hover:text-amber-800 hover:bg-amber-100 p-1 -ml-1"
                      >
                        <Icon name={showTips ? "ChevronUp" : "ChevronDown"} size={14} className="mr-1" />
                        {showTips ? "Hide" : "Show"} proofreading tips
                      </Button>
                      
                      {showTips && (
                        <div className="mt-2 bg-amber-100 rounded-md p-3">
                          <p className="text-xs font-medium text-amber-800 mb-2">
                            📝 Proofreading Checklist:
                          </p>
                          <ul className="text-xs text-amber-700 space-y-1">
                            {proofreadingTips.map((tip, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-amber-600 mr-2">•</span>
                                <span>{tip}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Dismiss Button */}
                <div className="flex-shrink-0 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDismiss}
                    className="text-amber-600 hover:text-amber-800 hover:bg-amber-100 p-1"
                    title="Dismiss disclaimer"
                  >
                    <Icon name="X" size={16} />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Toast variant
  if (variant === 'toast') {
    return (
      <div className={`fixed top-20 right-4 z-50 max-w-md bg-white rounded-lg shadow-lg border border-amber-200 ${className}`}>
        <div className="p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Icon name="AlertTriangle" size={20} color="var(--color-warning)" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 mb-1">
                AI Content Notice
              </p>
              <p className="text-sm text-gray-600">
                Please proofread AI-generated content for accuracy.
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <Icon name="X" size={16} />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Modal variant
  if (variant === 'modal') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className={`bg-white rounded-lg shadow-xl max-w-md w-full ${className}`}>
          <div className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center">
                  <Icon name="AlertTriangle" size={24} color="var(--color-warning)" />
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Important: AI-Generated Content
                </h3>
                <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                  The content in this document was generated using artificial intelligence. 
                  While AI can create helpful drafts, it may include errors, inaccuracies, 
                  or outdated information.
                </p>
                <p className="text-sm text-gray-600 mb-4">
                  <strong>Please carefully review and verify all content</strong> before 
                  using or sharing this document.
                </p>
                
                {showProofreadTips && (
                  <div className="bg-gray-50 rounded-md p-3 mb-4">
                    <p className="text-xs font-medium text-gray-800 mb-2">
                      Quick Proofreading Tips:
                    </p>
                    <ul className="text-xs text-gray-600 space-y-1">
                      {proofreadingTips.slice(0, 3).map((tip, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-gray-400 mr-2">•</span>
                          <span>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={handleShowTips}
                className="text-sm"
              >
                {showTips ? "Hide" : "Show"} Tips
              </Button>
              <Button
                onClick={handleDismiss}
                className="text-sm"
              >
                I Understand
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default AIDisclaimer;
