import React, { useState, useEffect } from 'react';
import Button from '../ui/Button';
import Icon from '../AppIcon';
import LogoUpload from '../ui/LogoUpload';
import ConfirmationModal from '../ui/ConfirmationModal';
import { useAuth } from '../../contexts/AuthContext';
import { getUserLogos, uploadLogo } from '../../services/logoService';
import { templateHasLogoOverlays, getLogoOverlays } from '../../services/logoOverlayService';
import { prodLogger } from '../../utils/prodLogger.js';

/**
 * Logo Overlay Editor Component
 * Provides simplified logo editing interface for templates with logo overlays
 * Mirrors TextOverlayEditor structure but with basic operations only
 */
const LogoOverlayEditor = ({
  template = null,
  selectedLogoOverlayId = null,
  logoCustomizations = {},
  onLogoChange = null,
  onLogoDelete = null,
  onReset = null,
  onUndo = null,
  canUndo = false,
  isVisible = true,
  onToggleVisibility = null,
  className = ''
}) => {
  const { user } = useAuth();
  const [userLogos, setUserLogos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadMode, setUploadMode] = useState(false);
  const [error, setError] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // Confirmation modal state
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    isLoading: false
  });

  // Get logo overlays from template
  const hasLogoOverlays = templateHasLogoOverlays(template);
  const logoOverlays = hasLogoOverlays ? getLogoOverlays(template) : [];
  const selectedOverlay = logoOverlays.find(overlay => overlay.id === selectedLogoOverlayId);

  // Load user's logos
  useEffect(() => {
    if (user?.id && isVisible) {
      loadUserLogos();
    }
  }, [user?.id, isVisible]);

  const loadUserLogos = async () => {
    try {
      setLoading(true);
      setError('');
      
      const result = await getUserLogos(user.id, { 
        activeOnly: true, 
        includeDefault: true 
      });
      
      if (result.success) {
        setUserLogos(result.logos);
      } else {
        setError(result.error || 'Failed to load logos');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to load user logos:', err);
      setError('Failed to load logos');
    } finally {
      setLoading(false);
    }
  };

  const handleLogoSelect = (logoId) => {
    if (!selectedLogoOverlayId || !onLogoChange) return;

    const selectedLogo = userLogos.find(logo => logo.id === logoId);
    if (selectedLogo) {
      onLogoChange(selectedLogoOverlayId, selectedLogo);
      setUploadMode(false);
    }
  };

  const handleLogoUpload = async (file, validationResult) => {
    if (!validationResult.success) {
      setError(validationResult.error);
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      const uploadResult = await uploadLogo(file, user.id, {
        name: file.name.replace(/\.[^/.]+$/, ''),
        description: 'Logo uploaded from template editor'
      });
      
      if (uploadResult.success) {
        // Refresh logo list
        await loadUserLogos();
        
        // Auto-select the newly uploaded logo
        if (selectedLogoOverlayId && onLogoChange) {
          onLogoChange(selectedLogoOverlayId, uploadResult.logo);
        }
        
        setUploadMode(false);
      } else {
        setError(uploadResult.error || 'Failed to upload logo');
      }
    } catch (err) {
      prodLogger.error('❌ Failed to upload logo:', err);
      setError('Failed to upload logo');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLogo = () => {
    if (!selectedLogoOverlayId || !onLogoDelete) return;
    
    setConfirmModal({
      isOpen: true,
      isLoading: false
    });
  };

  const handleConfirmDelete = async () => {
    if (!selectedLogoOverlayId || !onLogoDelete) return;

    setConfirmModal(prev => ({ ...prev, isLoading: true }));

    try {
      onLogoDelete(selectedLogoOverlayId);
      setConfirmModal({ isOpen: false, isLoading: false });
    } catch (error) {
      prodLogger.error('Error removing logo:', error);
      setConfirmModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleCloseConfirmModal = () => {
    if (!confirmModal.isLoading) {
      setConfirmModal({ isOpen: false, isLoading: false });
    }
  };

  // Don't render if template doesn't support logo overlays
  if (!hasLogoOverlays) {
    return null;
  }

  // Mobile version (simplified)
  if (window.innerWidth < 1024) {
    return (
      <div className={`logo-overlay-editor-mobile ${className}`}>
        {isVisible && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
            <div className="fixed bottom-0 left-0 right-0 bg-white rounded-t-lg max-h-[80vh] overflow-hidden">
              {/* Mobile Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="font-medium text-gray-900">Logo Editor</h3>
                <Button
                  variant="ghost"
                  onClick={() => onToggleVisibility?.(false)}
                  iconName="X"
                  size="sm"
                />
              </div>

              {/* Mobile Content */}
              <div className="p-4 overflow-y-auto">
                <MobileLogoEditor
                  selectedOverlay={selectedOverlay}
                  userLogos={userLogos}
                  loading={loading}
                  error={error}
                  uploadMode={uploadMode}
                  onLogoSelect={handleLogoSelect}
                  onLogoUpload={handleLogoUpload}
                  onDeleteLogo={handleDeleteLogo}
                  onToggleUpload={() => setUploadMode(!uploadMode)}
                  onUndo={onUndo}
                  canUndo={canUndo}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Desktop version
  return (
    <div className={`logo-overlay-editor bg-white border-l border-gray-200 ${className}`}>
      {isVisible && (
        <div className="w-80 h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Icon name="Image" size={20} className="text-green-600" />
              <h3 className="font-medium text-gray-900">Logo Editor</h3>
            </div>
            <div className="flex items-center gap-2">
              {canUndo && (
                <Button
                  variant="ghost"
                  onClick={onUndo}
                  iconName="Undo2"
                  size="sm"
                  title="Undo last change"
                />
              )}
              <Button
                variant="ghost"
                onClick={() => setIsCollapsed(!isCollapsed)}
                iconName={isCollapsed ? "ChevronUp" : "ChevronDown"}
                size="sm"
              />
            </div>
          </div>

          {!isCollapsed && (
            <>
              {/* Logo Overlay Selection */}
              <div className="p-4 border-b border-gray-200 bg-gray-50">
                <LogoOverlaySelector
                  logoOverlays={logoOverlays}
                  selectedLogoOverlayId={selectedLogoOverlayId}
                  logoCustomizations={logoCustomizations}
                />
              </div>

              {/* Editor Content */}
              <div className="flex-1 overflow-y-auto p-4">
                {selectedOverlay ? (
                  <LogoEditorContent
                    selectedOverlay={selectedOverlay}
                    userLogos={userLogos}
                    loading={loading}
                    error={error}
                    uploadMode={uploadMode}
                    onLogoSelect={handleLogoSelect}
                    onLogoUpload={handleLogoUpload}
                    onDeleteLogo={handleDeleteLogo}
                    onToggleUpload={() => setUploadMode(!uploadMode)}
                  />
                ) : (
                  <div className="text-center py-8">
                    <Icon name="Image" size={48} className="mx-auto mb-4 text-gray-300" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Select a Logo Area</h4>
                    <p className="text-gray-500">Click on a logo area in the template to edit it.</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      )}
      
      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmModal.isOpen}
        onClose={handleCloseConfirmModal}
        onConfirm={handleConfirmDelete}
        title="Remove Logo"
        message="Are you sure you want to remove this logo from the template?"
        confirmText="Remove"
        cancelText="Cancel"
        type="warning"
        isLoading={confirmModal.isLoading}
      />
    </div>
  );
};

/**
 * Logo Overlay Selector Component
 */
const LogoOverlaySelector = ({ logoOverlays, selectedLogoOverlayId, logoCustomizations }) => {
  if (logoOverlays.length === 0) return null;

  return (
    <div>
      <h4 className="text-sm font-medium text-gray-700 mb-2">Logo Areas</h4>
      <div className="space-y-1">
        {logoOverlays.map((overlay) => {
          const isSelected = selectedLogoOverlayId === overlay.id;
          const hasLogo = logoCustomizations[overlay.id]?.selectedLogoId;
          
          return (
            <div
              key={overlay.id}
              className={`flex items-center gap-2 p-2 rounded text-sm ${
                isSelected 
                  ? 'bg-green-50 text-green-700 border border-green-200' 
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Icon 
                name={hasLogo ? "Image" : "ImageOff"} 
                size={16} 
                className={hasLogo ? "text-green-500" : "text-gray-400"} 
              />
              <span className="flex-1">{overlay.id}</span>
              {hasLogo && (
                <Icon name="Check" size={14} className="text-green-500" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * Logo Editor Content Component
 */
const LogoEditorContent = ({
  selectedOverlay,
  userLogos,
  loading,
  error,
  uploadMode,
  onLogoSelect,
  onLogoUpload,
  onDeleteLogo,
  onToggleUpload
}) => {
  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          {error}
        </div>
      )}

      {/* Current Logo Area Info */}
      <div>
        <h4 className="font-medium text-gray-900 mb-2">Editing: {selectedOverlay.id}</h4>
        <p className="text-sm text-gray-500">
          Position: {selectedOverlay.position.x}, {selectedOverlay.position.y} • 
          Size: {selectedOverlay.position.width}×{selectedOverlay.position.height}
        </p>
      </div>

      {/* Upload Mode */}
      {uploadMode ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">Upload New Logo</h4>
            <Button
              variant="ghost"
              onClick={onToggleUpload}
              iconName="X"
              size="sm"
            />
          </div>
          
          <LogoUpload
            onFileSelect={onLogoUpload}
            disabled={loading}
            showPreview={true}
          />
        </div>
      ) : (
        <>
          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              variant="primary"
              onClick={onToggleUpload}
              iconName="Upload"
              iconPosition="left"
              disabled={loading}
              className="w-full"
            >
              Upload New Logo
            </Button>
            
            <Button
              variant="outline"
              onClick={onDeleteLogo}
              iconName="Trash2"
              iconPosition="left"
              disabled={loading}
              className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Remove Logo
            </Button>
          </div>

          {/* Logo Library */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Your Logo Library</h4>
            
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Icon name="Loader2" size={24} className="animate-spin text-gray-400" />
              </div>
            ) : userLogos.length > 0 ? (
              <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
                {userLogos.map((logo) => (
                  <div
                    key={logo.id}
                    className="border border-gray-200 rounded-lg p-2 cursor-pointer hover:border-green-300 hover:bg-green-50 transition-colors"
                    onClick={() => onLogoSelect(logo.id)}
                  >
                    {/* Square Logo Preview Container */}
                    <div className="aspect-square bg-white rounded border border-gray-100 p-2 flex items-center justify-center mb-2">
                      <img
                        src={logo.previewUrl}
                        alt={logo.name}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                    <p className="text-xs text-gray-600 truncate">{logo.name}</p>
                    {logo.isDefault && (
                      <div className="flex items-center gap-1 mt-1">
                        <Icon name="Star" size={10} className="text-blue-500" />
                        <span className="text-xs text-blue-700">Default</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Icon name="Image" size={32} className="mx-auto mb-2 text-gray-300" />
                <p className="text-sm text-gray-500">No logos in your library</p>
                <Button
                  variant="ghost"
                  onClick={onToggleUpload}
                  className="mt-2 text-sm"
                >
                  Upload your first logo
                </Button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

/**
 * Mobile Logo Editor Component
 */
const MobileLogoEditor = ({
  selectedOverlay,
  userLogos,
  loading,
  error,
  uploadMode,
  onLogoSelect,
  onLogoUpload,
  onDeleteLogo,
  onToggleUpload,
  onUndo,
  canUndo
}) => {
  return (
    <div className="space-y-4">
      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          {error}
        </div>
      )}

      {/* Quick Actions */}
      <div className="flex gap-2">
        <Button
          variant="primary"
          onClick={onToggleUpload}
          iconName="Upload"
          size="sm"
          className="flex-1"
        >
          {uploadMode ? 'Cancel' : 'Upload'}
        </Button>
        
        <Button
          variant="outline"
          onClick={onDeleteLogo}
          iconName="Trash2"
          size="sm"
          className="text-red-600"
        >
          Remove
        </Button>
        
        {canUndo && (
          <Button
            variant="ghost"
            onClick={onUndo}
            iconName="Undo2"
            size="sm"
          />
        )}
      </div>

      {/* Content */}
      {uploadMode ? (
        <LogoUpload
          onFileSelect={onLogoUpload}
          disabled={loading}
          showPreview={true}
        />
      ) : (
        <div className="grid grid-cols-3 gap-2 max-h-48 overflow-y-auto">
          {userLogos.map((logo) => (
            <div
              key={logo.id}
              className="border border-gray-200 rounded p-1 cursor-pointer hover:border-green-300"
              onClick={() => onLogoSelect(logo.id)}
            >
              {/* Square Logo Preview Container */}
              <div className="aspect-square bg-white rounded border border-gray-100 p-1 flex items-center justify-center">
                <img
                  src={logo.previewUrl}
                  alt={logo.name}
                  className="max-w-full max-h-full object-contain"
                />
              </div>
              {logo.isDefault && (
                <div className="flex items-center justify-center mt-1">
                  <Icon name="Star" size={8} className="text-blue-500" />
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LogoOverlayEditor;
