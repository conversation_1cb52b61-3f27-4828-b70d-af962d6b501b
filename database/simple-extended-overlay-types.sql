-- Simple Extended Overlay Types - Database Enhancement
-- Adds support for website, company, email, phone overlay types
-- Uses existing user_profiles fields without breaking changes

-- NOTE: The user_profiles table already contains the required fields:
-- - full_name TEXT (for {{user_name}})
-- - organization TEXT (for {{company_name}}) 
-- - website TEXT (for {{website}})
-- - phone TEXT (for {{phone_number}})
-- - email TEXT (for {{email_address}})

-- No additional database changes needed!
-- The ProfileDataService already maps these fields correctly.

-- Example overlay types in templates:
-- {
--   "overlays": [
--     {
--       "id": "company_name",
--       "type": "company",
--       "placeholder": "{{company_name}}",
--       "position": {"x": 50, "y": 100, "width": 300, "height": 40},
--       "styling": {"fontSize": 20, "fontWeight": "bold", "color": "#2c3e50"}
--     },
--     {
--       "id": "website",
--       "type": "website", 
--       "placeholder": "{{website}}",
--       "position": {"x": 50, "y": 400, "width": 250, "height": 20},
--       "styling": {"fontSize": 14, "color": "#3498db"}
--     },
--     {
--       "id": "email",
--       "type": "email",
--       "placeholder": "{{email_address}}",
--       "position": {"x": 50, "y": 425, "width": 200, "height": 20},
--       "styling": {"fontSize": 12, "color": "#7f8c8d"}
--     },
--     {
--       "id": "phone",
--       "type": "phone",
--       "placeholder": "{{phone_number}}",
--       "position": {"x": 50, "y": 450, "width": 150, "height": 20},
--       "styling": {"fontSize": 12, "color": "#7f8c8d"}
--     }
--   ]
-- }

-- The ProfileDataService.getProfileValue() method already handles the mapping:
-- 'company_name' -> userProfile.organization
-- 'website' -> userProfile.website  
-- 'phone_number' -> userProfile.phone
-- 'email_address' -> userProfile.email

-- Implementation is complete - just need to update the frontend to support
-- the new overlay types in the admin interface and user forms.
