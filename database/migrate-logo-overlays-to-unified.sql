-- COMPLETE LOGO OVERLAY MIGRATION TO UNIFIED SYSTEM
-- This script migrates all logo overlays from the separate logo_overlays column
-- to the unified text_overlays structure and removes the old column

-- Phase 1: Pre-migration verification
DO $$
DECLARE
    templates_with_logos INTEGER;
    total_logo_overlays INTEGER;
    templates_with_both INTEGER;
BEGIN
    -- Count templates with logo overlays in the old structure
    SELECT COUNT(*) INTO templates_with_logos
    FROM cover_templates 
    WHERE logo_overlays IS NOT NULL 
    AND jsonb_array_length(logo_overlays->'overlays') > 0;
    
    -- Count total logo overlays in old structure
    SELECT COALESCE(SUM(jsonb_array_length(logo_overlays->'overlays')), 0) INTO total_logo_overlays
    FROM cover_templates 
    WHERE logo_overlays IS NOT NULL;
    
    -- Count templates that have both old and new logo structures
    SELECT COUNT(*) INTO templates_with_both
    FROM cover_templates
    WHERE logo_overlays IS NOT NULL 
    AND jsonb_array_length(logo_overlays->'overlays') > 0
    AND EXISTS (
        SELECT 1 FROM jsonb_array_elements(text_overlays->'overlays') as overlay
        WHERE overlay->>'type' = 'logo'
    );
    
    RAISE NOTICE '=== PRE-MIGRATION ANALYSIS ===';
    RAISE NOTICE 'Templates with logo_overlays: %', templates_with_logos;
    RAISE NOTICE 'Total logo overlays in old structure: %', total_logo_overlays;
    RAISE NOTICE 'Templates with both structures: %', templates_with_both;
    RAISE NOTICE '';
    
    IF templates_with_both > 0 THEN
        RAISE NOTICE 'WARNING: % templates have logo overlays in both old and new structures', templates_with_both;
        RAISE NOTICE 'These will need manual review after migration.';
    END IF;
END $$;

-- Phase 2: Create backup table
DROP TABLE IF EXISTS cover_templates_logo_backup;
CREATE TABLE cover_templates_logo_backup AS 
SELECT id, name, logo_overlays, text_overlays, updated_at
FROM cover_templates 
WHERE logo_overlays IS NOT NULL 
AND jsonb_array_length(logo_overlays->'overlays') > 0;

RAISE NOTICE 'Backup table created with % records', (SELECT COUNT(*) FROM cover_templates_logo_backup);

-- Phase 3: Migration function
CREATE OR REPLACE FUNCTION migrate_logo_overlays_to_unified()
RETURNS TABLE(template_id TEXT, status TEXT, details TEXT) AS $$
DECLARE
    template_record RECORD;
    old_logo_overlays JSONB;
    current_text_overlays JSONB;
    existing_logo_overlays JSONB;
    merged_overlays JSONB;
    migration_status TEXT;
    migration_details TEXT;
BEGIN
    -- Process each template with logo overlays
    FOR template_record IN 
        SELECT id, name, logo_overlays, text_overlays
        FROM cover_templates 
        WHERE logo_overlays IS NOT NULL 
        AND jsonb_array_length(logo_overlays->'overlays') > 0
    LOOP
        old_logo_overlays := template_record.logo_overlays->'overlays';
        current_text_overlays := COALESCE(template_record.text_overlays, '{"overlays": []}'::jsonb);
        
        -- Check if text_overlays already has logo overlays
        SELECT jsonb_agg(overlay) INTO existing_logo_overlays
        FROM jsonb_array_elements(current_text_overlays->'overlays') as overlay
        WHERE overlay->>'type' = 'logo';
        
        IF existing_logo_overlays IS NOT NULL AND jsonb_array_length(existing_logo_overlays) > 0 THEN
            -- Template already has logo overlays in text_overlays
            migration_status := 'CONFLICT';
            migration_details := format('Template already has %s logo overlays in text_overlays', 
                                      jsonb_array_length(existing_logo_overlays));
        ELSE
            -- Safe to migrate: append logo overlays to text_overlays
            merged_overlays := jsonb_set(
                current_text_overlays,
                '{overlays}',
                (current_text_overlays->'overlays') || old_logo_overlays
            );
            
            -- Update the template
            UPDATE cover_templates 
            SET text_overlays = merged_overlays,
                updated_at = NOW()
            WHERE id = template_record.id;
            
            migration_status := 'SUCCESS';
            migration_details := format('Migrated %s logo overlays to text_overlays', 
                                      jsonb_array_length(old_logo_overlays));
        END IF;
        
        RETURN QUERY SELECT template_record.id, migration_status, migration_details;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Phase 4: Execute migration
SELECT * FROM migrate_logo_overlays_to_unified();

-- Phase 5: Post-migration verification
DO $$
DECLARE
    migrated_templates INTEGER;
    total_unified_logos INTEGER;
    conflict_templates INTEGER;
    verification_passed BOOLEAN := true;
BEGIN
    -- Count successfully migrated templates
    SELECT COUNT(*) INTO migrated_templates
    FROM cover_templates
    WHERE EXISTS (
        SELECT 1 FROM jsonb_array_elements(text_overlays->'overlays') as overlay
        WHERE overlay->>'type' = 'logo'
    );
    
    -- Count total logo overlays in unified structure
    SELECT COALESCE(SUM(
        (SELECT COUNT(*) FROM jsonb_array_elements(text_overlays->'overlays') as overlay
         WHERE overlay->>'type' = 'logo')
    ), 0) INTO total_unified_logos
    FROM cover_templates;
    
    -- Count templates with conflicts (still have both structures)
    SELECT COUNT(*) INTO conflict_templates
    FROM cover_templates
    WHERE logo_overlays IS NOT NULL 
    AND jsonb_array_length(logo_overlays->'overlays') > 0
    AND EXISTS (
        SELECT 1 FROM jsonb_array_elements(text_overlays->'overlays') as overlay
        WHERE overlay->>'type' = 'logo'
    );
    
    RAISE NOTICE '';
    RAISE NOTICE '=== POST-MIGRATION VERIFICATION ===';
    RAISE NOTICE 'Templates with logo overlays in text_overlays: %', migrated_templates;
    RAISE NOTICE 'Total logo overlays in unified structure: %', total_unified_logos;
    RAISE NOTICE 'Templates with conflicts remaining: %', conflict_templates;
    
    IF conflict_templates > 0 THEN
        verification_passed := false;
        RAISE WARNING 'Migration incomplete: % templates still have conflicts', conflict_templates;
        RAISE NOTICE 'Manual review required for these templates.';
    END IF;
    
    IF verification_passed AND conflict_templates = 0 THEN
        RAISE NOTICE 'Migration verification PASSED - Safe to remove logo_overlays column';
    ELSE
        RAISE WARNING 'Migration verification FAILED - Do NOT remove logo_overlays column yet';
    END IF;
END $$;

-- Phase 6: Show final template states
SELECT 
    'FINAL MIGRATION SUMMARY' as summary,
    id,
    name,
    CASE 
        WHEN logo_overlays IS NOT NULL AND jsonb_array_length(logo_overlays->'overlays') > 0 THEN 'HAS_OLD_STRUCTURE'
        ELSE 'NO_OLD_STRUCTURE'
    END as old_structure_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM jsonb_array_elements(text_overlays->'overlays') as overlay WHERE overlay->>'type' = 'logo') THEN 'HAS_NEW_STRUCTURE'
        ELSE 'NO_NEW_STRUCTURE'
    END as new_structure_status,
    (SELECT COUNT(*) FROM jsonb_array_elements(text_overlays->'overlays') as overlay WHERE overlay->>'type' = 'logo') as unified_logo_count
FROM cover_templates
WHERE logo_overlays IS NOT NULL 
OR EXISTS (SELECT 1 FROM jsonb_array_elements(text_overlays->'overlays') as overlay WHERE overlay->>'type' = 'logo')
ORDER BY name;

-- Clean up migration function
DROP FUNCTION migrate_logo_overlays_to_unified();

-- Instructions for next steps
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== NEXT STEPS ===';
    RAISE NOTICE '1. Review the migration summary above';
    RAISE NOTICE '2. Test the application with the migrated data';
    RAISE NOTICE '3. If everything works correctly, run the column removal script';
    RAISE NOTICE '4. Update frontend code to remove backward compatibility';
    RAISE NOTICE '';
    RAISE NOTICE 'Backup table "cover_templates_logo_backup" contains pre-migration data';
    RAISE NOTICE 'Keep this table until migration is fully verified';
END $$;
