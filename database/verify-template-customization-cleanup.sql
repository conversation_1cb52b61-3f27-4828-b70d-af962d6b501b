-- Verify Template Customization Cleanup
-- Run this to confirm that no template customization objects exist in your database

\echo 'Verifying template customization cleanup...'

-- Check if the table exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'user_template_customizations'
        ) 
        THEN '❌ Table still exists' 
        ELSE '✅ Table does not exist (good)'
    END AS table_status;

-- Check for any remaining indexes
SELECT 
    CASE 
        WHEN COUNT(*) = 0 
        THEN '✅ No orphaned indexes found' 
        ELSE '⚠️ ' || COUNT(*) || ' orphaned indexes found'
    END AS index_status
FROM pg_indexes 
WHERE tablename = 'user_template_customizations';

-- Check for any remaining policies
SELECT 
    CASE 
        WHEN COUNT(*) = 0 
        THEN '✅ No orphaned policies found' 
        ELSE '⚠️ ' || COUNT(*) || ' orphaned policies found'
    END AS policy_status
FROM pg_policies 
WHERE tablename = 'user_template_customizations';

-- Check for any remaining triggers
SELECT 
    CASE 
        WHEN COUNT(*) = 0 
        THEN '✅ No orphaned triggers found' 
        ELSE '⚠️ ' || COUNT(*) || ' orphaned triggers found'
    END AS trigger_status
FROM information_schema.triggers 
WHERE event_object_table = 'user_template_customizations';

\echo 'Verification completed!'