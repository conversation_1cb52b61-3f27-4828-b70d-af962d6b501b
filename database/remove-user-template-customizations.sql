-- Remove User Template Customizations Table and Related Objects
-- Run this script to completely remove template customization persistence from your database

\echo 'Removing user template customizations table and related objects...'

-- Drop the table and all dependent objects
DROP TABLE IF EXISTS public.user_template_customizations CASCADE;

-- Drop indexes (if they weren't automatically dropped with CASCADE)
DROP INDEX IF EXISTS idx_user_template_customizations_user_template;
DROP INDEX IF EXISTS idx_user_template_customizations_type;

-- Drop any remaining policies (only if table exists)
DO $$ 
BEGIN
    -- Check if table exists first
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_template_customizations') THEN
        -- Drop policies only if table exists
        EXECUTE 'DROP POLICY IF EXISTS "Users can view own template customizations" ON public.user_template_customizations';
        EXECUTE 'DROP POLICY IF EXISTS "Users can insert own template customizations" ON public.user_template_customizations';
        EXECUTE 'DROP POLICY IF EXISTS "Users can update own template customizations" ON public.user_template_customizations';
        EXECUTE 'DROP POLICY IF EXISTS "Users can delete own template customizations" ON public.user_template_customizations';
        
        -- Drop the trigger (only if table exists)
        EXECUTE 'DROP TRIGGER IF EXISTS update_user_template_customizations_updated_at ON public.user_template_customizations';
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        -- Table doesn't exist, which is fine
        NULL;
END
$$;

-- Verify cleanup
DO $$
DECLARE
    table_exists BOOLEAN;
    index_count INTEGER;
    policy_count INTEGER;
BEGIN
    -- Check if table still exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_template_customizations'
    ) INTO table_exists;
    
    -- Count any remaining indexes
    SELECT COUNT(*) FROM pg_indexes 
    WHERE tablename = 'user_template_customizations' 
    INTO index_count;
    
    -- Count any remaining policies
    SELECT COUNT(*) FROM pg_policies 
    WHERE tablename = 'user_template_customizations' 
    INTO policy_count;
    
    IF NOT table_exists AND index_count = 0 AND policy_count = 0 THEN
        RAISE NOTICE 'SUCCESS: user_template_customizations table never existed or has been completely removed';
    ELSIF NOT table_exists THEN
        RAISE NOTICE 'SUCCESS: user_template_customizations table does not exist (this is expected)';
        IF index_count > 0 THEN
            RAISE WARNING 'WARNING: % orphaned indexes still exist', index_count;
        END IF;
        IF policy_count > 0 THEN
            RAISE WARNING 'WARNING: % orphaned policies still exist', policy_count;
        END IF;
    END IF;
END
$$;

\echo 'User template customizations cleanup completed!'