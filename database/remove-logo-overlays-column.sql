-- REMOVE LOGO_OVERLAYS COLUMN AFTER SUCCESSFUL MIGRATION
-- This script should ONLY be run after:
-- 1. Running migrate-logo-overlays-to-unified.sql successfully
-- 2. Verifying all functionality works with unified structure
-- 3. Testing the application thoroughly

-- Final verification before column removal
DO $$
DECLARE
    remaining_old_logos INTEGER;
    total_new_logos INTEGER;
    conflict_count INTEGER;
    safe_to_proceed BOOLEAN := true;
BEGIN
    -- Check for remaining logo overlays in old structure
    SELECT COUNT(*) INTO remaining_old_logos
    FROM cover_templates 
    WHERE logo_overlays IS NOT NULL 
    AND jsonb_array_length(logo_overlays->'overlays') > 0;
    
    -- Count logo overlays in new structure
    SELECT COALESCE(SUM(
        (SELECT COUNT(*) FROM jsonb_array_elements(text_overlays->'overlays') as overlay
         WHERE overlay->>'type' = 'logo')
    ), 0) INTO total_new_logos
    FROM cover_templates;
    
    -- Check for any conflicts
    SELECT COUNT(*) INTO conflict_count
    FROM cover_templates
    WHERE logo_overlays IS NOT NULL 
    AND jsonb_array_length(logo_overlays->'overlays') > 0
    AND EXISTS (
        SELECT 1 FROM jsonb_array_elements(text_overlays->'overlays') as overlay
        WHERE overlay->>'type' = 'logo'
    );
    
    RAISE NOTICE '=== FINAL VERIFICATION BEFORE COLUMN REMOVAL ===';
    RAISE NOTICE 'Templates with old logo_overlays: %', remaining_old_logos;
    RAISE NOTICE 'Total logo overlays in unified structure: %', total_new_logos;
    RAISE NOTICE 'Conflict templates: %', conflict_count;
    
    IF remaining_old_logos > 0 THEN
        safe_to_proceed := false;
        RAISE WARNING 'UNSAFE: % templates still have logo data in old structure', remaining_old_logos;
    END IF;
    
    IF total_new_logos = 0 THEN
        RAISE WARNING 'WARNING: No logo overlays found in unified structure';
        RAISE NOTICE 'This might be expected if you have no logo templates, but please verify';
    END IF;
    
    IF conflict_count > 0 THEN
        safe_to_proceed := false;
        RAISE ERROR 'UNSAFE: % templates have conflicting logo structures', conflict_count;
    END IF;
    
    IF safe_to_proceed THEN
        RAISE NOTICE 'VERIFICATION PASSED: Safe to remove logo_overlays column';
    ELSE
        RAISE ERROR 'VERIFICATION FAILED: DO NOT proceed with column removal';
    END IF;
END $$;

-- Create final backup before column removal
CREATE TABLE IF NOT EXISTS cover_templates_final_backup_before_column_removal AS 
SELECT * FROM cover_templates;

RAISE NOTICE 'Final backup created: cover_templates_final_backup_before_column_removal';

-- Remove the logo_overlays column
ALTER TABLE public.cover_templates DROP COLUMN IF EXISTS logo_overlays;

-- Remove the index for logo_overlays
DROP INDEX IF EXISTS idx_cover_templates_logo_overlays;

-- Update any database functions that reference logo_overlays
-- Drop old logo overlay functions that are no longer needed
DROP FUNCTION IF EXISTS public.get_template_logo_overlays(TEXT);
DROP FUNCTION IF EXISTS public.template_has_logo_overlays(TEXT);
DROP FUNCTION IF EXISTS public.get_default_logo_overlay_for_category(TEXT);

-- Create new unified functions
CREATE OR REPLACE FUNCTION public.get_template_logo_overlays_unified(template_id TEXT)
RETURNS JSONB AS $$
DECLARE
    logo_overlays JSONB;
BEGIN
    SELECT jsonb_agg(overlay) INTO logo_overlays
    FROM (
        SELECT overlay
        FROM cover_templates,
             jsonb_array_elements(text_overlays->'overlays') as overlay
        WHERE id = template_id
        AND overlay->>'type' = 'logo'
    ) as logo_data;
    
    RETURN COALESCE(logo_overlays, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.template_has_logo_overlays_unified(template_id TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    has_logos BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM cover_templates,
             jsonb_array_elements(text_overlays->'overlays') as overlay
        WHERE id = template_id
        AND overlay->>'type' = 'logo'
    ) INTO has_logos;
    
    RETURN COALESCE(has_logos, false);
END;
$$ LANGUAGE plpgsql;

-- Verify column removal
DO $$
DECLARE
    column_exists BOOLEAN;
    index_exists BOOLEAN;
BEGIN
    -- Check if column still exists
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'cover_templates' 
        AND column_name = 'logo_overlays'
        AND table_schema = 'public'
    ) INTO column_exists;
    
    -- Check if index still exists
    SELECT EXISTS (
        SELECT 1 
        FROM pg_indexes 
        WHERE indexname = 'idx_cover_templates_logo_overlays'
        AND schemaname = 'public'
    ) INTO index_exists;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== COLUMN REMOVAL VERIFICATION ===';
    RAISE NOTICE 'logo_overlays column exists: %', column_exists;
    RAISE NOTICE 'logo_overlays index exists: %', index_exists;
    
    IF NOT column_exists AND NOT index_exists THEN
        RAISE NOTICE 'SUCCESS: logo_overlays column and index successfully removed';
    ELSE
        RAISE WARNING 'ISSUE: Column or index removal may have failed';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Migration to unified logo overlay system COMPLETE!';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Update frontend code to remove backward compatibility';
    RAISE NOTICE '2. Test application thoroughly';
    RAISE NOTICE '3. Remove backup tables when confident in migration';
END $$;
