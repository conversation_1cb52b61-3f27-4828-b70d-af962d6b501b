-- Extended Overlay Types Schema - Database Enhancement
-- Adds support for specialized contact information overlay types
-- Builds on existing user_profiles schema without breaking changes

-- NOTE: The user_profiles table already contains the required fields:
-- - full_name TEXT (for {{user_name}})
-- - organization TEXT (for {{company_name}}) 
-- - website TEXT (for {{website}})
-- - phone TEXT (for {{phone_number}})
-- - email TEXT (for {{email_address}})

-- Create validation functions for new overlay types

-- Function to validate email format
CREATE OR REPLACE FUNCTION public.validate_email_format(email_text TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Return true if email is null/empty (optional fields) or matches email pattern
    RETURN email_text IS NULL OR 
           email_text = '' OR 
           email_text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql;

-- Function to validate website URL format
CREATE OR REPLACE FUNCTION public.validate_website_format(website_text TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Return true if website is null/empty or matches URL pattern
    RETURN website_text IS NULL OR 
           website_text = '' OR 
           website_text ~* '^(https?://)?(www\.)?[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}(/.*)?$';
END;
$$ LANGUAGE plpgsql;

-- Function to validate phone number format (basic validation)
CREATE OR REPLACE FUNCTION public.validate_phone_format(phone_text TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Return true if phone is null/empty or contains only digits, spaces, +, -, (, )
    RETURN phone_text IS NULL OR 
           phone_text = '' OR 
           phone_text ~ '^[+]?[0-9() -]+$';
END;
$$ LANGUAGE plpgsql;

-- Function to format website URL (adds https:// if missing)
CREATE OR REPLACE FUNCTION public.format_website_url(website_text TEXT)
RETURNS TEXT AS $$
BEGIN
    IF website_text IS NULL OR website_text = '' THEN
        RETURN website_text;
    END IF;
    
    -- Add https:// if no protocol specified
    IF website_text !~* '^https?://' THEN
        RETURN 'https://' || website_text;
    END IF;
    
    RETURN website_text;
END;
$$ LANGUAGE plpgsql;

-- Function to format phone number (international format)
CREATE OR REPLACE FUNCTION public.format_phone_number(phone_text TEXT, format_type TEXT DEFAULT 'international')
RETURNS TEXT AS $$
DECLARE
    cleaned_phone TEXT;
    formatted_phone TEXT;
BEGIN
    IF phone_text IS NULL OR phone_text = '' THEN
        RETURN phone_text;
    END IF;
    
    -- Remove all non-digit characters
    cleaned_phone := regexp_replace(phone_text, '[^0-9]', '', 'g');
    
    -- Format based on type
    CASE format_type
        WHEN 'international' THEN
            -- Format as +1 (XXX) XXX-XXXX for 10-digit US numbers
            IF length(cleaned_phone) = 10 THEN
                formatted_phone := '+1 (' || substring(cleaned_phone, 1, 3) || ') ' || 
                                 substring(cleaned_phone, 4, 3) || '-' || 
                                 substring(cleaned_phone, 7, 4);
            ELSIF length(cleaned_phone) = 11 AND substring(cleaned_phone, 1, 1) = '1' THEN
                formatted_phone := '+' || substring(cleaned_phone, 1, 1) || ' (' || 
                                 substring(cleaned_phone, 2, 3) || ') ' || 
                                 substring(cleaned_phone, 5, 3) || '-' || 
                                 substring(cleaned_phone, 8, 4);
            ELSE
                formatted_phone := phone_text; -- Return original if can't format
            END IF;
        WHEN 'national' THEN
            -- Format as (XXX) XXX-XXXX
            IF length(cleaned_phone) >= 10 THEN
                formatted_phone := '(' || substring(cleaned_phone, -10, 3) || ') ' || 
                                 substring(cleaned_phone, -7, 3) || '-' || 
                                 substring(cleaned_phone, -4, 4);
            ELSE
                formatted_phone := phone_text;
            END IF;
        ELSE
            formatted_phone := phone_text;
    END CASE;
    
    RETURN formatted_phone;
END;
$$ LANGUAGE plpgsql;

-- Function to get contact overlay defaults for template creation
CREATE OR REPLACE FUNCTION public.get_contact_overlay_defaults(overlay_type TEXT)
RETURNS JSONB AS $$
BEGIN
    CASE overlay_type
        WHEN 'company' THEN
            RETURN jsonb_build_object(
                'validation', jsonb_build_object(
                    'maxLength', 100,
                    'required', false,
                    'autoFormat', true
                )
            );
        WHEN 'website' THEN
            RETURN jsonb_build_object(
                'validation', jsonb_build_object(
                    'pattern', 'url',
                    'autoFormat', true,
                    'showPrefix', false,
                    'required', false
                )
            );
        WHEN 'email' THEN
            RETURN jsonb_build_object(
                'validation', jsonb_build_object(
                    'pattern', 'email',
                    'required', false,
                    'obfuscate', false
                )
            );
        WHEN 'phone' THEN
            RETURN jsonb_build_object(
                'validation', jsonb_build_object(
                    'format', 'international',
                    'country', 'auto',
                    'required', false,
                    'displayStyle', 'formatted'
                )
            );
        ELSE
            RETURN '{}'::jsonb;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Function to validate template overlay configuration
CREATE OR REPLACE FUNCTION public.validate_template_overlay(overlay_data JSONB)
RETURNS BOOLEAN AS $$
DECLARE
    overlay_type TEXT;
    validation_config JSONB;
BEGIN
    -- Get overlay type
    overlay_type := overlay_data->>'type';
    
    -- If not a specialized contact type, return true (existing validation)
    IF overlay_type NOT IN ('company', 'website', 'email', 'phone') THEN
        RETURN TRUE;
    END IF;
    
    -- Check required fields exist
    IF NOT (overlay_data ? 'id' AND overlay_data ? 'placeholder' AND overlay_data ? 'position') THEN
        RETURN FALSE;
    END IF;
    
    -- Validate position object
    IF NOT (overlay_data->'position' ? 'x' AND 
            overlay_data->'position' ? 'y' AND 
            overlay_data->'position' ? 'width' AND 
            overlay_data->'position' ? 'height') THEN
        RETURN FALSE;
    END IF;
    
    -- Type-specific validation
    validation_config := overlay_data->'validation';
    
    CASE overlay_type
        WHEN 'website' THEN
            -- Validate website-specific configurations
            IF validation_config ? 'pattern' AND validation_config->>'pattern' != 'url' THEN
                RETURN FALSE;
            END IF;
        WHEN 'email' THEN
            -- Validate email-specific configurations  
            IF validation_config ? 'pattern' AND validation_config->>'pattern' != 'email' THEN
                RETURN FALSE;
            END IF;
        WHEN 'phone' THEN
            -- Validate phone-specific configurations
            IF validation_config ? 'format' AND 
               validation_config->>'format' NOT IN ('international', 'national', 'local') THEN
                RETURN FALSE;
            END IF;
        -- Company type has no special validation requirements
    END CASE;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add check constraint to cover_templates to validate extended overlay types
-- (Only if you want to enforce validation at database level)
-- ALTER TABLE public.cover_templates 
-- ADD CONSTRAINT check_valid_overlay_types 
-- CHECK (
--     CASE 
--         WHEN text_overlays->'overlays' IS NOT NULL THEN
--             (SELECT bool_and(public.validate_template_overlay(overlay))
--              FROM jsonb_array_elements(text_overlays->'overlays') AS overlay)
--         ELSE TRUE
--     END
-- );

-- Create indexes for efficient querying of overlay types
CREATE INDEX IF NOT EXISTS idx_cover_templates_overlay_types 
ON public.cover_templates USING GIN (
    (
        SELECT jsonb_agg(overlay->>'type') 
        FROM jsonb_array_elements(text_overlays->'overlays') AS overlay
    )
);

-- Create a view for templates with contact overlays
CREATE OR REPLACE VIEW public.templates_with_contact_overlays AS
SELECT 
    id,
    name,
    category,
    description,
    tags,
    (
        SELECT jsonb_agg(overlay->>'type') 
        FROM jsonb_array_elements(text_overlays->'overlays') AS overlay
        WHERE overlay->>'type' IN ('company', 'website', 'email', 'phone')
    ) AS contact_overlay_types,
    (
        SELECT COUNT(*) 
        FROM jsonb_array_elements(text_overlays->'overlays') AS overlay
        WHERE overlay->>'type' IN ('company', 'website', 'email', 'phone')
    ) AS contact_overlay_count,
    created_at,
    updated_at
FROM public.cover_templates
WHERE text_overlays->'overlays' @> '[{"type": "company"}]' OR
      text_overlays->'overlays' @> '[{"type": "website"}]' OR
      text_overlays->'overlays' @> '[{"type": "email"}]' OR
      text_overlays->'overlays' @> '[{"type": "phone"}]';

-- Grant appropriate permissions
GRANT EXECUTE ON FUNCTION public.validate_email_format TO authenticated;
GRANT EXECUTE ON FUNCTION public.validate_website_format TO authenticated;
GRANT EXECUTE ON FUNCTION public.validate_phone_format TO authenticated;
GRANT EXECUTE ON FUNCTION public.format_website_url TO authenticated;
GRANT EXECUTE ON FUNCTION public.format_phone_number TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_contact_overlay_defaults TO authenticated;
GRANT EXECUTE ON FUNCTION public.validate_template_overlay TO authenticated;

GRANT SELECT ON public.templates_with_contact_overlays TO authenticated;

-- Example usage and testing queries

-- Test validation functions
-- SELECT public.validate_email_format('<EMAIL>'); -- Should return true
-- SELECT public.validate_email_format('invalid-email'); -- Should return false
-- SELECT public.validate_website_format('example.com'); -- Should return true
-- SELECT public.format_website_url('example.com'); -- Should return 'https://example.com'
-- SELECT public.format_phone_number('1234567890', 'international'); -- Should return '+****************'

-- Find templates with contact overlays
-- SELECT * FROM public.templates_with_contact_overlays;

-- Query templates by overlay type
-- SELECT id, name, 
--        (SELECT jsonb_agg(overlay) 
--         FROM jsonb_array_elements(text_overlays->'overlays') AS overlay
--         WHERE overlay->>'type' = 'email') as email_overlays
-- FROM public.cover_templates
-- WHERE text_overlays->'overlays' @> '[{"type": "email"}]';

COMMENT ON FUNCTION public.validate_email_format IS 'Validates email address format for overlay validation';
COMMENT ON FUNCTION public.validate_website_format IS 'Validates website URL format for overlay validation';
COMMENT ON FUNCTION public.validate_phone_format IS 'Validates phone number format for overlay validation';
COMMENT ON FUNCTION public.format_website_url IS 'Formats website URL by adding protocol if missing';
COMMENT ON FUNCTION public.format_phone_number IS 'Formats phone number in specified format (international, national, local)';
COMMENT ON FUNCTION public.get_contact_overlay_defaults IS 'Returns default validation configuration for contact overlay types';
COMMENT ON FUNCTION public.validate_template_overlay IS 'Validates template overlay configuration for extended types';
COMMENT ON VIEW public.templates_with_contact_overlays IS 'View showing templates that contain contact information overlays';
